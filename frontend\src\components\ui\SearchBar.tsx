import React, { useState, useRef, useEffect } from 'react';
import { ICONS } from '../../constants';

interface SearchResult {
  id: string;
  title: string;
  type: 'demand' | 'citizen' | 'event' | 'document';
  description?: string;
  url: string;
}

interface SearchBarProps {
  onSearch?: (query: string) => void;
  placeholder?: string;
  className?: string;
}

export const SearchBar: React.FC<SearchBarProps> = ({ 
  onSearch, 
  placeholder = "Buscar...", 
  className = "" 
}) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Mock search function - replace with actual search implementation
  const performSearch = async (searchQuery: string): Promise<SearchResult[]> => {
    if (!searchQuery.trim()) return [];
    
    setIsLoading(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const mockResults: SearchResult[] = [
      {
        id: '1',
        title: `Demanda sobre ${searchQuery}`,
        type: 'demand' as const,
        description: 'Solicitação de melhoria na infraestrutura',
        url: '/demands/1'
      },
      {
        id: '2',
        title: `Cidadão ${searchQuery}`,
        type: 'citizen' as const,
        description: 'João Silva - Morador do Centro',
        url: '/citizens/2'
      },
      {
        id: '3',
        title: `Evento ${searchQuery}`,
        type: 'event' as const,
        description: 'Reunião pública agendada',
        url: '/agenda'
      }
    ].filter(result => 
      result.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      result.description?.toLowerCase().includes(searchQuery.toLowerCase())
    );
    
    setIsLoading(false);
    return mockResults;
  };

  const handleInputChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);
    
    if (value.trim()) {
      const searchResults = await performSearch(value);
      setResults(searchResults);
      setIsOpen(true);
    } else {
      setResults([]);
      setIsOpen(false);
    }
    
    onSearch?.(value);
  };

  const handleResultClick = (result: SearchResult) => {
    setQuery(result.title);
    setIsOpen(false);
    // Navigate to result URL
    window.location.href = result.url;
  };

  const getTypeIcon = (type: SearchResult['type']) => {
    switch (type) {
      case 'demand':
        return ICONS.DEMANDS;
      case 'citizen':
        return ICONS.CITIZENS;
      case 'event':
        return ICONS.CALENDAR;
      case 'document':
        return ICONS.DOCUMENTS;
      default:
        return ICONS.DASHBOARD;
    }
  };

  const getTypeLabel = (type: SearchResult['type']) => {
    switch (type) {
      case 'demand':
        return 'Demanda';
      case 'citizen':
        return 'Cidadão';
      case 'event':
        return 'Evento';
      case 'document':
        return 'Documento';
      default:
        return 'Item';
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsOpen(false);
      inputRef.current?.blur();
    }
  };

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg 
            className="h-5 w-5 text-gray-400 dark:text-neutral-DEFAULT" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path 
              strokeLinecap="round" 
              strokeLinejoin="round" 
              strokeWidth={2} 
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" 
            />
          </svg>
        </div>
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-neutral-medium rounded-md leading-5 bg-white dark:bg-neutral-dark text-gray-900 dark:text-neutral-light placeholder-gray-500 dark:placeholder-neutral-DEFAULT focus:outline-none focus:placeholder-gray-400 dark:focus:placeholder-neutral-medium focus:ring-1 focus:ring-primary focus:border-primary dark:focus:ring-primary-light dark:focus:border-primary-light sm:text-sm"
        />
        {isLoading && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
          </div>
        )}
      </div>

      {/* Search Results Dropdown */}
      {isOpen && (results.length > 0 || isLoading) && (
        <div className="absolute z-50 mt-1 w-full bg-white dark:bg-neutral-dark shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black dark:ring-neutral-medium ring-opacity-5 overflow-auto focus:outline-none sm:text-sm">
          {isLoading ? (
            <div className="px-4 py-2 text-gray-500 dark:text-neutral-DEFAULT">
              Buscando...
            </div>
          ) : (
            results.map((result) => (
              <div
                key={result.id}
                onClick={() => handleResultClick(result)}
                className="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-gray-100 dark:hover:bg-neutral-darker"
              >
                <div className="flex items-center">
                  <span className="mr-3 text-gray-400 dark:text-neutral-DEFAULT">
                    {React.cloneElement(getTypeIcon(result.type), { className: 'w-4 h-4' })}
                  </span>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-gray-900 dark:text-neutral-light">
                        {result.title}
                      </span>
                      <span className="text-xs text-gray-500 dark:text-neutral-DEFAULT bg-gray-100 dark:bg-neutral-darker px-2 py-1 rounded">
                        {getTypeLabel(result.type)}
                      </span>
                    </div>
                    {result.description && (
                      <p className="text-sm text-gray-500 dark:text-neutral-DEFAULT truncate">
                        {result.description}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      )}

      {/* No Results */}
      {isOpen && !isLoading && results.length === 0 && query.trim() && (
        <div className="absolute z-50 mt-1 w-full bg-white dark:bg-neutral-dark shadow-lg rounded-md py-1 text-base ring-1 ring-black dark:ring-neutral-medium ring-opacity-5">
          <div className="px-4 py-2 text-gray-500 dark:text-neutral-DEFAULT">
            Nenhum resultado encontrado para "{query}"
          </div>
        </div>
      )}
    </div>
  );
};