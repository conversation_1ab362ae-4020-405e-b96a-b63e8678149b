import { format, parse, startOfWeek, getDay } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { AgendaEvent } from '../types';

// Configuração de localização para o calendário
export const locales = {
  'pt-BR': ptBR,
};

// Configuração de formatos de data
export const formats = {
  dateFormat: 'dd',
  dayFormat: (date: Date, culture?: string, localizer?: any) =>
    localizer.format(date, 'dd', culture),
  dayRangeHeaderFormat: ({ start, end }: { start: Date; end: Date }, culture?: string, localizer?: any) =>
    localizer.format(start, 'MMMM yyyy', culture),
  dayHeaderFormat: (date: Date, culture?: string, localizer?: any) =>
    localizer.format(date, 'cccc, dd/MM', culture),
  monthHeaderFormat: (date: Date, culture?: string, localizer?: any) =>
    localizer.format(date, 'MMMM yyyy', culture),
  agendaHeaderFormat: ({ start, end }: { start: Date; end: Date }, culture?: string, localizer?: any) =>
    `${localizer.format(start, 'dd/MM/yyyy', culture)} — ${localizer.format(end, 'dd/MM/yyyy', culture)}`,
  agendaDateFormat: (date: Date, culture?: string, localizer?: any) =>
    localizer.format(date, 'cccc, dd/MM', culture),
  agendaTimeFormat: (date: Date, culture?: string, localizer?: any) =>
    localizer.format(date, 'HH:mm', culture),
  agendaTimeRangeFormat: ({ start, end }: { start: Date; end: Date }, culture?: string, localizer?: any) =>
    `${localizer.format(start, 'HH:mm', culture)} — ${localizer.format(end, 'HH:mm', culture)}`,
  timeGutterFormat: (date: Date, culture?: string, localizer?: any) =>
    localizer.format(date, 'HH:mm', culture),
};

// Configuração de mensagens em português
export const messages = {
  allDay: 'Dia todo',
  previous: 'Anterior',
  next: 'Próximo',
  today: 'Hoje',
  month: 'Mês',
  week: 'Semana',
  day: 'Dia',
  agenda: 'Agenda',
  date: 'Data',
  time: 'Hora',
  event: 'Evento',
  noEventsInRange: 'Não há eventos neste período.',
  showMore: (total: number) => `+ Ver mais (${total})`,
};

// Converter AgendaEvent para formato do React Big Calendar
export const convertAgendaEventToCalendarEvent = (event: AgendaEvent) => {
  return {
    id: event.id,
    title: event.title,
    start: new Date(event.start),
    end: new Date(event.end),
    allDay: event.isAllDay || false,
    resource: {
      description: event.description,
      location: event.location,
      attendees: event.attendees,
      createdBy: event.createdBy,
      createdAt: event.createdAt,
      updatedAt: event.updatedAt,
    },
  };
};

// Converter evento do calendário para AgendaEvent
export const convertCalendarEventToAgendaEvent = (calendarEvent: any): Partial<AgendaEvent> => {
  return {
    id: calendarEvent.id,
    title: calendarEvent.title,
    start: calendarEvent.start.toISOString(),
    end: calendarEvent.end.toISOString(),
    isAllDay: calendarEvent.allDay,
    description: calendarEvent.resource?.description,
    location: calendarEvent.resource?.location,
    attendees: calendarEvent.resource?.attendees,
  };
};

// Função para criar um novo evento com horários padrão
export const createNewEvent = (slotInfo: any): Partial<AgendaEvent> => {
  const start = slotInfo.start;
  const end = slotInfo.end || new Date(start.getTime() + 60 * 60 * 1000); // 1 hora depois

  return {
    title: '',
    start: start.toISOString(),
    end: end.toISOString(),
    isAllDay: slotInfo.slots?.length > 1, // Se selecionou múltiplos slots, é dia todo
    description: '',
    location: '',
    attendees: [],
  };
};

// Função para validar se um evento é válido
export const validateEvent = (event: Partial<AgendaEvent>): string | null => {
  if (!event.title?.trim()) {
    return 'Título é obrigatório';
  }
  
  if (!event.start || !event.end) {
    return 'Data de início e fim são obrigatórias';
  }
  
  const startDate = new Date(event.start);
  const endDate = new Date(event.end);
  
  if (startDate >= endDate) {
    return 'A data de término deve ser posterior à data de início';
  }
  
  return null;
};

// Função para formatar data para input datetime-local
export const formatDateTimeLocal = (date: Date | string): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return format(dateObj, "yyyy-MM-dd'T'HH:mm");
};

// Função para obter a cor do evento baseada em critérios
export const getEventStyle = (event: any) => {
  const now = new Date();
  const eventStart = new Date(event.start);
  const eventEnd = new Date(event.end);
  
  let backgroundColor = '#3174ad'; // Cor padrão (azul)
  
  // Evento passado
  if (eventEnd < now) {
    backgroundColor = '#6b7280'; // Cinza
  }
  // Evento acontecendo agora
  else if (eventStart <= now && eventEnd >= now) {
    backgroundColor = '#059669'; // Verde
  }
  // Evento futuro próximo (próximas 24 horas)
  else if (eventStart <= new Date(now.getTime() + 24 * 60 * 60 * 1000)) {
    backgroundColor = '#dc2626'; // Vermelho
  }
  
  return {
    style: {
      backgroundColor,
      borderRadius: '4px',
      opacity: 0.8,
      color: 'white',
      border: '0px',
      display: 'block',
    },
  };
};
