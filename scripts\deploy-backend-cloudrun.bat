@echo off
echo ========================================
echo   DEPLOY BACKEND - GOOGLE CLOUD RUN
echo ========================================
echo.

echo [1/6] Verificando Google Cloud CLI...
gcloud version >nul 2>&1
if %errorlevel% neq 0 (
    echo Google Cloud CLI nao encontrado!
    echo Instale em: https://cloud.google.com/sdk/docs/install
    pause
    exit /b 1
)

echo Google Cloud CLI encontrado!
echo.

echo [2/6] Configurando projeto...
gcloud config set project promandato-9a4cf

echo.
echo [3/6] Criando Dockerfile para o backend...
cd backend

echo FROM node:18-alpine > Dockerfile
echo WORKDIR /app >> Dockerfile
echo COPY package*.json ./ >> Dockerfile
echo RUN npm ci --only=production >> Dockerfile
echo COPY . . >> Dockerfile
echo RUN npm run build 2^>nul ^|^| echo "No build script" >> Dockerfile
echo EXPOSE 3002 >> Dockerfile
echo CMD ["npm", "start"] >> Dockerfile

echo.
echo [4/6] Criando .dockerignore...
echo node_modules > .dockerignore
echo npm-debug.log >> .dockerignore
echo .git >> .dockerignore
echo .gitignore >> .dockerignore
echo README.md >> .dockerignore
echo .env >> .dockerignore
echo coverage >> .dockerignore
echo .nyc_output >> .dockerignore

echo.
echo [5/6] Fazendo build e deploy...
gcloud builds submit --tag gcr.io/promandato-9a4cf/backend ./backend
gcloud run deploy backend ^
  --image gcr.io/promandato-9a4cf/backend ^
  --platform managed ^
  --region southamerica-east1 ^
  --allow-unauthenticated ^
  --port 8080 ^
  --memory 1Gi ^
  --cpu 1 ^
  --min-instances 0 ^
  --max-instances 10

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo       BACKEND DEPLOYADO COM SUCESSO!
    echo ========================================
    echo.
    echo URL do Backend: 
    gcloud run services describe promandato-backend --region=us-central1 --format="value(status.url)"
    echo.
    echo Proximos passos:
    echo 1. Configurar dominio customizado (opcional)
    echo 2. Atualizar URLs na landing page
    echo 3. Configurar variaveis de ambiente
    echo.
) else (
    echo.
    echo ERRO: Deploy falhou!
    echo Verifique os logs acima.
)

echo.
echo [6/6] Limpando arquivos temporarios...
del Dockerfile 2>nul
del .dockerignore 2>nul

cd ..
pause

