import React, { useState, useEffect } from 'react';
import { 
  BarChart3, 
  Users, 
  Building2, 
  Globe, 
  Settings,
  TrendingUp,
  DollarSign,
  Activity,
  Bell,
  Download,
  RefreshCw
} from 'lucide-react';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import LandingPageIntegration from '../components/dashboard/LandingPageIntegration';
import OrganizationsManagement from '../components/dashboard/OrganizationsManagement';
import { useAuth } from '../hooks/useAuth';
import { UserRole } from '../types';

interface AdminMetrics {
  totalUsers: number;
  totalOrganizations: number;
  monthlyRevenue: number;
  conversionRate: number;
  systemHealth: 'healthy' | 'warning' | 'critical';
  recentActivity: Array<{
    id: string;
    type: 'user_signup' | 'organization_created' | 'payment_received' | 'trial_started';
    description: string;
    timestamp: string;
  }>;
}

const AdminDashboardPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [activeTab, setActiveTab] = useState<'overview' | 'landing' | 'organizations' | 'users' | 'analytics' | 'settings'>('overview');
  const [metrics, setMetrics] = useState<AdminMetrics>({
    totalUsers: 1247,
    totalOrganizations: 156,
    monthlyRevenue: 89750,
    conversionRate: 12.5,
    systemHealth: 'healthy',
    recentActivity: [
      {
        id: '1',
        type: 'organization_created',
        description: 'Nova organização: Prefeitura de Santos',
        timestamp: '2024-01-15T14:30:00Z'
      },
      {
        id: '2',
        type: 'trial_started',
        description: 'Trial iniciado: Câmara Municipal de Florianópolis',
        timestamp: '2024-01-15T13:15:00Z'
      },
      {
        id: '3',
        type: 'payment_received',
        description: 'Pagamento recebido: R$ 449,00 - Assembleia Legislativa RS',
        timestamp: '2024-01-15T11:45:00Z'
      }
    ]
  });

  const [isLoading, setIsLoading] = useState(false);

  // Função para atualizar métricas
  const refreshMetrics = async () => {
    setIsLoading(true);
    try {
      // Simular chamada à API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Aqui você faria a chamada real para a API
      // const response = await fetch('/api/admin/metrics');
      // const data = await response.json();
      // setMetrics(data);
      
    } catch (error) {
      console.error('Erro ao atualizar métricas:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Verificar se o usuário tem permissão de admin
  const isAdmin = currentUser?.role === UserRole.ADMIN;

  if (!isAdmin) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="p-8 text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Acesso Negado</h2>
          <p className="text-gray-600">Você não tem permissão para acessar o dashboard administrativo.</p>
        </Card>
      </div>
    );
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'user_signup':
        return <Users className="h-4 w-4 text-blue-600" />;
      case 'organization_created':
        return <Building2 className="h-4 w-4 text-green-600" />;
      case 'payment_received':
        return <DollarSign className="h-4 w-4 text-yellow-600" />;
      case 'trial_started':
        return <Activity className="h-4 w-4 text-purple-600" />;
      default:
        return <Bell className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Dashboard Administrativo</h1>
              <p className="text-gray-600">Sistema de Gestão Promandato</p>
            </div>
            <div className="flex items-center space-x-4">
              <Button 
                variant="outline" 
                size="sm"
                onClick={refreshMetrics}
                disabled={isLoading}
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Atualizar
              </Button>
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Relatório
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8">
            {[
              { id: 'overview', label: 'Visão Geral', icon: BarChart3 },
              { id: 'landing', label: 'Landing Page', icon: Globe },
              { id: 'organizations', label: 'Organizações', icon: Building2 },
              { id: 'users', label: 'Usuários', icon: Users },
              { id: 'analytics', label: 'Analytics', icon: TrendingUp },
              { id: 'settings', label: 'Configurações', icon: Settings }
            ].map((tab) => (
              <button
                key={tab.id}
                type="button"
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4 mr-2" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Métricas Principais */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Usuários Totais</p>
                    <p className="text-2xl font-bold text-gray-900">{metrics.totalUsers.toLocaleString()}</p>
                    <p className="text-sm text-green-600">+12% este mês</p>
                  </div>
                  <div className="p-3 bg-blue-100 rounded-full">
                    <Users className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
              </Card>

              <Card className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Organizações</p>
                    <p className="text-2xl font-bold text-gray-900">{metrics.totalOrganizations}</p>
                    <p className="text-sm text-green-600">+8% este mês</p>
                  </div>
                  <div className="p-3 bg-green-100 rounded-full">
                    <Building2 className="h-6 w-6 text-green-600" />
                  </div>
                </div>
              </Card>

              <Card className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Receita Mensal</p>
                    <p className="text-2xl font-bold text-gray-900">{formatCurrency(metrics.monthlyRevenue)}</p>
                    <p className="text-sm text-green-600">+15% este mês</p>
                  </div>
                  <div className="p-3 bg-yellow-100 rounded-full">
                    <DollarSign className="h-6 w-6 text-yellow-600" />
                  </div>
                </div>
              </Card>

              <Card className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Taxa de Conversão</p>
                    <p className="text-2xl font-bold text-gray-900">{metrics.conversionRate}%</p>
                    <p className="text-sm text-green-600">+2.3% este mês</p>
                  </div>
                  <div className="p-3 bg-purple-100 rounded-full">
                    <TrendingUp className="h-6 w-6 text-purple-600" />
                  </div>
                </div>
              </Card>
            </div>

            {/* Status do Sistema */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Activity className="h-5 w-5 mr-2" />
                Status do Sistema
              </h3>
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`w-3 h-3 rounded-full mr-3 ${
                    metrics.systemHealth === 'healthy' ? 'bg-green-500' :
                    metrics.systemHealth === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                  }`}></div>
                  <span className={`font-medium ${
                    metrics.systemHealth === 'healthy' ? 'text-green-600' :
                    metrics.systemHealth === 'warning' ? 'text-yellow-600' : 'text-red-600'
                  }`}>
                    {metrics.systemHealth === 'healthy' ? 'Sistema Operacional' :
                     metrics.systemHealth === 'warning' ? 'Atenção Necessária' : 'Problemas Críticos'}
                  </span>
                </div>
                <div className="text-sm text-gray-600">
                  Última verificação: {new Date().toLocaleTimeString('pt-BR')}
                </div>
              </div>
            </Card>

            {/* Atividade Recente */}
            <Card className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Bell className="h-5 w-5 mr-2" />
                Atividade Recente
              </h3>
              <div className="space-y-4">
                {metrics.recentActivity.map((activity) => (
                  <div key={activity.id} className="flex items-center space-x-3">
                    <div className="p-2 bg-gray-100 rounded-full">
                      {getActivityIcon(activity.type)}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">{activity.description}</p>
                      <p className="text-xs text-gray-500">
                        {new Date(activity.timestamp).toLocaleString('pt-BR')}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </div>
        )}

        {activeTab === 'landing' && <LandingPageIntegration />}
        {activeTab === 'organizations' && <OrganizationsManagement />}
        {activeTab === 'users' && (
          <div className="text-center py-12">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Gestão de Usuários</h3>
            <p className="text-gray-600">Funcionalidade de gestão de usuários em desenvolvimento.</p>
            <p className="text-sm text-gray-500 mt-2">
              Para gerenciar usuários, acesse o dashboard administrativo em{' '}
              <a href="http://localhost:3002" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                http://localhost:3002
              </a>
            </p>
          </div>
        )}

        {activeTab === 'analytics' && (
          <div className="text-center py-12">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Analytics Avançados</h3>
            <p className="text-gray-600">Relatórios detalhados e métricas avançadas em desenvolvimento.</p>
          </div>
        )}

        {activeTab === 'settings' && (
          <div className="text-center py-12">
            <h3 className="text-lg font-medium text-gray-900 mb-2">Configurações do Sistema</h3>
            <p className="text-gray-600">Configurações avançadas do sistema em desenvolvimento.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminDashboardPage;
