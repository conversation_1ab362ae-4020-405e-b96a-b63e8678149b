// Configurações e constantes para o serviço de IA

export const AI_FEATURES = {
  TEXT_ANALYSIS: 'text-analysis',
  AUTO_CATEGORIZATION: 'auto-categorization',
  PREDICTIVE_ANALYTICS: 'predictive-analytics',
  SMART_RESPONSES: 'smart-responses'
} as const;

export const AI_FEATURE_DESCRIPTIONS = {
  [AI_FEATURES.TEXT_ANALYSIS]: {
    name: 'Análise de Texto',
    description: 'Análise automática de sentimentos, emoções e urgência em demandas',
    category: 'Análise',
    benefits: [
      'Identificação automática de sentimentos',
      'Detecção de urgência',
      'Extração de palavras-chave',
      'Análise emocional avançada'
    ],
    useCases: [
      'Priorização automática de demandas',
      'Identificação de casos urgentes',
      'Análise de satisfação do cidadão'
    ]
  },
  [AI_FEATURES.AUTO_CATEGORIZATION]: {
    name: 'Categorização Automática',
    description: 'Categorização inteligente de demandas por departamento e tipo',
    category: 'Automação',
    benefits: [
      'Redução do tempo de triagem',
      'Maior precisão na categorização',
      'Sugestão de departamentos responsáveis',
      'Estimativa de tempo de resolução'
    ],
    useCases: [
      'Roteamento automático de demandas',
      'Distribuição de carga de trabalho',
      'Planejamento de recursos'
    ]
  },
  [AI_FEATURES.PREDICTIVE_ANALYTICS]: {
    name: 'Análise Preditiva',
    description: 'Previsões baseadas em dados históricos e tendências',
    category: 'Predição',
    benefits: [
      'Previsão de volume de demandas',
      'Identificação de tendências',
      'Detecção de anomalias',
      'Recomendações estratégicas'
    ],
    useCases: [
      'Planejamento de recursos',
      'Prevenção de problemas',
      'Otimização de processos'
    ]
  },
  [AI_FEATURES.SMART_RESPONSES]: {
    name: 'Respostas Inteligentes',
    description: 'Sugestões automáticas de respostas personalizadas',
    category: 'Automação',
    benefits: [
      'Respostas mais rápidas',
      'Consistência na comunicação',
      'Personalização automática',
      'Templates inteligentes'
    ],
    useCases: [
      'Atendimento mais eficiente',
      'Padronização de respostas',
      'Melhoria na satisfação'
    ]
  }
} as const;

export const PLAN_AI_FEATURES = {
  BASIC: [],
  STANDARD: [AI_FEATURES.TEXT_ANALYSIS],
  PROFESSIONAL: [
    AI_FEATURES.TEXT_ANALYSIS,
    AI_FEATURES.AUTO_CATEGORIZATION,
    AI_FEATURES.PREDICTIVE_ANALYTICS,
    AI_FEATURES.SMART_RESPONSES
  ]
} as const;

export const AI_USAGE_LIMITS = {
  [AI_FEATURES.TEXT_ANALYSIS]: {
    STANDARD: 1000, // 1000 análises por mês
    PROFESSIONAL: -1 // Ilimitado
  },
  [AI_FEATURES.AUTO_CATEGORIZATION]: {
    PROFESSIONAL: -1 // Ilimitado
  },
  [AI_FEATURES.PREDICTIVE_ANALYTICS]: {
    PROFESSIONAL: 500 // 500 análises por mês
  },
  [AI_FEATURES.SMART_RESPONSES]: {
    PROFESSIONAL: -1 // Ilimitado
  }
} as const;

export const AI_PROMPTS = {
  TEXT_ANALYSIS: `
Analise o seguinte texto de uma demanda pública brasileira e retorne um JSON estruturado:

Texto: "{text}"

Retorne um JSON com:
- sentiment: "positive" | "negative" | "neutral"
- confidence: número entre 0 e 1
- keywords: array de palavras-chave relevantes
- emotions: objeto com valores 0-1 para joy, anger, fear, sadness, surprise, disgust, trust, anticipation
- urgency: "low" | "medium" | "high"
- complexity: "simple" | "moderate" | "complex"
- language: código do idioma
- topics: array de tópicos identificados
- entities: array de entidades com text, type e confidence

Considere o contexto de administração pública brasileira.
`,

  CATEGORIZATION: `
Categorize a seguinte demanda pública brasileira:

Texto: "{text}"
Contexto: {context}

Categorias disponíveis:
- Infraestrutura (Pavimentação, Iluminação, Sinalização, Drenagem)
- Saúde (Atendimento, Medicamentos, Equipamentos, Estrutura)
- Educação (Ensino, Infraestrutura, Transporte, Merenda)
- Segurança (Policiamento, Iluminação, Equipamentos)
- Meio Ambiente (Limpeza, Arborização, Poluição, Reciclagem)
- Transporte (Ônibus, Ciclovia, Trânsito, Acessibilidade)
- Assistência Social (Programas, Atendimento, Documentação)

Retorne JSON com category, subcategory, confidence, suggestedTags, department, priority, estimatedResolutionTime, requiredSkills, relatedCategories.
`,

  PREDICTIVE_ANALYSIS: `
Faça uma análise preditiva da seguinte demanda:

Texto: "{text}"
Histórico: {context}

Analise padrões típicos de demandas públicas brasileiras e retorne JSON com:
- resolutionTime: tempo estimado em horas
- priority: prioridade calculada
- similarCases: casos similares encontrados
- trends: tendências identificadas
- riskFactors: fatores de risco
- recommendations: recomendações de ação

Base-se em dados históricos típicos de administração pública.
`,

  SMART_RESPONSE: `
Gere respostas inteligentes para a seguinte demanda pública:

Texto: "{text}"
Contexto do cidadão: {context}

Gere 3 opções de resposta com tons diferentes:
1. Formal/oficial
2. Amigável/acolhedora  
3. Empática/compreensiva

Para cada resposta, inclua:
- response: texto da resposta
- confidence: confiança na adequação
- tone: tom utilizado
- category: categoria da resposta
- personalization: dados de personalização
- templates: templates aplicáveis
- followUpSuggestions: sugestões de acompanhamento
- estimatedSatisfaction: satisfação estimada

As respostas devem seguir padrões de comunicação oficial brasileira.
`
} as const;

export const AI_ERROR_MESSAGES = {
  FEATURE_NOT_AVAILABLE: 'Esta funcionalidade não está disponível no seu plano atual',
  USAGE_LIMIT_EXCEEDED: 'Limite de uso mensal atingido para esta funcionalidade',
  API_ERROR: 'Erro temporário no serviço de IA. Tente novamente em alguns minutos',
  INVALID_REQUEST: 'Dados da requisição inválidos',
  NETWORK_ERROR: 'Erro de conexão. Verifique sua internet',
  TIMEOUT_ERROR: 'Tempo limite excedido. Tente novamente',
  AUTHENTICATION_ERROR: 'Erro de autenticação. Faça login novamente'
} as const;

export const AI_SUCCESS_MESSAGES = {
  ANALYSIS_COMPLETE: 'Análise concluída com sucesso',
  CATEGORIZATION_COMPLETE: 'Categorização realizada automaticamente',
  PREDICTION_COMPLETE: 'Análise preditiva finalizada',
  RESPONSE_GENERATED: 'Respostas inteligentes geradas'
} as const;

// Configurações de cache
export const CACHE_CONFIG = {
  DEFAULT_TTL: 15, // 15 minutos
  MAX_CACHE_SIZE: 1000, // máximo de entradas no cache
  CLEANUP_INTERVAL: 5 * 60 * 1000 // limpeza a cada 5 minutos
} as const;

// Configurações de retry
export const RETRY_CONFIG = {
  MAX_RETRIES: 3,
  INITIAL_DELAY: 1000, // 1 segundo
  MAX_DELAY: 10000, // 10 segundos
  BACKOFF_FACTOR: 2
} as const;

// Configurações de timeout
export const TIMEOUT_CONFIG = {
  DEFAULT: 30000, // 30 segundos
  BATCH_ANALYSIS: 120000, // 2 minutos para análise em lote
  PREDICTIVE: 60000 // 1 minuto para análise preditiva
} as const;