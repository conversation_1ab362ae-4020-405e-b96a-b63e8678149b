# Configurações do Servidor
PORT=3002
NODE_ENV=development

# Configurações JWT
JWT_SECRET=a2c1a95285dbaa7fc13054fa0b3fef3616429dc3c0a49346c617fe1ed96ac82e
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Configurações do Frontend
FRONTEND_URL=http://localhost:3000

# Configurações de Email (opcional)
EMAIL_FROM=<EMAIL>
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Configurações de Segurança
PASSWORD_MIN_LENGTH=8
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900000
SESSION_TIMEOUT=86400000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
LOGIN_RATE_LIMIT_WINDOW_MS=900000
LOGIN_RATE_LIMIT_MAX_ATTEMPTS=5