#!/bin/bash

set -e

BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "💾 Criando backup em $BACKUP_DIR..."

# Backup dos dados
if [ -d "backend/data" ]; then
    cp -r backend/data "$BACKUP_DIR/"
    echo "✅ Dados do backend copiados"
fi

# Backup das configurações
cp backend/.env "$BACKUP_DIR/.env.backend" 2>/dev/null || echo "⚠️  Arquivo backend/.env não encontrado"
cp frontend/.env.production "$BACKUP_DIR/.env.frontend" 2>/dev/null || echo "⚠️  Arquivo frontend/.env.production não encontrado"

# Backup dos logs
if [ -d "logs" ]; then
    cp -r logs "$BACKUP_DIR/"
    echo "✅ Logs copiados"
fi

echo "✅ Backup concluído em $BACKUP_DIR"
