export interface PaymentData {
  id: string;
  customerName: string;
  customerEmail: string;
  planType: string;
  amount: number;
  currency: string;
  status: 'succeeded' | 'failed' | 'pending' | 'canceled';
  billingCycle: 'monthly' | 'yearly';
  createdAt: string;
  subscriptionId?: string;
  invoiceId?: string;
}

export interface PaymentMetrics {
  totalRevenue: number;
  monthlyRecurring: number;
  yearlyRecurring: number;
  totalCustomers: number;
  successRate: number;
  churnRate: number;
  averageRevenuePerUser: number;
  conversionRate: number;
}

export type PaymentStatus = 'succeeded' | 'failed' | 'pending' | 'canceled';
export type BillingCycle = 'monthly' | 'yearly';
export type PlanType = 'basic' | 'standard' | 'professional';
export type FilterValue = string;
