import NotificationService from './services/notificationService.js';
import User from './models/User.js';

async function seedSimple() {
  try {
    console.log('🌱 Criando notificações de exemplo...');
    
    const users = await User.findAll();
    console.log(`📋 Encontrados ${users.length} usuários`);
    
    for (const user of users) {
      console.log(`📢 Criando notificações para ${user.name}`);
      
      // Notificação de nova demanda
      await NotificationService.notifyUser(
        user.id,
        'Nova demanda recebida',
        '<PERSON> enviou uma solicitação sobre iluminação pública',
        {
          type: 'info',
          category: 'demand',
          actionUrl: '/demands/1',
          actionLabel: 'Ver demanda'
        }
      );
      
      // Notificação de evento próximo
      await NotificationService.notifyUser(
        user.id,
        'Evento próximo',
        'Reunião pública agendada para amanhã às 14h',
        {
          type: 'warning',
          category: 'event',
          actionUrl: '/agenda',
          actionLabel: 'Ver agenda'
        }
      );
      
      // Notificação de demanda resolvida
      await NotificationService.notifyUser(
        user.id,
        'Demanda resolvida',
        'A solicitação #123 foi marcada como concluída',
        {
          type: 'success',
          category: 'demand',
          actionUrl: '/demands/123',
          actionLabel: 'Ver detalhes'
        }
      );
      
      console.log(`✅ 3 notificações criadas para ${user.name}`);
    }
    
    console.log('🎉 Seed concluído com sucesso!');
    
  } catch (error) {
    console.error('❌ Erro no seed:', error);
  }
}

seedSimple();
