@echo off
echo ========================================
echo    DEPLOY BACKEND PROMANDATO - FIXED
echo ========================================
echo.

echo [1/8] Verificando Google Cloud CLI...
where gcloud >nul 2>&1
if %errorlevel% neq 0 (
    echo ERRO: Google Cloud CLI nao encontrado!
    echo Instale em: https://cloud.google.com/sdk/docs/install
    pause
    exit /b 1
)

echo Google Cloud CLI encontrado!
echo.

echo [2/8] Configurando projeto...
gcloud config set project promandato-9a4cf

echo.
echo [3/8] Navegando para o diretorio backend...
cd backend

echo.
echo [4/8] Verificando se o dashboard foi buildado...
if not exist "dashboard\dist" (
    echo Dashboard nao buildado. Buildando agora...
    cd dashboard
    call npm install
    call npm run build
    cd ..
) else (
    echo Dashboard ja buildado.
)

echo.
echo [5/8] Criando arquivo de configuracao de producao...
echo # Configuracao automatica para Cloud Run > .env.production
echo NODE_ENV=production >> .env.production
echo PORT=8080 >> .env.production
echo DEBUG_CORS=true >> .env.production
echo FRONTEND_URL=https://promandato-9a4cf.web.app,https://promandato-9a4cf.firebaseapp.com >> .env.production
echo CORS_ORIGIN=https://promandato-9a4cf.web.app,https://promandato-9a4cf.firebaseapp.com >> .env.production

echo.
echo [6/8] Verificando Dockerfile...
if not exist "Dockerfile" (
    echo Criando Dockerfile...
    echo FROM node:20-alpine > Dockerfile
    echo WORKDIR /app >> Dockerfile
    echo COPY package*.json ./ >> Dockerfile
    echo RUN npm ci --only=production ^&^& npm cache clean --force >> Dockerfile
    echo COPY . . >> Dockerfile
    echo RUN addgroup -g 1001 -S nodejs >> Dockerfile
    echo RUN adduser -S nodejs -u 1001 >> Dockerfile
    echo RUN mkdir -p /app/data /app/uploads ^&^& chown -R nodejs:nodejs /app >> Dockerfile
    echo USER nodejs >> Dockerfile
    echo EXPOSE 8080 >> Dockerfile
    echo ENV NODE_ENV=production >> Dockerfile
    echo ENV PORT=8080 >> Dockerfile
    echo CMD ["npm", "start"] >> Dockerfile
) else (
    echo Dockerfile ja existe.
)

echo.
echo [7/8] Fazendo deploy para Cloud Run...
gcloud run deploy promandato-backend ^
  --source . ^
  --platform managed ^
  --region southamerica-east1 ^
  --allow-unauthenticated ^
  --port 8080 ^
  --memory 2Gi ^
  --cpu 2 ^
  --min-instances 0 ^
  --max-instances 10 ^
  --timeout 300 ^
  --concurrency 80 ^
  --set-env-vars NODE_ENV=production,PORT=8080,DEBUG_CORS=true ^
  --set-env-vars FRONTEND_URL="https://promandato-9a4cf.web.app,https://promandato-9a4cf.firebaseapp.com" ^
  --set-env-vars CORS_ORIGIN="https://promandato-9a4cf.web.app,https://promandato-9a4cf.firebaseapp.com"

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo       BACKEND DEPLOYADO COM SUCESSO!
    echo ========================================
    echo.
    echo URL do Backend: 
    gcloud run services describe promandato-backend --region=southamerica-east1 --format="value(status.url)"
    echo.
    echo Testando o endpoint de health...
    timeout 5 >nul
    echo.
    echo Proximos passos:
    echo 1. Testar a URL do backend
    echo 2. Verificar logs se necessario: gcloud logging read "resource.type=cloud_run_revision"
    echo 3. Atualizar URLs no frontend se necessario
    echo.
) else (
    echo.
    echo ERRO: Deploy falhou!
    echo Verifique os logs acima.
)

echo.
echo [8/8] Limpando arquivos temporarios...
del .env.production 2>nul

cd ..
echo.
echo Deploy concluido!
pause
