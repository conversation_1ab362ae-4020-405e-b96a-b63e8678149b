@echo off
echo ========================================
echo    ETAPA 4: DEPLOY APLICACAO FRONTEND
echo ========================================
echo.

echo [1/6] Verificando frontend...
if not exist "frontend\package.json" (
    echo ERRO: Frontend nao encontrado!
    pause
    exit /b 1
)

echo Frontend encontrado!
echo.

echo [2/6] Instalando dependencias...
cd frontend
npm install

echo.
echo [3/6] Configurando variaveis de ambiente...
echo Criando arquivo .env.production...

echo VITE_API_URL=https://api.promandato.com.br > .env.production
echo VITE_FIREBASE_PROJECT_ID=promandato-9a4cf >> .env.production
echo VITE_ENVIRONMENT=production >> .env.production

echo.
echo [4/6] Fazendo build de producao...
npm run build

if %errorlevel% neq 0 (
    echo ERRO: Build falhou!
    pause
    exit /b 1
)

echo.
echo [5/6] Configurando Firebase Hosting para app...
cd ..

echo Atualizando firebase.json...
echo {> firebase_temp.json
echo   "hosting": [>> firebase_temp.json
echo     {>> firebase_temp.json
echo       "target": "landing",>> firebase_temp.json
echo       "public": "landingpage",>> firebase_temp.json
echo       "ignore": ["firebase.json", "**/.*", "**/node_modules/**"],>> firebase_temp.json
echo       "rewrites": [{"source": "**", "destination": "/index.html"}]>> firebase_temp.json
echo     },>> firebase_temp.json
echo     {>> firebase_temp.json
echo       "target": "app",>> firebase_temp.json
echo       "public": "frontend/dist",>> firebase_temp.json
echo       "ignore": ["firebase.json", "**/.*", "**/node_modules/**"],>> firebase_temp.json
echo       "rewrites": [{"source": "**", "destination": "/index.html"}]>> firebase_temp.json
echo     }>> firebase_temp.json
echo   ],>> firebase_temp.json
echo   "firestore": {>> firebase_temp.json
echo     "rules": "firestore.rules",>> firebase_temp.json
echo     "indexes": "firestore.indexes.json">> firebase_temp.json
echo   }>> firebase_temp.json
echo }>> firebase_temp.json

move firebase_temp.json firebase.json

echo.
echo [6/6] Fazendo deploy da aplicacao...
firebase target:apply hosting app promandato-9a4cf
firebase deploy --only hosting:app

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo       APLICACAO DEPLOYADA!
    echo ========================================
    echo.
    echo URL: https://promandato-9a4cf.web.app
    echo.
    echo Proximos passos:
    echo 1. Configurar dominio customizado (app.promandato.com.br)
    echo 2. Testar login e funcionalidades
    echo 3. Configurar monitoramento
    echo.
) else (
    echo.
    echo ERRO: Deploy falhou!
    echo Verifique os logs e tente novamente.
)

pause
