#!/usr/bin/env node

/**
 * Script CLI para importar dados do CSV do Stripe
 * 
 * Uso:
 * node scripts/import-stripe-csv.js <caminho-do-arquivo.csv>
 * 
 * Exemplo:
 * node scripts/import-stripe-csv.js ./data/stripe-export.csv
 */

import path from 'path';
import { fileURLToPath } from 'url';
import StripeImportService from '../services/stripeImportService.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Cores para output no terminal
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function colorize(text, color) {
    return `${colors[color]}${text}${colors.reset}`;
}

function printHeader() {
    console.log(colorize('\n='.repeat(60), 'cyan'));
    console.log(colorize('🚀 IMPORTADOR DE DADOS DO STRIPE - PRO-MANDATO', 'cyan'));
    console.log(colorize('='.repeat(60), 'cyan'));
}

function printUsage() {
    console.log(colorize('\n📋 USO:', 'yellow'));
    console.log('  node scripts/import-stripe-csv.js <caminho-do-arquivo.csv>');
    console.log(colorize('\n📝 EXEMPLO:', 'yellow'));
    console.log('  node scripts/import-stripe-csv.js ./data/stripe-export.csv');
    console.log(colorize('\n📄 FORMATO DO CSV:', 'yellow'));
    console.log('  O arquivo deve conter as seguintes colunas:');
    console.log('  - Customer Email (obrigatório)');
    console.log('  - Customer Name (obrigatório)');
    console.log('  - Customer ID');
    console.log('  - Subscription ID');
    console.log('  - Product Name');
    console.log('  - Status');
    console.log('  - Amount');
    console.log('  - Currency');
    console.log('  - Created');
    console.log('  - Phone');
    console.log('  - Country');
}

function printStats(stats) {
    console.log(colorize('\n📊 ESTATÍSTICAS DA IMPORTAÇÃO:', 'blue'));
    console.log(`  ${colorize('Total de registros:', 'bright')} ${stats.total}`);
    console.log(`  ${colorize('✅ Criados:', 'green')} ${stats.created}`);
    console.log(`  ${colorize('🔄 Atualizados:', 'blue')} ${stats.updated}`);
    console.log(`  ${colorize('❌ Erros:', 'red')} ${stats.errors}`);
}

function printErrors(errors) {
    if (errors.length === 0) return;
    
    console.log(colorize('\n❌ ERROS ENCONTRADOS:', 'red'));
    errors.slice(0, 10).forEach((error, index) => {
        console.log(`  ${index + 1}. ${error.error}`);
        if (error.row.email || error.row['Customer Email']) {
            console.log(`     Email: ${error.row.email || error.row['Customer Email']}`);
        }
    });
    
    if (errors.length > 10) {
        console.log(`  ... e mais ${errors.length - 10} erros`);
    }
}

async function main() {
    printHeader();
    
    // Verificar argumentos
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log(colorize('❌ Erro: Caminho do arquivo CSV é obrigatório', 'red'));
        printUsage();
        process.exit(1);
    }
    
    const csvFilePath = path.resolve(args[0]);
    
    console.log(colorize(`\n📁 Arquivo: ${csvFilePath}`, 'bright'));
    
    try {
        // Verificar se arquivo existe
        const fs = await import('fs-extra');
        if (!await fs.pathExists(csvFilePath)) {
            throw new Error(`Arquivo não encontrado: ${csvFilePath}`);
        }
        
        console.log(colorize('✅ Arquivo encontrado', 'green'));
        console.log(colorize('\n🔄 Iniciando importação...', 'yellow'));
        
        // Criar instância do serviço de importação
        const importService = new StripeImportService();
        
        // Executar importação
        const startTime = Date.now();
        const result = await importService.importFromCSV(csvFilePath);
        const endTime = Date.now();
        const duration = ((endTime - startTime) / 1000).toFixed(2);
        
        // Mostrar resultados
        console.log(colorize(`\n⏱️  Tempo de execução: ${duration}s`, 'cyan'));
        
        if (result.success) {
            console.log(colorize('\n🎉 IMPORTAÇÃO CONCLUÍDA COM SUCESSO!', 'green'));
            printStats(result.stats);
            
            if (result.errors && result.errors.length > 0) {
                printErrors(result.errors);
            }
            
            console.log(colorize('\n📧 PRÓXIMOS PASSOS:', 'blue'));
            console.log('  1. Verifique os emails enviados aos usuários');
            console.log('  2. Confira o relatório detalhado em backend/data/import-reports/');
            console.log('  3. Teste o login dos usuários criados');
            
        } else {
            console.log(colorize('\n❌ FALHA NA IMPORTAÇÃO', 'red'));
            console.log(`Erro: ${result.error}`);
            printStats(result.stats);
            process.exit(1);
        }
        
    } catch (error) {
        console.log(colorize(`\n❌ ERRO: ${error.message}`, 'red'));
        console.error(error);
        process.exit(1);
    }
    
    console.log(colorize('\n' + '='.repeat(60), 'cyan'));
}

// Executar script
if (import.meta.url === `file://${process.argv[1]}`) {
    main().catch(error => {
        console.error(colorize(`\n❌ Erro fatal: ${error.message}`, 'red'));
        process.exit(1);
    });
}
