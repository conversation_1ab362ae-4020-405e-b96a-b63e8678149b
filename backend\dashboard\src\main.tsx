import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import {
  registerServiceWorker,
  monitorPerformance,
  preloadCriticalResources
} from './utils/bundleOptimization';
import { initializeErrorHandling, safeLocalStorage } from './utils/errorHandler';

// Initialize dark mode before rendering
const initializeDarkMode = () => {
  try {
    const isDark = safeLocalStorage.getItem('darkMode', false);
    console.log('🌙 Inicializando modo escuro:', isDark);

    if (isDark) {
      document.documentElement.classList.add('dark');
      document.body.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
      document.body.classList.remove('dark');
    }
  } catch (error) {
    console.error('Error initializing dark mode:', error);
  }
};

// Initialize error handling first
initializeErrorHandling();

// Initialize optimizations and dark mode
initializeDarkMode();
preloadCriticalResources();
registerServiceWorker();
monitorPerformance();

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);