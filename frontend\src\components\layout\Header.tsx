import React, { useState, useRef, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { ICONS, USER_NAV_ITEMS, ROUTE_PATHS } from '../../constants';
import { SearchBar } from '../ui/SearchBar';
import { NotificationCenter } from '../ui/NotificationCenter';

interface HeaderProps {
  toggleSidebar: () => void;
}

export const Header: React.FC<HeaderProps> = ({ toggleSidebar }) => {
  const { currentUser, signOut } = useAuth();
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const userMenuRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  // Debug: Log do avatar URL
  console.log('Header - Avatar URL atual:', currentUser?.avatarUrl);

  const handleSignOut = async () => {
    await signOut();
    navigate(ROUTE_PATHS.LOGIN);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setIsUserMenuOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  if (!currentUser) return null;

  return (
    <header className="bg-white dark:bg-neutral-dark shadow-sm border-b border-gray-200 dark:border-neutral-medium">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">
          {/* Left side */}
          <div className="flex items-center">
            {/* Mobile menu button */}
            <div className="lg:hidden">
              <button
                type="button"
                onClick={toggleSidebar}
                className="text-neutral-dark dark:text-neutral-light hover:text-primary dark:hover:text-primary-light focus:outline-none"
              >
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>
            </div>
          </div>

          {/* Center - Search Bar */}
          <div className="hidden lg:block">
            <SearchBar 
              placeholder="Pesquisar demandas, cidadãos, eventos..." 
              className="w-96"
            />
          </div>

          {/* Right side */}
          <div className="flex items-center space-x-4">
            {/* Notifications */}
            <NotificationCenter />

            {/* User Menu */}
            <div className="relative" ref={userMenuRef}>
              <button
                type="button"
                onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                className="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-dark dark:focus:ring-offset-neutral-darkest"
              >
                <span className="sr-only">Open user menu</span>
                <img
                  className="h-8 w-8 rounded-full object-cover"
                  src={currentUser.avatarUrl || `https://ui-avatars.com/api/?name=${currentUser.name}&background=random`}
                  alt={currentUser.name}
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(currentUser.name)}&background=random`;
                  }}
                />
                <span className="ml-2 hidden md:inline text-neutral-dark dark:text-neutral-light font-medium">{currentUser.name}</span>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="ml-1 h-5 w-5 text-neutral-dark dark:text-neutral-light hidden md:inline">
                  <path fillRule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clipRule="evenodd" />
                </svg>
              </button>

              {/* User dropdown menu */}
              {isUserMenuOpen && (
                <div className="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-neutral-dark ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
                  {USER_NAV_ITEMS.map((item) => (
                    <Link
                      key={item.label}
                      to={item.path}
                      onClick={() => setIsUserMenuOpen(false)}
                      className="group flex items-center w-full px-4 py-2 text-sm text-neutral-dark dark:text-neutral-light hover:bg-gray-100 dark:hover:bg-neutral-darker"
                    >
                      {item.icon && <span className="mr-3 text-gray-400 dark:text-neutral-DEFAULT group-hover:text-gray-500 dark:group-hover:text-neutral-light">{React.cloneElement(item.icon, { className: 'w-5 h-5'})}</span>}
                      {item.label}
                    </Link>
                  ))}
                  <button
                    type="button"
                    onClick={handleSignOut}
                    className="group flex items-center w-full px-4 py-2 text-sm text-neutral-dark dark:text-neutral-light hover:bg-gray-100 dark:hover:bg-neutral-darker"
                  >
                    <span className="mr-3 text-gray-400 dark:text-neutral-DEFAULT group-hover:text-gray-500 dark:group-hover:text-neutral-light">{React.cloneElement(ICONS.LOGOUT, { className: 'w-5 h-5'})}</span>
                    Sair
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};