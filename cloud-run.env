# ===========================================
# GOOGLE CLOUD RUN - CONFIGURAÇÃO
# ===========================================

# Configurações do projeto
PROJECT_ID=promandato-9a4cf
REGION=southamerica-east1
SERVICE_NAME=promandato-backend

# ===========================================
# VARIÁVEIS DE AMBIENTE PARA CLOUD RUN
# ===========================================

# Ambiente
NODE_ENV=production
PORT=8080

# Segurança
JWT_SECRET=sua_chave_jwt_super_secreta_aqui
BCRYPT_SALT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900000
SESSION_TIMEOUT=3600000

# Stripe
STRIPE_SECRET_KEY=sk_test_6pRNASCoBOKtIshFeQd4XMUhWefIZZkWdyjH1KpgsQcsF1brs2FBwU958752Y47aQ7475Q57J5K8apw62Y69yJG8g
STRIPE_WEBHOOK_SECRET=whsec_zHQpJadEXtqbSJX6umRuCJs7ZA4dCgbY

# URLs
FRONTEND_URL=https://www.promandato.com.br
ALLOWED_ORIGINS=https://promandato-9a4cf.web.app,https://www.promandato.com.br,https://www.promandato.com.br/landingpage,https://www.promandato.com.br/app

# Firebase (se necessário no backend)
FIREBASE_PROJECT_ID=promandato-9a4cf
FIREBASE_PRIVATE_KEY_ID=sua_private_key_id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nsua_private_key_aqui\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>

# Email
ADMIN_EMAIL=<EMAIL>

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
