import rateLimit from 'express-rate-limit';
import config from '../config.js';

// Rate limiter geral - muito mais permissivo para produção
const generalLimiter = rateLimit({
  windowMs: process.env.NODE_ENV === 'production' ? 5 * 60 * 1000 : config.rateLimit.windowMs, // 5 minutos em produção
  max: process.env.NODE_ENV === 'production' ? 2000 : config.rateLimit.maxRequests, // 2000 requests em produção
  message: {
    success: false,
    message: 'Muitas requisições. Tente novamente em alguns minutos.',
    retryAfter: Math.ceil(config.rateLimit.windowMs / 1000)
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Configuração melhorada para Cloud Run
  keyGenerator: (req) => {
    // Para Cloud Run, usar X-Forwarded-For se disponível
    const forwarded = req.headers['x-forwarded-for'];
    if (forwarded) {
      return forwarded.split(',')[0].trim();
    }
    // Fallback para req.ip ou outros métodos
    return req.ip || req.connection.remoteAddress || req.socket.remoteAddress || 'unknown';
  },
  // Pular rate limiting para arquivos estáticos, health checks e endpoints públicos
  skip: (req) => {
    const skipPaths = [
      '/assets/', '/static/', '/favicon.ico', '/manifest.json',
      '/health', '/', '/api/health',
      '/landingpage/assets/', '/landingpage/css/', '/landingpage/js/',
      '/landingpage/images/', '/landingpage/fonts/',
      '/api/public/', '/api/plans', '/api/cors-debug', '/api/system/info'
    ];
    return skipPaths.some(path => req.url.startsWith(path)) ||
           req.url.match(/\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/);
  },
  handler: (req, res) => {
    console.log(`[RATE LIMIT] IP: ${req.ip}, URL: ${req.url}, Headers:`, req.headers);
    res.status(429).json({
      success: false,
      message: 'Muitas requisições. Tente novamente em alguns minutos.',
      retryAfter: Math.ceil(config.rateLimit.windowMs / 1000)
    });
  }
});

// Rate limiter específico para login
const loginLimiter = rateLimit({
  windowMs: config.rateLimit.loginWindowMs,
  max: config.rateLimit.maxLoginAttempts,
  message: {
    success: false,
    message: 'Muitas tentativas de login. Tente novamente em 15 minutos.',
    retryAfter: Math.ceil(config.rateLimit.loginWindowMs / 1000)
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true, // Não contar requests bem-sucedidos
  // Configuração para funcionar com trust proxy
  keyGenerator: (req) => {
    return req.ip || req.connection.remoteAddress || 'unknown';
  },
  handler: (req, res) => {
    res.status(429).json({
      success: false,
      message: 'Muitas tentativas de login. Tente novamente em 15 minutos.',
      retryAfter: Math.ceil(config.rateLimit.loginWindowMs / 1000)
    });
  }
});

// Rate limiter para criação de usuários
const createUserLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hora
  max: 5, // máximo 5 usuários criados por IP por hora
  message: {
    success: false,
    message: 'Muitos usuários criados. Tente novamente em 1 hora.',
    retryAfter: 3600
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Configuração para funcionar com trust proxy
  keyGenerator: (req) => {
    return req.ip || req.connection.remoteAddress || 'unknown';
  },
  handler: (req, res) => {
    res.status(429).json({
      success: false,
      message: 'Muitos usuários criados. Tente novamente em 1 hora.',
      retryAfter: 3600
    });
  }
});

// Rate limiter para reset de senha
const passwordResetLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hora
  max: 3, // máximo 3 tentativas de reset por IP por hora
  message: {
    success: false,
    message: 'Muitas tentativas de reset de senha. Tente novamente em 1 hora.',
    retryAfter: 3600
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Configuração para funcionar com trust proxy
  keyGenerator: (req) => {
    return req.ip || req.connection.remoteAddress || 'unknown';
  },
  handler: (req, res) => {
    res.status(429).json({
      success: false,
      message: 'Muitas tentativas de reset de senha. Tente novamente em 1 hora.',
      retryAfter: 3600
    });
  }
});

// Rate limiter específico para endpoints públicos/read-only
const publicLimiter = rateLimit({
  windowMs: 1 * 60 * 1000, // 1 minuto
  max: process.env.NODE_ENV === 'production' ? 500 : 100, // 500 requests por minuto em produção
  message: {
    success: false,
    message: 'Muitas requisições para endpoints públicos. Tente novamente em alguns segundos.',
    retryAfter: 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => {
    // Para endpoints públicos, ser mais permissivo
    const forwarded = req.headers['x-forwarded-for'];
    if (forwarded) {
      return forwarded.split(',')[0].trim();
    }
    return req.ip || req.connection.remoteAddress || req.socket.remoteAddress || 'unknown';
  },
  handler: (req, res) => {
    console.log(`[PUBLIC RATE LIMIT] IP: ${req.ip}, URL: ${req.url}`);
    res.status(429).json({
      success: false,
      message: 'Muitas requisições para endpoints públicos. Tente novamente em alguns segundos.',
      retryAfter: 60
    });
  }
});

export {
  generalLimiter,
  publicLimiter,
  loginLimiter,
  createUserLimiter,
  passwordResetLimiter
};