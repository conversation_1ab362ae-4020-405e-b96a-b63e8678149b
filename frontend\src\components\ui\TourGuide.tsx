import React, { useState, useEffect, useRef } from 'react';
import { Button } from './Button';

interface TourStep {
  id: string;
  title: string;
  content: string;
  target: string; // CSS selector for the target element
  position: 'top' | 'bottom' | 'left' | 'right';
  action?: () => void; // Optional action to perform when step is shown
}

interface TourGuideProps {
  steps: TourStep[];
  isActive: boolean;
  onComplete: () => void;
  onSkip: () => void;
}

export const TourGuide: React.FC<TourGuideProps> = ({
  steps,
  isActive,
  onComplete,
  onSkip
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [targetElement, setTargetElement] = useState<HTMLElement | null>(null);
  const [tooltipPosition, setTooltipPosition] = useState({ top: 0, left: 0 });
  const tooltipRef = useRef<HTMLDivElement>(null);

  const currentStepData = steps[currentStep];

  useEffect(() => {
    if (!isActive || !currentStepData) return;

    const target = document.querySelector(currentStepData.target) as HTMLElement;
    if (target) {
      setTargetElement(target);
      
      // Execute step action if provided
      if (currentStepData.action) {
        currentStepData.action();
      }

      // Scroll target into view
      target.scrollIntoView({ behavior: 'smooth', block: 'center' });
      
      // Calculate tooltip position
      setTimeout(() => {
        calculateTooltipPosition(target, currentStepData.position);
      }, 100);
    }
  }, [currentStep, isActive, currentStepData]);

  const calculateTooltipPosition = (target: HTMLElement, position: string) => {
    if (!tooltipRef.current) return;

    const targetRect = target.getBoundingClientRect();
    const tooltipRect = tooltipRef.current.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

    let top = 0;
    let left = 0;

    switch (position) {
      case 'top':
        top = targetRect.top + scrollTop - tooltipRect.height - 10;
        left = targetRect.left + scrollLeft + (targetRect.width - tooltipRect.width) / 2;
        break;
      case 'bottom':
        top = targetRect.bottom + scrollTop + 10;
        left = targetRect.left + scrollLeft + (targetRect.width - tooltipRect.width) / 2;
        break;
      case 'left':
        top = targetRect.top + scrollTop + (targetRect.height - tooltipRect.height) / 2;
        left = targetRect.left + scrollLeft - tooltipRect.width - 10;
        break;
      case 'right':
        top = targetRect.top + scrollTop + (targetRect.height - tooltipRect.height) / 2;
        left = targetRect.right + scrollLeft + 10;
        break;
    }

    // Ensure tooltip stays within viewport
    const maxLeft = window.innerWidth - tooltipRect.width - 20;
    const maxTop = window.innerHeight + scrollTop - tooltipRect.height - 20;
    
    left = Math.max(20, Math.min(left, maxLeft));
    top = Math.max(scrollTop + 20, Math.min(top, maxTop));

    setTooltipPosition({ top, left });
  };

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onComplete();
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const skipTour = () => {
    onSkip();
  };

  if (!isActive || !currentStepData) return null;

  return (
    <>
      {/* Overlay */}
      <div className="fixed inset-0 bg-black bg-opacity-50 z-40" />
      
      {/* Highlight target element */}
      {targetElement && (
        <div
          className="fixed z-50 pointer-events-none"
          style={{
            top: targetElement.getBoundingClientRect().top + window.pageYOffset - 4,
            left: targetElement.getBoundingClientRect().left + window.pageXOffset - 4,
            width: targetElement.offsetWidth + 8,
            height: targetElement.offsetHeight + 8,
            border: '2px solid #3B82F6',
            borderRadius: '8px',
            boxShadow: '0 0 0 9999px rgba(0, 0, 0, 0.5)',
          }}
        />
      )}

      {/* Tooltip */}
      <div
        ref={tooltipRef}
        className="fixed z-50 bg-white dark:bg-neutral-dark rounded-lg shadow-xl border border-gray-200 dark:border-neutral-medium p-6 max-w-sm"
        style={{
          top: tooltipPosition.top,
          left: tooltipPosition.left,
        }}
      >
        {/* Arrow */}
        <div
          className={`absolute w-3 h-3 bg-white dark:bg-neutral-dark border-gray-200 dark:border-neutral-medium transform rotate-45 ${
            currentStepData.position === 'top' ? 'bottom-[-6px] border-b border-r' :
            currentStepData.position === 'bottom' ? 'top-[-6px] border-t border-l' :
            currentStepData.position === 'left' ? 'right-[-6px] border-r border-b' :
            'left-[-6px] border-l border-t'
          }`}
          style={{
            left: currentStepData.position === 'left' || currentStepData.position === 'right' ? 
              undefined : '50%',
            top: currentStepData.position === 'top' || currentStepData.position === 'bottom' ? 
              undefined : '50%',
            marginLeft: currentStepData.position === 'left' || currentStepData.position === 'right' ? 
              undefined : '-6px',
            marginTop: currentStepData.position === 'top' || currentStepData.position === 'bottom' ? 
              undefined : '-6px',
          }}
        />

        {/* Content */}
        <div className="mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-neutral-light mb-2">
            {currentStepData.title}
          </h3>
          <p className="text-gray-600 dark:text-neutral-DEFAULT">
            {currentStepData.content}
          </p>
        </div>

        {/* Progress */}
        <div className="mb-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm text-gray-500 dark:text-neutral-medium">
              Passo {currentStep + 1} de {steps.length}
            </span>
            <button
              type="button"
              onClick={skipTour}
              className="text-sm text-gray-500 dark:text-neutral-medium hover:text-gray-700 dark:hover:text-neutral-DEFAULT"
            >
              Pular tour
            </button>
          </div>
          <div className="w-full bg-gray-200 dark:bg-neutral-medium rounded-full h-2">
            <div
              className="bg-primary h-2 rounded-full transition-all duration-300"
              style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
            />
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-between">
          <Button
            variant="outline"
            size="sm"
            onClick={prevStep}
            disabled={currentStep === 0}
          >
            Anterior
          </Button>
          
          <Button
            variant="primary"
            size="sm"
            onClick={nextStep}
          >
            {currentStep === steps.length - 1 ? 'Finalizar' : 'Próximo'}
          </Button>
        </div>
      </div>
    </>
  );
};

// Hook for managing tour state
export const useTourGuide = () => {
  const [isActive, setIsActive] = useState(false);
  const [hasSeenTour, setHasSeenTour] = useState(false);

  useEffect(() => {
    // Check if user has seen the tour before
    const tourCompleted = localStorage.getItem('tour-completed');
    if (tourCompleted) {
      setHasSeenTour(true);
    }
  }, []);

  const startTour = () => {
    setIsActive(true);
  };

  const completeTour = () => {
    setIsActive(false);
    setHasSeenTour(true);
    localStorage.setItem('tour-completed', 'true');
  };

  const skipTour = () => {
    setIsActive(false);
    setHasSeenTour(true);
    localStorage.setItem('tour-completed', 'true');
  };

  const resetTour = () => {
    setHasSeenTour(false);
    localStorage.removeItem('tour-completed');
  };

  return {
    isActive,
    hasSeenTour,
    startTour,
    completeTour,
    skipTour,
    resetTour
  };
};

// Predefined tour steps for different pages
export const dashboardTourSteps: TourStep[] = [
  {
    id: 'welcome',
    title: 'Bem-vindo ao Pro-Mandato!',
    content: 'Este é seu painel principal onde você pode ver um resumo de todas as atividades do seu mandato.',
    target: '[data-tour="dashboard"]',
    position: 'bottom'
  },
  {
    id: 'sidebar',
    title: 'Menu de Navegação',
    content: 'Use este menu para navegar entre as diferentes seções do sistema.',
    target: '[data-tour="sidebar"]',
    position: 'right'
  },
  {
    id: 'demands-card',
    title: 'Demandas',
    content: 'Aqui você pode ver o número de demandas pendentes e acessar a gestão completa.',
    target: '[data-tour="demands-card"]',
    position: 'top'
  },
  {
    id: 'citizens-card',
    title: 'Cidadãos',
    content: 'Acompanhe o número de cidadãos cadastrados e gerencie seus contatos.',
    target: '[data-tour="citizens-card"]',
    position: 'top'
  },
  {
    id: 'search',
    title: 'Busca Global',
    content: 'Use a barra de busca para encontrar rapidamente demandas, cidadãos ou eventos.',
    target: '[data-tour="search"]',
    position: 'bottom'
  },
  {
    id: 'notifications',
    title: 'Notificações',
    content: 'Fique por dentro de todas as novidades e alertas importantes.',
    target: '[data-tour="notifications"]',
    position: 'bottom'
  }
];