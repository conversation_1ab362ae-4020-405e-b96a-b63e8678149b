<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pagamento Confirmado - Pro-Mandato</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Lucide Icons -->
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <!-- Stripe -->
    <script src="https://js.stripe.com/v3/"></script>

    <!-- Custom Styles -->
    <link rel="stylesheet" href="styles.css">
    
    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f4ff',
                            100: '#e0e9ff',
                            500: '#667eea',
                            600: '#5a67d8',
                            700: '#4c51bf',
                            800: '#434190',
                            900: '#3c366b'
                        },
                        secondary: {
                            50: '#fef7e0',
                            100: '#feebc8',
                            500: '#fbbf24',
                            600: '#f59e0b',
                            700: '#d97706'
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="font-sans antialiased bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <span class="text-2xl font-bold gradient-text">🏛️ Pro-Mandato</span>
                </div>
                <div>
                    <a href="http://localhost:5174" class="bg-primary-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-primary-700 transition-colors">
                        Acessar Sistema
                    </a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Success Content -->
    <main class="min-h-screen flex items-center justify-center py-12">
        <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <!-- Success Icon -->
            <div class="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-8">
                <i data-lucide="check-circle" class="h-12 w-12 text-green-600"></i>
            </div>
            
            <!-- Success Message -->
            <h1 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                🎉 Pagamento Confirmado!
            </h1>
            
            <p class="text-xl text-gray-600 mb-8">
                Bem-vindo ao Pro-Mandato! Sua assinatura foi ativada com sucesso.
            </p>
            
            <!-- Next Steps -->
            <div class="bg-white rounded-xl shadow-lg p-8 mb-8">
                <h2 class="text-2xl font-semibold text-gray-900 mb-6">Próximos Passos</h2>
                
                <div class="space-y-6">
                    <!-- Step 1 -->
                    <div class="flex items-start">
                        <div class="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">
                            1
                        </div>
                        <div class="text-left">
                            <h3 class="font-semibold text-gray-900 mb-1">Acesse sua conta</h3>
                            <p class="text-gray-600">Clique no botão abaixo para fazer login no sistema Pro-Mandato</p>
                        </div>
                    </div>
                    
                    <!-- Step 2 -->
                    <div class="flex items-start">
                        <div class="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">
                            2
                        </div>
                        <div class="text-left">
                            <h3 class="font-semibold text-gray-900 mb-1">Configure seu perfil</h3>
                            <p class="text-gray-600">Complete as informações do seu mandato e equipe</p>
                        </div>
                    </div>
                    
                    <!-- Step 3 -->
                    <div class="flex items-start">
                        <div class="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">
                            3
                        </div>
                        <div class="text-left">
                            <h3 class="font-semibold text-gray-900 mb-1">Importe seus dados</h3>
                            <p class="text-gray-600">Nossa equipe ajudará você a migrar seus dados existentes</p>
                        </div>
                    </div>
                    
                    <!-- Step 4 -->
                    <div class="flex items-start">
                        <div class="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-bold mr-4 mt-1">
                            4
                        </div>
                        <div class="text-left">
                            <h3 class="font-semibold text-gray-900 mb-1">Comece a usar</h3>
                            <p class="text-gray-600">Explore todos os recursos e transforme sua gestão política</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                <a href="http://localhost:5174" class="bg-primary-600 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-primary-700 transition-colors">
                    Acessar Pro-Mandato
                </a>
                <button type="button" onclick="requestOnboarding()" class="border-2 border-primary-600 text-primary-600 px-8 py-4 rounded-lg text-lg font-semibold hover:bg-primary-600 hover:text-white transition-colors">
                    Agendar Onboarding
                </button>
            </div>
            
            <!-- Support Info -->
            <div class="bg-blue-50 rounded-lg p-6">
                <h3 class="font-semibold text-gray-900 mb-2">Precisa de Ajuda?</h3>
                <p class="text-gray-600 mb-4">
                    Nossa equipe está pronta para ajudar você a começar
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center text-sm">
                    <div class="flex items-center">
                        <i data-lucide="phone" class="h-4 w-4 text-primary-600 mr-2"></i>
                        <span>(11) 9999-9999</span>
                    </div>
                    <div class="flex items-center">
                        <i data-lucide="mail" class="h-4 w-4 text-primary-600 mr-2"></i>
                        <span><EMAIL></span>
                    </div>
                    <div class="flex items-center">
                        <i data-lucide="message-circle" class="h-4 w-4 text-primary-600 mr-2"></i>
                        <span>Chat ao vivo</span>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200 py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <p class="text-gray-600">
                © 2024 Pro-Mandato. Todos os direitos reservados.
            </p>
        </div>
    </footer>

    <script>
        // Inicializar ícones Lucide
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof lucide !== 'undefined') {
                lucide.createIcons();
            }
        });

        // Função para agendar onboarding
        function requestOnboarding() {
            // Aqui você pode implementar a lógica para agendar onboarding
            alert('Funcionalidade de agendamento será implementada em breve!');
        }

        // Verificar status do pagamento
        async function checkPaymentStatus() {
            const urlParams = new URLSearchParams(window.location.search);
            const sessionId = urlParams.get('session_id');

            if (!sessionId) {
                console.log('No session ID found');
                return;
            }

            try {
                const response = await fetch(`/api/stripe/check-payment-status/${sessionId}`);

                if (!response.ok) {
                    throw new Error('Failed to check payment status');
                }

                const data = await response.json();

                if (data.success && data.status === 'paid') {
                    console.log('Payment confirmed:', data);

                    // Atualizar informações na página se necessário
                    if (data.customerEmail) {
                        // Você pode mostrar o email do cliente ou outras informações
                        console.log('Customer email:', data.customerEmail);
                    }

                    if (data.amountTotal) {
                        const amount = (data.amountTotal / 100).toLocaleString('pt-BR', {
                            style: 'currency',
                            currency: data.currency?.toUpperCase() || 'BRL'
                        });
                        console.log('Amount paid:', amount);
                    }
                } else {
                    console.warn('Payment not confirmed or failed');
                }

            } catch (error) {
                console.error('Error checking payment status:', error);
            }
        }

        // Verificar status quando a página carregar
        checkPaymentStatus();
    </script>
</body>
</html>
