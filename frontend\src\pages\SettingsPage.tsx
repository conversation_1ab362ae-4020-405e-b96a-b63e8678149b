import React from 'react';
// Corrigir a importação dos componentes UI
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/Input';
import PlanManagement from '../components/ui/PlanManagement';
import { useAuth } from '../hooks/useAuth';
import { useTheme } from '../hooks/useTheme';
import { SETTINGS_NAV_ITEMS } from '../constants';
import { Link, useLocation } from 'react-router-dom';
import { UserRole } from '../types';

// Adicionar suporte para children
interface SettingsPageProps {
  children?: React.ReactNode;
}

const SettingsPage: React.FC<SettingsPageProps> = ({ children }) => {
  const { currentUser } = useAuth();
  const { theme, toggleTheme } = useTheme();
  const location = useLocation();

  // Mock data para planos e recursos de IA (substitua pelas importações reais)
  const PLANS = [
    { id: 1, name: 'Básico', price: { monthly: 29 } },
    { id: 2, name: 'Profissional', price: { monthly: 79 } },
    { id: 3, name: 'Enterprise', price: { monthly: 199 } }
  ];

  const AI_FEATURES = [
    { id: 1, name: 'Análise de Sentimentos', description: 'Análise automática de comentários', enabled: true },
    { id: 2, name: 'Geração de Relatórios', description: 'Relatórios automáticos com IA', enabled: true },
    { id: 3, name: 'Chatbot Inteligente', description: 'Assistente virtual para cidadãos', enabled: false }
  ];

  // Estado para configurações de perfil
  const [profileName, setProfileName] = React.useState(currentUser?.name || '');
  const [profileEmail, setProfileEmail] = React.useState(currentUser?.email || '');
  const [notificationsEnabled, setNotificationsEnabled] = React.useState(true);

  // Estados para configurações do sistema (Admin)
  const [systemSettings, setSystemSettings] = React.useState({
    // Branding
    organizationName: 'ProMandato',
    logoUrl: '',
    primaryColor: '#3B82F6',
    secondaryColor: '#10B981',
    
    // Integrações
    emailProvider: 'smtp',
    smtpHost: '',
    smtpPort: 587,
    smtpUser: '',
    smtpPassword: '',
    
    // APIs e Webhooks
    apiEnabled: true,
    webhooksEnabled: false,
    webhookUrl: '',
    
    // Configurações de planos
    plansEnabled: true,
    trialDays: 14,
    
    // Configurações de IA
    aiEnabled: true,
    aiProvider: 'gemini',
    aiApiKey: '',
    
    // Configurações de notificações
    pushNotificationsEnabled: true,
    smsEnabled: false,
    smsProvider: '',
    smsApiKey: '',
    
    // Configurações de segurança
    twoFactorRequired: false,
    sessionTimeout: 24, // horas
    passwordMinLength: 8,
    
    // Configurações de backup
    autoBackup: true,
    backupFrequency: 'daily', // daily, weekly, monthly
    backupRetention: 30, // dias
  });

  const [activeAdminTab, setActiveAdminTab] = React.useState('branding');

  const handleProfileSave = () => {
    console.log("Saving profile:", { profileName, profileEmail });
    alert("Perfil salvo (simulado)!");
  };

  const handlePreferencesSave = () => {
    console.log("Saving preferences:", { notificationsEnabled, theme });
    alert("Preferências salvas (simulado)!");
  };

  const handleSystemSettingsSave = () => {
    console.log("Saving system settings:", systemSettings);
    alert("Configurações do sistema salvas (simulado)!");
  };

  const handleSystemSettingChange = (key: string, value: any) => {
    setSystemSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // Filtrar itens de configuração com base nas permissões do usuário
  const filteredSettingsItems = SETTINGS_NAV_ITEMS.filter(item => {
    if (!item.allowedRoles) return true; // Sem restrições de papel
    if (!currentUser) return false; // Usuário não logado
    return item.allowedRoles.includes(currentUser.role);
  });

  return (
    <div className="space-y-8">
      <h1 className="text-3xl font-bold text-neutral-dark dark:text-neutral-light">Configurações</h1>
      
      {/* Menu de navegação de configurações */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="md:col-span-1">
          <Card>
            <nav className="space-y-1">
              {filteredSettingsItems.map((item) => (
                <Link
                  key={item.label}
                  to={item.path}
                  className={`flex items-center px-3 py-2 text-sm font-medium rounded-md ${
                    location.pathname === item.path
                      ? 'bg-primary text-white'
                      : 'text-neutral-dark dark:text-neutral-light hover:bg-neutral-light dark:hover:bg-neutral-dark'
                  }`}
                >
                  <span className="mr-3">{item.icon}</span>
                  {item.label}
                </Link>
              ))}
            </nav>
          </Card>
        </div>
        
        {/* Conteúdo principal - mostrar children ou conteúdo padrão */}
        <div className="md:col-span-3 space-y-6">
          {children ? (
            children
          ) : location.pathname === '/settings/plan' ? (
            <PlanManagement />
          ) : (
            <>
              <Card title="Configurações de Perfil">
                <div className="space-y-4">
                  <Input
                    label="Nome Completo"
                    value={profileName}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => setProfileName(e.target.value)}
                  />
                  <Input
                    label="Email"
                    type="email"
                    value={profileEmail}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => setProfileEmail(e.target.value)}
                    disabled
                  />
                  <Input
                    label="Nova Senha"
                    type="password"
                    placeholder="Deixe em branco para não alterar"
                  />
                  <Input
                    label="Confirmar Nova Senha"
                    type="password"
                  />
                  <div className="pt-2">
                    <Button onClick={handleProfileSave}>Salvar Perfil</Button>
                  </div>
                </div>
              </Card>

              <Card title="Preferências do Sistema">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-neutral-dark dark:text-neutral-light">Ativar Notificações por Email</span>
                    <label htmlFor="notifications" className="flex items-center cursor-pointer">
                      <div className="relative">
                        <input 
                          type="checkbox" 
                          id="notifications" 
                          className="sr-only" 
                          checked={notificationsEnabled} 
                          onChange={() => setNotificationsEnabled(!notificationsEnabled)}
                          aria-label="Ativar Notificações por Email"
                        />
                        <div className={`block w-10 h-6 rounded-full ${notificationsEnabled ? 'bg-primary' : 'bg-gray-300 dark:bg-neutral-medium'}`}></div>
                        <div className={`dot absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform ${notificationsEnabled ? 'transform translate-x-full' : ''}`}></div>
                      </div>
                    </label>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-neutral-dark dark:text-neutral-light">Modo Escuro</span>
                    <label htmlFor="darkModeToggle" className="flex items-center cursor-pointer">
                      <div className="relative">
                        <input 
                          type="checkbox" 
                          id="darkModeToggle" 
                          className="sr-only" 
                          checked={theme === 'dark'} 
                          onChange={toggleTheme}
                          aria-label="Ativar Modo Escuro"
                        />
                        <div className={`block w-10 h-6 rounded-full ${theme === 'dark' ? 'bg-primary' : 'bg-gray-300 dark:bg-neutral-medium'}`}></div>
                        <div className={`dot absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform ${theme === 'dark' ? 'transform translate-x-full' : ''}`}></div>
                      </div>
                    </label>
                  </div>
                  <div className="pt-2">
                    <Button onClick={handlePreferencesSave}>Salvar Preferências</Button>
                  </div>
                </div>
              </Card>
              
              {currentUser?.role === UserRole.ADMIN && (
                <Card title="Configurações da Organização (Admin)">
                  <div className="space-y-6">
                    {/* Tabs de navegação */}
                    <div className="border-b border-gray-200 dark:border-neutral-medium">
                      <nav className="-mb-px flex space-x-8">
                        {[
                          { id: 'branding', label: 'Branding', icon: '🎨' },
                          { id: 'integrations', label: 'Integrações', icon: '🔗' },
                          { id: 'plans', label: 'Planos', icon: '💎' },
                          { id: 'ai', label: 'IA', icon: '🤖' },
                          { id: 'security', label: 'Segurança', icon: '🔒' },
                          { id: 'backup', label: 'Backup', icon: '💾' }
                        ].map((tab) => (
                          <button
                            key={tab.id}
                            type="button"
                            onClick={() => setActiveAdminTab(tab.id)}
                            className={`py-2 px-1 border-b-2 font-medium text-sm ${
                              activeAdminTab === tab.id
                                ? 'border-primary text-primary'
                                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-neutral-medium dark:hover:text-neutral-light'
                            }`}
                          >
                            <span className="mr-2">{tab.icon}</span>
                            {tab.label}
                          </button>
                        ))}
                      </nav>
                    </div>

                    {/* Conteúdo das tabs */}
                    <div className="mt-6">
                      {activeAdminTab === 'branding' && (
                        <div className="space-y-4">
                          <h3 className="text-lg font-medium text-neutral-dark dark:text-neutral-light">Identidade Visual</h3>
                          
                          <Input
                            label="Nome da Organização"
                            value={systemSettings.organizationName}
                            onChange={(e) => handleSystemSettingChange('organizationName', e.target.value)}
                            placeholder="Ex: Câmara Municipal de..."
                          />
                          
                          <Input
                            label="URL do Logo"
                            value={systemSettings.logoUrl}
                            onChange={(e) => handleSystemSettingChange('logoUrl', e.target.value)}
                            placeholder="https://exemplo.com/logo.png"
                          />
                          
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <label htmlFor="primaryColor" className="block text-sm font-medium text-neutral-dark dark:text-neutral-light mb-1">
                                Cor Primária
                              </label>
                              <input
                                id="primaryColor"
                                type="color"
                                value={systemSettings.primaryColor}
                                onChange={(e) => handleSystemSettingChange('primaryColor', e.target.value)}
                                className="w-full h-10 rounded border border-gray-300 dark:border-neutral-medium"
                              />
                            </div>
                            <div>
                              <label htmlFor="secondaryColor" className="block text-sm font-medium text-neutral-dark dark:text-neutral-light mb-1">
                                Cor Secundária
                              </label>
                              <input
                                id="secondaryColor"
                                type="color"
                                value={systemSettings.secondaryColor}
                                onChange={(e) => handleSystemSettingChange('secondaryColor', e.target.value)}
                                className="w-full h-10 rounded border border-gray-300 dark:border-neutral-medium"
                              />
                            </div>
                          </div>
                        </div>
                      )}

                      {activeAdminTab === 'integrations' && (
                        <div className="space-y-6">
                          <div>
                            <h3 className="text-lg font-medium text-neutral-dark dark:text-neutral-light mb-4">Configurações de Email</h3>
                            <div className="space-y-4">
                              <div>
                                <label htmlFor="emailProvider" className="block text-sm font-medium text-neutral-dark dark:text-neutral-light mb-1">
                                  Provedor de Email
                                </label>
                                <select
                                  id="emailProvider"
                                  value={systemSettings.emailProvider}
                                  onChange={(e) => handleSystemSettingChange('emailProvider', e.target.value)}
                                  className="w-full px-3 py-2 border border-gray-300 dark:border-neutral-medium rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-neutral-dark dark:text-neutral-light"
                                >
                                  <option value="smtp">SMTP Personalizado</option>
                                  <option value="sendgrid">SendGrid</option>
                                  <option value="mailgun">Mailgun</option>
                                  <option value="ses">Amazon SES</option>
                                </select>
                              </div>
                              
                              {systemSettings.emailProvider === 'smtp' && (
                                <>
                                  <Input
                                    label="Servidor SMTP"
                                    value={systemSettings.smtpHost}
                                    onChange={(e) => handleSystemSettingChange('smtpHost', e.target.value)}
                                    placeholder="smtp.gmail.com"
                                  />
                                  <Input
                                    label="Porta SMTP"
                                    type="number"
                                    value={systemSettings.smtpPort}
                                    onChange={(e) => handleSystemSettingChange('smtpPort', parseInt(e.target.value))}
                                    placeholder="587"
                                  />
                                  <Input
                                    label="Usuário SMTP"
                                    value={systemSettings.smtpUser}
                                    onChange={(e) => handleSystemSettingChange('smtpUser', e.target.value)}
                                    placeholder="<EMAIL>"
                                  />
                                  <Input
                                    label="Senha SMTP"
                                    type="password"
                                    value={systemSettings.smtpPassword}
                                    onChange={(e) => handleSystemSettingChange('smtpPassword', e.target.value)}
                                    placeholder="••••••••"
                                  />
                                </>
                              )}
                            </div>
                          </div>

                          <div>
                            <h3 className="text-lg font-medium text-neutral-dark dark:text-neutral-light mb-4">APIs e Webhooks</h3>
                            <div className="space-y-4">
                              <div className="flex items-center justify-between">
                                <div>
                                  <span className="text-sm font-medium text-neutral-dark dark:text-neutral-light">API Habilitada</span>
                                  <p className="text-sm text-gray-500 dark:text-neutral-medium">Permitir acesso via API REST</p>
                                </div>
                                <label htmlFor="apiEnabled" className="relative inline-flex items-center cursor-pointer">
                                  <input
                                    id="apiEnabled"
                                    type="checkbox"
                                    checked={systemSettings.apiEnabled}
                                    onChange={(e) => handleSystemSettingChange('apiEnabled', e.target.checked)}
                                    className="sr-only peer"
                                    
                                  />
                                  <div className={`w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/25 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary`}></div>
                                </label>
                              </div>

                              <div className="flex items-center justify-between">
                                <div>
                                  <span className="text-sm font-medium text-neutral-dark dark:text-neutral-light">Webhooks Habilitados</span>
                                  <p className="text-sm text-gray-500 dark:text-neutral-medium">Enviar notificações para sistemas externos</p>
                                </div>
                                <label htmlFor="webhooksEnabled" className="relative inline-flex items-center cursor-pointer">
                                  <input
                                    id="webhooksEnabled"
                                    type="checkbox"
                                    checked={systemSettings.webhooksEnabled}
                                    onChange={(e) => handleSystemSettingChange('webhooksEnabled', e.target.checked)}
                                    className="sr-only peer"
                                  />
                                  <div className={`w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/25 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary`}></div>
                                </label>
                              </div>

                              {systemSettings.webhooksEnabled && (
                                <Input
                                  label="URL do Webhook"
                                  value={systemSettings.webhookUrl}
                                  onChange={(e) => handleSystemSettingChange('webhookUrl', e.target.value)}
                                  placeholder="https://seu-sistema.com/webhook"
                                />
                              )}
                            </div>
                          </div>
                        </div>
                      )}

                      {activeAdminTab === 'plans' && (
                        <div className="space-y-6">
                          <div className="flex items-center justify-between">
                            <div>
                              <h3 className="text-lg font-medium text-neutral-dark dark:text-neutral-light">Gerenciamento de Planos</h3>
                              <p className="text-sm text-gray-500 dark:text-neutral-medium">Configure os planos de assinatura disponíveis</p>
                            </div>
                            <Button
                              onClick={() => window.open('http://localhost:3002', '_blank')}
                              variant="secondary"
                            >
                              Abrir Dashboard de Planos
                            </Button>
                          </div>

                          <div className="flex items-center justify-between">
                            <div>
                              <span className="text-sm font-medium text-neutral-dark dark:text-neutral-light">Sistema de Planos Habilitado</span>
                              <p className="text-sm text-gray-500 dark:text-neutral-medium">Ativar sistema de assinaturas</p>
                            </div>
                            <label htmlFor="plansEnabled" className="relative inline-flex items-center cursor-pointer">
                              <input
                                id="plansEnabled"
                                type="checkbox"
                                checked={systemSettings.plansEnabled}
                                onChange={(e) => handleSystemSettingChange('plansEnabled', e.target.checked)}
                                className="sr-only peer"
                              />
                              <div className={`w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/25 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary`}></div>
                            </label>
                          </div>

                          <Input
                            label="Dias de Teste Gratuito"
                            type="number"
                            value={systemSettings.trialDays}
                            onChange={(e) => handleSystemSettingChange('trialDays', parseInt(e.target.value))}
                            placeholder="14"
                          />

                          <div className="bg-gray-50 dark:bg-neutral-darker p-4 rounded-lg">
                            <h4 className="font-medium text-neutral-dark dark:text-neutral-light mb-2">Planos Configurados</h4>
                            <div className="space-y-2">
                              {PLANS.map((plan) => (
                                <div key={plan.id} className="flex items-center justify-between text-sm">
                                  <span className="text-neutral-dark dark:text-neutral-light">{plan.name}</span>
                                  <span className="text-gray-500 dark:text-neutral-medium">
                                    R$ {plan.price.monthly}/mês
                                  </span>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      )}

                      {activeAdminTab === 'ai' && (
                        <div className="space-y-6">
                          <div>
                            <h3 className="text-lg font-medium text-neutral-dark dark:text-neutral-light mb-4">Configurações de IA</h3>
                            
                            <div className="flex items-center justify-between mb-4">
                              <div>
                                <span className="text-sm font-medium text-neutral-dark dark:text-neutral-light">IA Habilitada</span>
                                <p className="text-sm text-gray-500 dark:text-neutral-medium">Ativar recursos de inteligência artificial</p>
                              </div>
                              <label htmlFor="aiEnabled" className="relative inline-flex items-center cursor-pointer">
                                <input
                                  id="aiEnabled"
                                  type="checkbox"
                                  checked={systemSettings.aiEnabled}
                                  onChange={(e) => handleSystemSettingChange('aiEnabled', e.target.checked)}
                                  className="sr-only peer"
                                />
                                <div className={`w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/25 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary`}></div>
                              </label>
                            </div>

                            {systemSettings.aiEnabled && (
                              <div className="space-y-4">
                                <div>
                                  <label htmlFor="aiProvider" className="block text-sm font-medium text-neutral-dark dark:text-neutral-light mb-1">
                                    Provedor de IA
                                  </label>
                                  <select
                                    id="aiProvider"
                                    value={systemSettings.aiProvider}
                                    onChange={(e) => handleSystemSettingChange('aiProvider', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 dark:border-neutral-medium rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-neutral-dark dark:text-neutral-light"
                                  >
                                    <option value="gemini">Google Gemini</option>
                                    <option value="openai">OpenAI GPT</option>
                                    <option value="anthropic">Anthropic Claude</option>
                                  </select>
                                </div>

                                <Input
                                  label="Chave da API de IA"
                                  type="password"
                                  value={systemSettings.aiApiKey}
                                  onChange={(e) => handleSystemSettingChange('aiApiKey', e.target.value)}
                                  placeholder="••••••••••••••••••••••••••••••••"
                                />

                                <div className="bg-gray-50 dark:bg-neutral-darker p-4 rounded-lg">
                                  <h4 className="font-medium text-neutral-dark dark:text-neutral-light mb-2">Recursos de IA Disponíveis</h4>
                                  <div className="space-y-2">
                                    {AI_FEATURES.map((feature) => (
                                      <div key={feature.id} className="flex items-center justify-between text-sm">
                                        <div>
                                          <span className="text-neutral-dark dark:text-neutral-light">{feature.name}</span>
                                          <p className="text-xs text-gray-500 dark:text-neutral-medium">{feature.description}</p>
                                        </div>
                                        <span className={`px-2 py-1 rounded text-xs ${
                                          feature.enabled 
                                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                                            : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                                        }`}>
                                          {feature.enabled ? 'Ativo' : 'Inativo'}
                                        </span>
                                      </div>
                                    ))}
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {activeAdminTab === 'security' && (
                        <div className="space-y-6">
                          <h3 className="text-lg font-medium text-neutral-dark dark:text-neutral-light">Configurações de Segurança</h3>
                          
                          <div className="flex items-center justify-between">
                            <div>
                              <span className="text-sm font-medium text-neutral-dark dark:text-neutral-light">Autenticação de Dois Fatores Obrigatória</span>
                              <p className="text-sm text-gray-500 dark:text-neutral-medium">Exigir 2FA para todos os usuários</p>
                            </div>
                            <label htmlFor="twoFactorRequired" className="relative inline-flex items-center cursor-pointer">
                              <input
                                id="twoFactorRequired"
                                type="checkbox"
                                checked={systemSettings.twoFactorRequired}
                                onChange={(e) => handleSystemSettingChange('twoFactorRequired', e.target.checked)}
                                className="sr-only peer"
                              />
                              <div className={`w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/25 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary`}></div>
                            </label>
                          </div>

                          <Input
                            label="Timeout de Sessão (horas)"
                            type="number"
                            value={systemSettings.sessionTimeout}
                            onChange={(e) => handleSystemSettingChange('sessionTimeout', parseInt(e.target.value))}
                            placeholder="24"
                          />

                          <Input
                            label="Comprimento Mínimo da Senha"
                            type="number"
                            value={systemSettings.passwordMinLength}
                            onChange={(e) => handleSystemSettingChange('passwordMinLength', parseInt(e.target.value))}
                            placeholder="8"
                          />
                        </div>
                      )}

                      {activeAdminTab === 'backup' && (
                        <div className="space-y-6">
                          <h3 className="text-lg font-medium text-neutral-dark dark:text-neutral-light">Configurações de Backup</h3>
                          
                          <div className="flex items-center justify-between">
                            <div>
                              <span className="text-sm font-medium text-neutral-dark dark:text-neutral-light">Backup Automático</span>
                              <p className="text-sm text-gray-500 dark:text-neutral-medium">Realizar backups automáticos dos dados</p>
                            </div>
                            <label htmlFor="autoBackup" className="relative inline-flex items-center cursor-pointer">
                              <input
                                id="autoBackup"
                                type="checkbox"
                                checked={systemSettings.autoBackup}
                                onChange={(e) => handleSystemSettingChange('autoBackup', e.target.checked)}
                                className="sr-only peer"
                              />
                              <div className={`w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/25 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary`}></div>
                            </label>
                          </div>

                          {systemSettings.autoBackup && (
                            <>
                              <div>
                                <label htmlFor="backupFrequency" className="block text-sm font-medium text-neutral-dark dark:text-neutral-light mb-1">
                                  Frequência do Backup
                                </label>
                                <select
                                  id="backupFrequency"
                                  value={systemSettings.backupFrequency}
                                  onChange={(e) => handleSystemSettingChange('backupFrequency', e.target.value)}
                                  className="w-full px-3 py-2 border border-gray-300 dark:border-neutral-medium rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-neutral-dark dark:text-neutral-light"
                                >
                                  <option value="daily">Diário</option>
                                  <option value="weekly">Semanal</option>
                                  <option value="monthly">Mensal</option>
                                </select>
                              </div>

                              <Input
                                label="Retenção de Backup (dias)"
                                type="number"
                                value={systemSettings.backupRetention}
                                onChange={(e) => handleSystemSettingChange('backupRetention', parseInt(e.target.value))}
                                placeholder="30"
                              />
                            </>
                          )}

                          <div className="flex space-x-4">
                            <Button variant="secondary">
                              Fazer Backup Agora
                            </Button>
                            <Button variant="secondary">
                              Restaurar Backup
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="pt-6 border-t border-gray-200 dark:border-neutral-medium">
                      <Button onClick={handleSystemSettingsSave}>
                        Salvar Configurações do Sistema
                      </Button>
                    </div>
                  </div>
                </Card>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;