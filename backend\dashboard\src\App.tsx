import React, { Suspense } from 'react';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import Layout from './components/Layout';
import './App.css';

// Lazy load components for better code splitting
const PlansDashboard = React.lazy(() => import('./components/PlansDashboard'));
const OnlineClients = React.lazy(() => import('./components/OnlineClients'));
const AIFeatures = React.lazy(() => import('./components/AIFeatures'));
const AIInsights = React.lazy(() => import('./components/AIInsights'));
const SettingsPage = React.lazy(() => import('./components/SettingsPage'));
const PaymentsMonitor = React.lazy(() => import('./components/PaymentsMonitor'));
const StripeImport = React.lazy(() => import('./components/StripeImport'));

// Loading component
const LoadingSpinner: React.FC = () => (
  <div className="flex items-center justify-center h-64">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    <span className="ml-2 text-gray-600">Carregando...</span>
  </div>
);

function App() {
  const [currentPage, setCurrentPage] = React.useState('dashboard');
  const [darkMode] = React.useState(() => {
    const saved = localStorage.getItem('darkMode');
    return saved ? JSON.parse(saved) : false;
  });

  const renderPage = () => {
    switch (currentPage) {
      case 'dashboard':
        return (
          <Suspense fallback={<LoadingSpinner />}>
            <PlansDashboard />
          </Suspense>
        );
      case 'ai-features':
        return (
          <Suspense fallback={<LoadingSpinner />}>
            <AIFeatures />
          </Suspense>
        );
      case 'ai-insights':
        return (
          <Suspense fallback={<LoadingSpinner />}>
            <AIInsights darkMode={darkMode} />
          </Suspense>
        );
      case 'users':
        return (
          <Suspense fallback={<LoadingSpinner />}>
            <OnlineClients />
          </Suspense>
        );
      case 'payments':
        return (
          <Suspense fallback={<LoadingSpinner />}>
            <PaymentsMonitor />
          </Suspense>
        );
      case 'stripe-import':
        return (
          <Suspense fallback={<LoadingSpinner />}>
            <StripeImport />
          </Suspense>
        );
      case 'settings':
        return (
          <Suspense fallback={<LoadingSpinner />}>
            <SettingsPage />
          </Suspense>
        );
      default:
        return (
          <Suspense fallback={<LoadingSpinner />}>
            <PlansDashboard />
          </Suspense>
        );
    }
  };

  return (
    <AuthProvider>
      <div className="App">
        <ProtectedRoute requiredPermission="plans.read">
          <Layout currentPage={currentPage} onPageChange={setCurrentPage}>
            {renderPage()}
          </Layout>
        </ProtectedRoute>
      </div>
    </AuthProvider>
  );
}

export default App;