// Funções seguras para localStorage
export const saveToLocalStorage = (key: string, value: any): void => {
  try {
    const serializedValue = JSON.stringify(value);
    localStorage.setItem(key, serializedValue);
  } catch (error) {
    console.error(`Error saving to localStorage (key: ${key}):`, error);
  }
};

export const getFromLocalStorage = <T>(key: string, defaultValue: T): T => {
  try {
    const serializedValue = localStorage.getItem(key);
    if (serializedValue === null) {
      return defaultValue;
    }
    return JSON.parse(serializedValue) as T;
  } catch (error) {
    console.error(`Error reading from localStorage (key: ${key}):`, error);
    return defaultValue;
  }
};

// Funções seguras para sessionStorage
export const saveToSessionStorage = (key: string, value: any): void => {
  try {
    const serializedValue = JSON.stringify(value);
    sessionStorage.setItem(key, serializedValue);
  } catch (error) {
    console.error(`Error saving to sessionStorage (key: ${key}):`, error);
  }
};

export const getFromSessionStorage = <T>(key: string, defaultValue: T): T => {
  try {
    const serializedValue = sessionStorage.getItem(key);
    if (serializedValue === null) {
      return defaultValue;
    }
    return JSON.parse(serializedValue) as T;
  } catch (error) {
    console.error(`Error reading from sessionStorage (key: ${key}):`, error);
    return defaultValue;
  }
};

// Add this helper function to safely handle storage events
export const handleStorageEvent = (event: StorageEvent): any => {
  if (!event.newValue) return null;

  try {
    return JSON.parse(event.newValue);
  } catch (error) {
    console.error(`Error parsing storage value for key ${event.key}:`, error);
    return null;
  }
};

// Enhanced storage functions with better error handling and extension compatibility
export const safeSetItem = (key: string, value: any): boolean => {
  try {
    // Ensure we're always storing strings
    const serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
    localStorage.setItem(key, serializedValue);

    // Dispatch a custom event to avoid conflicts with extensions
    window.dispatchEvent(new CustomEvent('app-storage-change', {
      detail: { key, value: serializedValue, type: 'set' }
    }));

    return true;
  } catch (error) {
    console.error(`Error setting localStorage item (key: ${key}):`, error);
    return false;
  }
};

export const safeGetItem = <T>(key: string, defaultValue: T): T => {
  try {
    const item = localStorage.getItem(key);
    if (item === null) return defaultValue;

    // Try to parse as JSON, fallback to string
    try {
      return JSON.parse(item) as T;
    } catch {
      // If parsing fails, return as string if T is string, otherwise default
      return (typeof defaultValue === 'string' ? item : defaultValue) as T;
    }
  } catch (error) {
    console.error(`Error getting localStorage item (key: ${key}):`, error);
    return defaultValue;
  }
};

export const safeRemoveItem = (key: string): boolean => {
  try {
    localStorage.removeItem(key);

    // Dispatch custom event
    window.dispatchEvent(new CustomEvent('app-storage-change', {
      detail: { key, value: null, type: 'remove' }
    }));

    return true;
  } catch (error) {
    console.error(`Error removing localStorage item (key: ${key}):`, error);
    return false;
  }
};

