import React, { useState, useEffect } from 'react';
import { 
  Users, 
  Building2, 
  DollarSign, 
  TrendingUp, 
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Mail,
  Phone,
  Globe,
  BarChart3,
  PieChart,
  Settings,
  Download,
  Filter,
  Search,
  Plus,
  Eye,
  Edit,
  Trash2,
  ExternalLink
} from 'lucide-react';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';

// Interfaces
interface SystemMetrics {
  totalUsers: number;
  activeUsers: number;
  totalOrganizations: number;
  monthlyRevenue: number;
  conversionRate: number;
  systemHealth: 'healthy' | 'warning' | 'critical';
  landingPageViews: number;
  demoRequests: number;
  trialSignups: number;
}

interface LeadData {
  id: string;
  name: string;
  email: string;
  phone?: string;
  organization?: string;
  source: 'landing_page' | 'referral' | 'direct' | 'campaign';
  status: 'new' | 'contacted' | 'qualified' | 'converted' | 'lost';
  createdAt: string;
  lastContact?: string;
  notes?: string;
  planInterest?: 'basic' | 'standard' | 'professional';
}

interface OrganizationData {
  id: string;
  name: string;
  plan: 'basic' | 'standard' | 'professional';
  users: number;
  status: 'active' | 'trial' | 'suspended' | 'cancelled';
  monthlyRevenue: number;
  createdAt: string;
  lastActivity: string;
  contactEmail: string;
}

interface AdminDashboardProps {
  userRole: 'super_admin' | 'admin' | 'manager';
}

const AdminDashboard: React.FC<AdminDashboardProps> = ({ userRole }) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'leads' | 'organizations' | 'analytics' | 'settings'>('overview');
  const [metrics, setMetrics] = useState<SystemMetrics>({
    totalUsers: 1247,
    activeUsers: 892,
    totalOrganizations: 156,
    monthlyRevenue: 89750,
    conversionRate: 12.5,
    systemHealth: 'healthy',
    landingPageViews: 5420,
    demoRequests: 89,
    trialSignups: 34
  });

  const [leads, setLeads] = useState<LeadData[]>([
    {
      id: '1',
      name: 'Maria Silva',
      email: '<EMAIL>',
      phone: '(11) 99999-9999',
      organization: 'Prefeitura de São Paulo',
      source: 'landing_page',
      status: 'new',
      createdAt: '2024-01-15T10:30:00Z',
      planInterest: 'professional'
    },
    {
      id: '2',
      name: 'João Santos',
      email: '<EMAIL>',
      phone: '(21) 88888-8888',
      organization: 'Câmara Municipal RJ',
      source: 'referral',
      status: 'qualified',
      createdAt: '2024-01-14T15:45:00Z',
      lastContact: '2024-01-15T09:00:00Z',
      planInterest: 'standard'
    }
  ]);

  const [organizations, setOrganizations] = useState<OrganizationData[]>([
    {
      id: '1',
      name: 'Prefeitura de Belo Horizonte',
      plan: 'professional',
      users: 25,
      status: 'active',
      monthlyRevenue: 449,
      createdAt: '2023-12-01T00:00:00Z',
      lastActivity: '2024-01-15T14:30:00Z',
      contactEmail: '<EMAIL>'
    },
    {
      id: '2',
      name: 'Câmara Municipal de Curitiba',
      plan: 'standard',
      users: 12,
      status: 'trial',
      monthlyRevenue: 0,
      createdAt: '2024-01-10T00:00:00Z',
      lastActivity: '2024-01-15T11:20:00Z',
      contactEmail: '<EMAIL>'
    }
  ]);

  // Funções de utilidade
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const getStatusColor = (status: string) => {
    const colors = {
      active: 'text-green-600 bg-green-100',
      trial: 'text-blue-600 bg-blue-100',
      suspended: 'text-yellow-600 bg-yellow-100',
      cancelled: 'text-red-600 bg-red-100',
      new: 'text-blue-600 bg-blue-100',
      contacted: 'text-yellow-600 bg-yellow-100',
      qualified: 'text-green-600 bg-green-100',
      converted: 'text-green-600 bg-green-100',
      lost: 'text-red-600 bg-red-100',
      healthy: 'text-green-600',
      warning: 'text-yellow-600',
      critical: 'text-red-600'
    };
    return colors[status as keyof typeof colors] || 'text-gray-600 bg-gray-100';
  };

  // Componente de Métricas
  const MetricsOverview = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <Card className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">Usuários Totais</p>
            <p className="text-2xl font-bold text-gray-900">{metrics.totalUsers.toLocaleString()}</p>
            <p className="text-sm text-green-600">+12% este mês</p>
          </div>
          <div className="p-3 bg-blue-100 rounded-full">
            <Users className="h-6 w-6 text-blue-600" />
          </div>
        </div>
      </Card>

      <Card className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">Organizações</p>
            <p className="text-2xl font-bold text-gray-900">{metrics.totalOrganizations}</p>
            <p className="text-sm text-green-600">+8% este mês</p>
          </div>
          <div className="p-3 bg-green-100 rounded-full">
            <Building2 className="h-6 w-6 text-green-600" />
          </div>
        </div>
      </Card>

      <Card className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">Receita Mensal</p>
            <p className="text-2xl font-bold text-gray-900">{formatCurrency(metrics.monthlyRevenue)}</p>
            <p className="text-sm text-green-600">+15% este mês</p>
          </div>
          <div className="p-3 bg-yellow-100 rounded-full">
            <DollarSign className="h-6 w-6 text-yellow-600" />
          </div>
        </div>
      </Card>

      <Card className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">Taxa de Conversão</p>
            <p className="text-2xl font-bold text-gray-900">{metrics.conversionRate}%</p>
            <p className="text-sm text-green-600">+2.3% este mês</p>
          </div>
          <div className="p-3 bg-purple-100 rounded-full">
            <TrendingUp className="h-6 w-6 text-purple-600" />
          </div>
        </div>
      </Card>
    </div>
  );

  // Componente de Landing Page Analytics
  const LandingPageAnalytics = () => (
    <Card className="p-6 mb-8">
      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
        <Globe className="h-5 w-5 mr-2" />
        Analytics da Landing Page
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="text-center">
          <p className="text-3xl font-bold text-blue-600">{metrics.landingPageViews.toLocaleString()}</p>
          <p className="text-sm text-gray-600">Visualizações este mês</p>
        </div>
        <div className="text-center">
          <p className="text-3xl font-bold text-green-600">{metrics.demoRequests}</p>
          <p className="text-sm text-gray-600">Solicitações de demo</p>
        </div>
        <div className="text-center">
          <p className="text-3xl font-bold text-purple-600">{metrics.trialSignups}</p>
          <p className="text-sm text-gray-600">Cadastros para trial</p>
        </div>
      </div>
      <div className="mt-4 flex justify-end">
        <Button variant="outline" size="sm">
          <ExternalLink className="h-4 w-4 mr-2" />
          Ver Landing Page
        </Button>
      </div>
    </Card>
  );

  // Componente de Status do Sistema
  const SystemStatus = () => (
    <Card className="p-6 mb-8">
      <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
        <Activity className="h-5 w-5 mr-2" />
        Status do Sistema
      </h3>
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <div className={`w-3 h-3 rounded-full mr-3 ${
            metrics.systemHealth === 'healthy' ? 'bg-green-500' :
            metrics.systemHealth === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
          }`}></div>
          <span className={`font-medium ${getStatusColor(metrics.systemHealth)}`}>
            {metrics.systemHealth === 'healthy' ? 'Sistema Operacional' :
             metrics.systemHealth === 'warning' ? 'Atenção Necessária' : 'Problemas Críticos'}
          </span>
        </div>
        <div className="text-sm text-gray-600">
          Última verificação: {new Date().toLocaleTimeString('pt-BR')}
        </div>
      </div>
    </Card>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Dashboard Administrativo</h1>
              <p className="text-gray-600">Gerenciamento do Sistema Promandato</p>
            </div>
            <div className="flex items-center space-x-4">
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Exportar Dados
              </Button>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Configurações
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-8">
            {[
              { id: 'overview', label: 'Visão Geral', icon: BarChart3 },
              { id: 'leads', label: 'Leads', icon: Users },
              { id: 'organizations', label: 'Organizações', icon: Building2 },
              { id: 'analytics', label: 'Analytics', icon: PieChart },
              { id: 'settings', label: 'Configurações', icon: Settings }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4 mr-2" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'overview' && (
          <div>
            <MetricsOverview />
            <LandingPageAnalytics />
            <SystemStatus />
          </div>
        )}

        {activeTab === 'leads' && (
          <div>
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold text-gray-900">Gerenciamento de Leads</h2>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Novo Lead
              </Button>
            </div>
            
            {/* Filtros e Busca */}
            <Card className="p-4 mb-6">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Buscar leads..."
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>
                <select 
                  aria-label="Filtrar por status"
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                  <option value="">Todos os status</option>
                  <option value="new">Novo</option>
                  <option value="contacted">Contatado</option>
                  <option value="qualified">Qualificado</option>
                  <option value="converted">Convertido</option>
                  <option value="lost">Perdido</option>
                </select>
                <select 
                  aria-label="Filtrar por fonte"
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                  <option value="">Todas as fontes</option>
                  <option value="landing_page">Landing Page</option>
                  <option value="referral">Indicação</option>
                  <option value="direct">Direto</option>
                  <option value="campaign">Campanha</option>
                </select>
              </div>
            </Card>

            {/* Tabela de Leads */}
            <Card>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Lead
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Organização
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Fonte
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Plano de Interesse
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Data
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Ações
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {leads.map((lead) => (
                      <tr key={lead.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">{lead.name}</div>
                            <div className="text-sm text-gray-500 flex items-center">
                              <Mail className="h-3 w-3 mr-1" />
                              {lead.email}
                            </div>
                            {lead.phone && (
                              <div className="text-sm text-gray-500 flex items-center">
                                <Phone className="h-3 w-3 mr-1" />
                                {lead.phone}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {lead.organization || '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {lead.source}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(lead.status)}`}>
                            {lead.status}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {lead.planInterest ? (
                            <span className="capitalize">{lead.planInterest}</span>
                          ) : '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(lead.createdAt)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <button className="text-blue-600 hover:text-blue-900">
                              <Eye className="h-4 w-4" />
                            </button>
                            <button className="text-green-600 hover:text-green-900">
                              <Edit className="h-4 w-4" />
                            </button>
                            <button className="text-red-600 hover:text-red-900">
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </Card>
          </div>
        )}

        {/* Outras abas serão implementadas em seguida */}
        {activeTab !== 'overview' && activeTab !== 'leads' && (
          <div className="text-center py-12">
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {activeTab === 'organizations' && 'Gerenciamento de Organizações'}
              {activeTab === 'analytics' && 'Analytics Avançados'}
              {activeTab === 'settings' && 'Configurações do Sistema'}
            </h3>
            <p className="text-gray-600">Esta seção está em desenvolvimento.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminDashboard;
