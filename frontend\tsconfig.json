{"compilerOptions": {"target": "ES2020", "experimentalDecorators": true, "useDefineForClassFields": false, "forceConsistentCasingInFileNames": true, "module": "ESNext", "lib": ["ES2020", "DOM", "DOM.Iterable"], "skipLibCheck": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "moduleDetection": "force", "noEmit": true, "allowJs": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "noUncheckedSideEffectImports": true, "paths": {"@/*": ["./src/*"]}, "types": ["vite/client", "node"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "vite.config.ts"], "exclude": ["node_modules", "dist"]}