#!/usr/bin/env node

/**
 * Script para corrigir erros do console e limpar cache
 */

import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

console.log('🔧 Iniciando correção de erros do console...\n');

async function main() {
  try {
    // 1. Limpar cache do Vite
    console.log('1. Limpando cache do Vite...');
    const viteCacheDir = path.join(projectRoot, 'node_modules', '.vite');
    if (await fs.pathExists(viteCacheDir)) {
      await fs.remove(viteCacheDir);
      console.log('   ✅ Cache do Vite removido');
    } else {
      console.log('   ℹ️  Cache do Vite não encontrado');
    }

    // 2. Limpar dist
    console.log('\n2. Limpando diretório dist...');
    const distDir = path.join(projectRoot, 'dist');
    if (await fs.pathExists(distDir)) {
      await fs.remove(distDir);
      console.log('   ✅ Diretório dist removido');
    } else {
      console.log('   ℹ️  Diretório dist não encontrado');
    }

    // 3. Garantir que o diretório public/assets existe
    console.log('\n3. Verificando estrutura de assets...');
    const publicAssetsDir = path.join(projectRoot, 'public', 'assets');
    await fs.ensureDir(publicAssetsDir);
    console.log('   ✅ Diretório public/assets criado/verificado');

    // 4. Verificar se critical.css existe
    const criticalCssPath = path.join(publicAssetsDir, 'critical.css');
    if (!(await fs.pathExists(criticalCssPath))) {
      console.log('   ⚠️  critical.css não encontrado, criando...');
      
      const criticalCss = `/* Critical CSS for initial page load */
* { box-sizing: border-box; }
body { 
  margin: 0; 
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
}
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}`;
      
      await fs.writeFile(criticalCssPath, criticalCss);
      console.log('   ✅ critical.css criado');
    } else {
      console.log('   ✅ critical.css já existe');
    }

    // 5. Verificar configuração do Vite
    console.log('\n4. Verificando configuração do Vite...');
    const viteConfigPath = path.join(projectRoot, 'vite.config.ts');
    if (await fs.pathExists(viteConfigPath)) {
      console.log('   ✅ vite.config.ts encontrado');
    } else {
      console.log('   ⚠️  vite.config.ts não encontrado');
    }

    console.log('\n✅ Correções aplicadas com sucesso!');
    console.log('\n📋 Próximos passos:');
    console.log('   1. Execute: npm run build');
    console.log('   2. Execute: npm run dev');
    console.log('   3. Abra o navegador em http://localhost:3002');
    console.log('\n💡 Se os erros persistirem:');
    console.log('   - Limpe o cache do navegador (Ctrl+Shift+R)');
    console.log('   - Desative extensões do navegador temporariamente');
    console.log('   - Verifique o console para novos erros');

  } catch (error) {
    console.error('❌ Erro durante a correção:', error);
    process.exit(1);
  }
}

main();
