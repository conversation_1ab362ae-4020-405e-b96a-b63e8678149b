import React from 'react';
import { usePlan } from '../../hooks/usePlan';
import { AI_FEATURES, AIFeature, PlanType } from '../../types/plans';

interface AIUsageDashboardProps {
  className?: string;
}

export const AIUsageDashboard: React.FC<AIUsageDashboardProps> = ({ className = '' }) => {
  const { planType, getAIFeatureUsage, getRemainingUsage, canUseAIFeature } = usePlan();

  if (planType !== PlanType.PROFESSIONAL) {
    return (
      <div className={`bg-gradient-to-r from-secondary-light to-secondary p-6 rounded-lg ${className}`}>
        <div className="text-center">
          <div className="text-4xl mb-4">🤖</div>
          <h3 className="text-xl font-bold text-neutral-dark mb-2">
            Desbloqueie o Poder da IA
          </h3>
          <p className="text-neutral-dark mb-4">
            Upgrade para o Plano Enterprise e tenha acesso a recursos exclusivos de Inteligência Artificial
          </p>
          <button type="button" className="bg-neutral-dark text-white px-6 py-2 rounded-lg hover:bg-neutral-darker transition-colors">
            Fazer Upgrade
          </button>
        </div>
      </div>
    );
  }

  const getUsagePercentage = (featureId: string): number => {
    const usage = getAIFeatureUsage(featureId);
    if (!usage || usage.limit === -1) return 0;
    return (usage.used / usage.limit) * 100;
  };

  const getUsageColor = (percentage: number): string => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 70) return 'bg-yellow-500';
    return 'bg-primary';
  };

  return (
    <div className={`bg-white dark:bg-neutral-darker rounded-lg shadow-lg p-6 ${className}`}>
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-bold text-neutral-dark dark:text-neutral-light">
          🤖 Uso de IA - Plano Enterprise
        </h3>
        <span className="bg-secondary text-neutral-dark px-3 py-1 rounded-full text-sm font-medium">
          IA Ativa
        </span>
      </div>

      <div className="grid gap-4">
        {AI_FEATURES.map((feature: AIFeature) => {
          const usage = getAIFeatureUsage(feature.id);
          const canUse = canUseAIFeature(feature.id);
          const percentage = getUsagePercentage(feature.id);
          const remaining = getRemainingUsage(feature.id);

          return (
            <div
              key={feature.id}
              className="border border-neutral-light dark:border-neutral-dark rounded-lg p-4"
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <h4 className="font-semibold text-neutral-dark dark:text-neutral-light mb-1">
                    {feature.name}
                  </h4>
                  <p className="text-sm text-neutral-medium">
                    {feature.description}
                  </p>
                </div>
                <div className={`ml-4 px-2 py-1 rounded text-xs font-medium ${
                  canUse 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
                    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                }`}>
                  {canUse ? 'Disponível' : 'Limite Atingido'}
                </div>
              </div>

              {usage && usage.limit !== -1 && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-neutral-medium">
                      Usado: {usage.used} / {usage.limit}
                    </span>
                    <span className="text-neutral-medium">
                      {remaining} restantes
                    </span>
                  </div>
                  
                  <div className="w-full bg-neutral-light dark:bg-neutral-dark rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${getUsageColor(percentage)}`}
                      style={{ width: `${Math.min(percentage, 100)}%` }}
                    />
                  </div>
                  
                  <div className="text-xs text-neutral-medium">
                    {percentage.toFixed(1)}% utilizado
                  </div>
                </div>
              )}

              {usage && usage.limit === -1 && (
                <div className="text-sm text-neutral-medium">
                  <span className="text-primary font-medium">Uso ilimitado</span> - 
                  {usage.used} utilizações este mês
                </div>
              )}
            </div>
          );
        })}
      </div>

      <div className="mt-6 p-4 bg-neutral-light dark:bg-neutral-dark rounded-lg">
        <h4 className="font-semibold text-neutral-dark dark:text-neutral-light mb-2">
          💡 Dicas para Otimizar o Uso de IA
        </h4>
        <ul className="text-sm text-neutral-medium space-y-1">
          <li>• Use a análise de sentimento para priorizar demandas urgentes</li>
          <li>• Configure a geração de conteúdo para posts automáticos</li>
          <li>• Ative insights de cidadãos para melhor segmentação</li>
          <li>• Use análise preditiva para antecipar problemas</li>
        </ul>
      </div>
    </div>
  );
};

export default AIUsageDashboard;