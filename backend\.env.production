# Configurações de Produção - Pro-Mandato Backend

# URLs de produção
FRONTEND_URL=https://promandato-backend-2h6tsoanrq-rj.a.run.app,https://promandato-9a4cf.web.app,https://promandato-9a4cf.firebaseapp.com,https://app.promandato.com.br,https://promandato.com.br,https://www.promandato.com.br
BACKEND_URL=https://promandato-backend-517140455601.southamerica-east1.run.app
LANDING_URL=https://promandato-9a4cf.web.app

# CORS - URLs permitidas (separadas por vírgula)
CORS_ORIGIN=https://promandato-backend-2h6tsoanrq-rj.a.run.app,https://promandato-9a4cf.web.app,https://promandato-9a4cf.firebaseapp.com,https://app.promandato.com.br,https://promandato.com.br,https://www.promandato.com.br

# APIs de IA
GEMINI_API_KEY=AIzaSyBd-hxAPIFnx9zXNM5tPFAalsYk2sGsUpE
OPENAI_API_KEY=proj-OqxfLzzAfuN36TLCak9ER77k3zwq-bFi8OhP3ebpbKUAizBqXs-Vpe-OE5b8p9KxX8Q9C9aRGAT3BlbkFJLTb9m81esDLqxr_XGC8UWl7t11S3zPL8ypocG6rFiOjVFAs

# Stripe
STRIPE_API_KEY=pk_live_51QUu2mClUIoqY19kQholKzLBzhCKuYnrCqGAQPtJL3vfvp3BcuyhGopNqirFP9DzOcp1GMMPnoHCibwMIOGBroQq00Frc8LxxN
STRIPE_SECRET_KEY=***********************************************************************************************************
STRIPE_WEBHOOK_SECRET=whsec_test_webhook_secret

# Email (produção)
EMAIL_SERVICE=production
EMAIL_FROM=<EMAIL>

# JWT
JWT_SECRET=promandato-super-secret-key-2024

# Servidor
NODE_ENV=production
PORT=8080

# Configurações de segurança para produção
TRUST_PROXY=true
SECURE_COOKIES=true
