rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Funções auxiliares
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isImageFile() {
      return request.resource.contentType.matches('image/.*');
    }
    
    function isDocumentFile() {
      return request.resource.contentType.matches('application/pdf') ||
             request.resource.contentType.matches('application/msword') ||
             request.resource.contentType.matches('application/vnd.openxmlformats-officedocument.*') ||
             request.resource.contentType.matches('text/plain');
    }
    
    function isValidFileSize(maxSizeMB) {
      return request.resource.size <= maxSizeMB * 1024 * 1024;
    }
    
    // Regra para leitura pública de arquivos
    match /{allPaths=**} {
      allow read: if true;
    }
    
    // Regras para fotos de políticos
    match /politician_photos/{userId}/{fileName} {
      allow write: if (
        // Usuário está autenticado
        isAuthenticated() &&
        // É o próprio usuário ou um administrador
        (request.auth.uid == userId || 
         firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'admin' ||
         firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'master') &&
        // Arquivo é uma imagem
        isImageFile() &&
        // Tamanho máximo de 5MB
        isValidFileSize(5)
      );
    }
    
    // Regras para documentos
    match /documents/{userId}/{fileName} {
      allow write: if (
        isAuthenticated() &&
        (request.auth.uid == userId || 
         firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'admin' ||
         firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'master') &&
        isDocumentFile() &&
        isValidFileSize(10)
      );
    }
    
    // Regras para mídias sociais
    match /social_media/{userId}/{fileName} {
      allow write: if (
        isAuthenticated() &&
        (request.auth.uid == userId || 
         firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'admin' ||
         firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'master') &&
        (isImageFile() || request.resource.contentType.matches('video/.*')) &&
        isValidFileSize(20)
      );
    }
    
    // Regras para fotos de perfil de usuários
    match /profile_photos/{userId}/{fileName} {
      allow write: if (
        isAuthenticated() &&
        request.auth.uid == userId &&
        isImageFile() &&
        isValidFileSize(2)
      );
    }
    
    // Regras para fotos de cidadãos
    match /citizen_photos/{userId}/{fileName} {
      allow write: if (
        isAuthenticated() &&
        isImageFile() &&
        isValidFileSize(5)
      );
    }
  }
}
