import Organization from '../models/Organization.js';

/**
 * Middleware para verificar limites do plano
 */
export const checkPlanLimit = (limitType) => {
  return async (req, res, next) => {
    try {
      // Se for admin global, não tem limites
      if (req.user && req.user.role === 'ADMIN' && !req.user.organizationId) {
        return next();
      }

      // Verificar se o usuário tem organização
      if (!req.user || !req.user.organizationId) {
        return res.status(403).json({
          success: false,
          message: 'Usuário não está associado a uma organização'
        });
      }

      const organization = new Organization();
      const limitCheck = await organization.checkLimits(req.user.organizationId, limitType);

      if (!limitCheck.allowed) {
        return res.status(403).json({
          success: false,
          message: `Limite do plano atingido para ${limitType}`,
          limit: limitCheck.limit,
          currentUsage: limitCheck.currentUsage,
          remaining: limitCheck.remaining
        });
      }

      // Adicionar informações do limite ao request
      req.planLimit = limitCheck;
      next();
    } catch (error) {
      console.error('Erro ao verificar limite do plano:', error);
      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      });
    }
  };
};

/**
 * Middleware para verificar se uma funcionalidade está disponível no plano
 */
export const checkPlanFeature = (featureId) => {
  return async (req, res, next) => {
    try {
      // Se for admin global, tem acesso a tudo
      if (req.user && req.user.role === 'ADMIN' && !req.user.organizationId) {
        return next();
      }

      // Verificar se o usuário tem organização
      if (!req.user || !req.user.organizationId) {
        return res.status(403).json({
          success: false,
          message: 'Usuário não está associado a uma organização'
        });
      }

      const organizationModel = new Organization();
      const org = await organizationModel.getById(req.user.organizationId);

      if (!org) {
        return res.status(404).json({
          success: false,
          message: 'Organização não encontrada'
        });
      }

      // Verificar se a funcionalidade está disponível
      const hasFeature = org.features[featureId] === true;
      
      if (!hasFeature) {
        return res.status(403).json({
          success: false,
          message: `Funcionalidade '${featureId}' não disponível no seu plano`,
          currentPlan: org.planId,
          availableFeatures: Object.keys(org.features).filter(key => org.features[key] === true)
        });
      }

      next();
    } catch (error) {
      console.error('Erro ao verificar funcionalidade do plano:', error);
      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      });
    }
  };
};

/**
 * Middleware para verificar funcionalidades de IA
 */
export const checkAIFeature = (aiFeatureId) => {
  return async (req, res, next) => {
    try {
      // Se for admin global, tem acesso a tudo
      if (req.user && req.user.role === 'ADMIN' && !req.user.organizationId) {
        return next();
      }

      // Verificar se o usuário tem organização
      if (!req.user || !req.user.organizationId) {
        return res.status(403).json({
          success: false,
          message: 'Usuário não está associado a uma organização'
        });
      }

      const organizationModel = new Organization();
      const org = await organizationModel.getById(req.user.organizationId);

      if (!org) {
        return res.status(404).json({
          success: false,
          message: 'Organização não encontrada'
        });
      }

      // Verificar se a funcionalidade de IA está disponível
      const aiFeature = org.features.aiFeatures?.find(feature => 
        feature.id === aiFeatureId && feature.enabled
      );
      
      if (!aiFeature) {
        const planNames = {
          basic: 'Básico',
          standard: 'Padrão', 
          professional: 'Profissional'
        };

        let upgradeMessage = '';
        if (org.planId === 'basic') {
          upgradeMessage = 'Funcionalidades de IA estão disponíveis nos planos Padrão e Profissional.';
        } else if (org.planId === 'standard' && !['sentiment-analysis'].includes(aiFeatureId)) {
          upgradeMessage = 'Esta funcionalidade de IA está disponível apenas no plano Profissional.';
        }

        return res.status(403).json({
          success: false,
          message: `Funcionalidade de IA '${aiFeatureId}' não disponível no seu plano`,
          currentPlan: planNames[org.planId] || org.planId,
          upgradeMessage,
          availableAIFeatures: org.features.aiFeatures?.map(f => f.id) || []
        });
      }

      // Adicionar informações da funcionalidade ao request
      req.aiFeature = aiFeature;
      next();
    } catch (error) {
      console.error('Erro ao verificar funcionalidade de IA:', error);
      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      });
    }
  };
};

/**
 * Middleware para incrementar uso de uma funcionalidade
 */
export const incrementUsage = (usageType) => {
  return async (req, res, next) => {
    try {
      // Se for admin global, não conta uso
      if (req.user && req.user.role === 'ADMIN' && !req.user.organizationId) {
        return next();
      }

      if (req.user && req.user.organizationId) {
        const organization = new Organization();
        await organization.updateUsage(req.user.organizationId, usageType, 1);
      }

      next();
    } catch (error) {
      console.error('Erro ao incrementar uso:', error);
      // Não bloquear a requisição por erro de contagem
      next();
    }
  };
};

/**
 * Middleware para obter informações do plano atual
 */
export const getPlanInfo = async (req, res, next) => {
  try {
    if (req.user && req.user.organizationId) {
      const organizationModel = new Organization();
      const org = await organizationModel.getById(req.user.organizationId);
      
      if (org) {
        req.planInfo = {
          planId: org.planId,
          limits: org.limits,
          usage: org.usage,
          features: org.features,
          organization: {
            id: org.id,
            name: org.name,
            slug: org.slug
          }
        };
      }
    }
    
    next();
  } catch (error) {
    console.error('Erro ao obter informações do plano:', error);
    next();
  }
};

export default {
  checkPlanLimit,
  checkPlanFeature,
  checkAIFeature,
  incrementUsage,
  getPlanInfo
};
