import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { ROUTE_PATHS, ICONS } from '../../constants';

interface BreadcrumbItem {
  label: string;
  path?: string;
  icon?: React.ReactElement;
}

interface BreadcrumbsProps {
  items?: BreadcrumbItem[];
  className?: string;
}

export const Breadcrumbs: React.FC<BreadcrumbsProps> = ({ items, className = "" }) => {
  const location = useLocation();

  // Auto-generate breadcrumbs based on current path if items not provided
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const pathSegments = location.pathname.split('/').filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [
      { label: 'Início', path: ROUTE_PATHS.DASHBOARD, icon: ICONS.DASHBOARD }
    ];

    // Map path segments to readable labels
    const pathLabels: Record<string, { label: string; icon?: React.ReactElement }> = {
      'demands': { label: 'Demandas', icon: ICONS.DEMANDS },
      'citizens': { label: 'Cidadãos', icon: ICONS.CITIZENS },
      'agenda': { label: 'Agenda', icon: ICONS.AGENDA },
      'documents': { label: 'Documentos', icon: ICONS.DOCUMENTS },
      'social-media': { label: 'Redes Sociais', icon: ICONS.SOCIAL_MEDIA },
      'reports': { label: 'Relatórios', icon: ICONS.REPORTS },
      'settings': { label: 'Configurações', icon: ICONS.SETTINGS },
      'profile': { label: 'Perfil', icon: ICONS.USER_CIRCLE },
      'team': { label: 'Equipe', icon: ICONS.TEAM },
      'workspace-settings': { label: 'Local de Trabalho', icon: ICONS.BUILDING_OFFICE },
      'politician-profile': { label: 'Perfil do Político', icon: ICONS.POLITICIAN },
      'bulk-messages': { label: 'Mensageria', icon: ICONS.MAIL },
      'new': { label: 'Novo' },
      'edit': { label: 'Editar' }
    };

    let currentPath = '';
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      
      // Skip ID segments (numeric values)
      if (/^\d+$/.test(segment)) {
        return;
      }

      const pathInfo = pathLabels[segment];
      if (pathInfo) {
        breadcrumbs.push({
          label: pathInfo.label,
          path: index === pathSegments.length - 1 ? undefined : currentPath,
          icon: pathInfo.icon
        });
      }
    });

    return breadcrumbs;
  };

  const breadcrumbItems = items || generateBreadcrumbs();

  if (breadcrumbItems.length <= 1) {
    return null; // Don't show breadcrumbs if only home
  }

  return (
    <nav className={`flex ${className}`} aria-label="Breadcrumb">
      <ol className="inline-flex items-center space-x-1 md:space-x-3">
        {breadcrumbItems.map((item, index) => (
          <li key={index} className="inline-flex items-center">
            {index > 0 && (
              <svg
                className="w-6 h-6 text-gray-400 dark:text-neutral-medium"
                fill="currentColor"
                viewBox="0 0 20 20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  fillRule="evenodd"
                  d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                  clipRule="evenodd"
                />
              </svg>
            )}
            
            {item.path ? (
              <Link
                to={item.path}
                className="inline-flex items-center text-sm font-medium text-gray-700 dark:text-neutral-DEFAULT hover:text-primary dark:hover:text-primary-light"
              >
                {item.icon && (
                  <span className="mr-2">
                    {React.cloneElement(item.icon, { className: 'w-4 h-4' } as any)}
                  </span>
                )}
                {item.label}
              </Link>
            ) : (
              <span className="inline-flex items-center text-sm font-medium text-gray-500 dark:text-neutral-medium">
                {item.icon && (
                  <span className="mr-2">
                    {React.cloneElement(item.icon, { className: 'w-4 h-4' } as any)}
                  </span>
                )}
                {item.label}
              </span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};