/**
 * Serviço para integração com Stripe
 * Centraliza todas as operações relacionadas a pagamentos
 */

import { loadStripe, Stripe } from '@stripe/stripe-js';
import ENV_CONFIG from '../config/environment';
import { apiService, ApiError } from './apiService';

export interface PlanType {
  id: 'basic' | 'standard' | 'professional';
  name: string;
  description: string;
  features: string[];
  prices: {
    monthly: number;
    yearly: number;
  };
}

export interface CheckoutSessionData {
  planType: string;
  billingCycle: 'monthly' | 'yearly';
  leadData?: {
    name: string;
    email: string;
    phone?: string;
    company?: string;
  };
  successUrl?: string;
  cancelUrl?: string;
}

export interface StripeSession {
  id: string;
  url: string;
}

class StripeService {
  private stripe: Stripe | null = null;
  private isInitialized = false;

  /**
   * Inicializar Stripe
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      this.stripe = await loadStripe(ENV_CONFIG.STRIPE_PUBLIC_KEY);
      
      if (!this.stripe) {
        throw new Error('Falha ao carregar Stripe');
      }

      this.isInitialized = true;
      console.log('✅ Stripe initialized successfully');
    } catch (error) {
      console.error('❌ Error initializing Stripe:', error);
      throw new ApiError('Erro ao inicializar sistema de pagamentos', 0, 'STRIPE_INIT_ERROR');
    }
  }

  /**
   * Criar sessão de checkout
   */
  async createCheckoutSession(data: CheckoutSessionData): Promise<StripeSession> {
    try {
      const response = await apiService.post<StripeSession>('/stripe/create-checkout-session', data);
      
      if (!response.success || !response.data) {
        throw new ApiError(
          response.error || 'Erro ao criar sessão de pagamento',
          0,
          'CHECKOUT_SESSION_ERROR'
        );
      }

      return response.data;
    } catch (error) {
      console.error('Error creating checkout session:', error);
      
      if (error instanceof ApiError) {
        throw error;
      }
      
      throw new ApiError(
        'Erro ao processar pagamento. Tente novamente.',
        0,
        'PAYMENT_PROCESSING_ERROR'
      );
    }
  }

  /**
   * Redirecionar para checkout
   */
  async redirectToCheckout(sessionId: string): Promise<void> {
    if (!this.stripe) {
      await this.initialize();
    }

    if (!this.stripe) {
      throw new ApiError('Sistema de pagamentos não disponível', 0, 'STRIPE_NOT_AVAILABLE');
    }

    try {
      const { error } = await this.stripe.redirectToCheckout({
        sessionId: sessionId
      });

      if (error) {
        throw new ApiError(
          error.message || 'Erro ao redirecionar para pagamento',
          0,
          'STRIPE_REDIRECT_ERROR'
        );
      }
    } catch (error) {
      console.error('Error redirecting to checkout:', error);
      throw error;
    }
  }

  /**
   * Processar pagamento completo (criar sessão + redirecionar)
   */
  async processPayment(data: CheckoutSessionData): Promise<void> {
    try {
      // Garantir que o Stripe está inicializado
      await this.initialize();

      // Criar sessão de checkout
      const session = await this.createCheckoutSession(data);

      // Redirecionar para checkout
      await this.redirectToCheckout(session.id);
    } catch (error) {
      console.error('Error processing payment:', error);
      throw error;
    }
  }

  /**
   * Obter planos disponíveis
   */
  async getPlans(): Promise<PlanType[]> {
    try {
      const response = await apiService.get<PlanType[]>('/stripe/plans');
      
      if (!response.success || !response.data) {
        throw new ApiError('Erro ao carregar planos', 0, 'PLANS_LOAD_ERROR');
      }

      return response.data;
    } catch (error) {
      console.error('Error loading plans:', error);
      
      // Fallback com planos hardcoded se a API falhar
      return this.getFallbackPlans();
    }
  }

  /**
   * Planos de fallback caso a API não esteja disponível
   */
  private getFallbackPlans(): PlanType[] {
    return [
      {
        id: 'basic',
        name: 'Básico',
        description: 'Ideal para pequenas equipes e projetos iniciais',
        features: [
          'Até 5 usuários',
          'Até 100 demandas',
          'Até 500 cidadãos',
          '10GB de armazenamento',
          'Exportação de dados',
          'Suporte básico'
        ],
        prices: {
          monthly: 169.90,
          yearly: 1834.92
        }
      },
      {
        id: 'standard',
        name: 'Padrão',
        description: 'Para equipes em crescimento que precisam de mais recursos',
        features: [
          'Até 15 usuários',
          'Até 500 demandas',
          'Até 2.000 cidadãos',
          '50GB de armazenamento',
          'Mensagens em massa',
          'Integração redes sociais',
          'Relatórios avançados',
          'API de acesso',
          'Suporte prioritário'
        ],
        prices: {
          monthly: 259.90,
          yearly: 2807.08
        }
      },
      {
        id: 'professional',
        name: 'Profissional',
        description: 'Solução completa para organizações grandes',
        features: [
          'Usuários ilimitados',
          'Demandas ilimitadas',
          'Cidadãos ilimitados',
          '200GB de armazenamento',
          'Todas as funcionalidades',
          'Notificações SMS',
          'Relatórios personalizados',
          'Webhooks',
          'Suporte dedicado',
          'Recursos de IA'
        ],
        prices: {
          monthly: 599.90,
          yearly: 6478.92
        }
      }
    ];
  }

  /**
   * Verificar status de pagamento
   */
  async checkPaymentStatus(sessionId: string): Promise<any> {
    try {
      const response = await apiService.get(`/stripe/session/${sessionId}`);
      return response.data;
    } catch (error) {
      console.error('Error checking payment status:', error);
      throw new ApiError('Erro ao verificar status do pagamento', 0, 'PAYMENT_STATUS_ERROR');
    }
  }

  /**
   * Cancelar assinatura
   */
  async cancelSubscription(subscriptionId: string): Promise<void> {
    try {
      const response = await apiService.post(`/stripe/cancel-subscription`, { subscriptionId });
      
      if (!response.success) {
        throw new ApiError(
          response.error || 'Erro ao cancelar assinatura',
          0,
          'SUBSCRIPTION_CANCEL_ERROR'
        );
      }
    } catch (error) {
      console.error('Error canceling subscription:', error);
      throw error;
    }
  }
}

export const stripeService = new StripeService();
export default stripeService;