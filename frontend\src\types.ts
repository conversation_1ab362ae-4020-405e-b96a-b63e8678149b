export enum UserRole {
  ADMIN = 'admin',
  STAFF = 'staff',
  MASTER = 'master',
  // CITIZEN = 'citizen', // Potentially for a citizen portal
}

export enum Permission {
  VIEW_DEMANDS = 'view_demands',
  EDIT_DEMANDS = 'edit_demands',
  MANAGE_CITIZENS = 'manage_citizens',
  MANAGE_AGENDA = 'manage_agenda',
  VIEW_DOCUMENTS = 'view_documents',
  MANAGE_SOCIAL_MEDIA = 'manage_social_media',
  VIEW_REPORTS = 'view_reports',
  MANAGE_TEAM = 'manage_team',
  MANAGE_SERVICES = 'manage_services', // Nova permissão
}

export interface User {
  masterId: string | null | undefined;
  id: string;
  email: string;
  name: string;
  role: UserRole;
  avatarUrl?: string;
}

export interface OnlineUser {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  avatarUrl?: string;
  status: 'online' | 'away' | 'busy' | 'offline';
  lastActivity: string;
  sessionStart: string;
  sessionDuration: number; // em minutos
  ipAddress?: string;
  location?: string;
  device?: string;
  browser?: string;
  currentPage?: string;
  actionsToday: number;
}

export interface UserSession {
  id: string;
  userId: string;
  sessionStart: string;
  lastActivity: string;
  ipAddress?: string;
  userAgent?: string;
  device?: string;
  browser?: string;
  location?: string;
  currentPage?: string;
  isActive: boolean;
}

export interface Demand {
  id: string;
  title: string;
  description: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high';
  deadline?: string; // ISO date string
  assignedTo?: string; // User ID
  createdBy: string; // User ID
  createdAt: string; // ISO date string, ou Firestore Timestamp convertido
  updatedAt: string; // ISO date string, ou Firestore Timestamp convertido
}

export interface Citizen {
  id: string;
  fullName: string;
  email?: string;
  phone?: string;
  address?: string;
  neighborhood?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  birthDate?: string;
  voterRegistration?: {
    number?: string;
    zone?: string;
    section?: string;
  };
  notes?: string;
  tags?: string[];
  workplace?: string;
  profession?: string;
  socialMedia?: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
    linkedin?: string;
    [key: string]: string | undefined;
  };
  whatsapp?: string;
  createdAt: Date | string;
  updatedAt: Date | string;
  createdBy?: string;
}

export interface TeamMember {
  id: string;
  userId: string; // Links to User
  name: string; // Duplicated for convenience or from User
  email?: string; // Email do membro da equipe (adicionado)
  roleInTeam: string; // e.g., 'Advisor', 'Secretary'
  permissions?: string[]; // Specific permissions within the team context
  joinedAt: string; // ISO date string
  createdAt?: string; // Data de criação (opcional)
  updatedAt?: string; // Data de atualização (opcional)
}

export interface AgendaEvent {
  id: string;
  title: string;
  start: string; // ISO date string (YYYY-MM-DDTHH:mm)
  end: string;   // ISO date string (YYYY-MM-DDTHH:mm)
  description?: string;
  attendees?: string[]; // User IDs or names
  location?: string;
  isAllDay?: boolean;
  createdBy: string; // User ID
  createdAt: string; // ISO date string from Firestore Timestamp
  updatedAt: string; // ISO date string from Firestore Timestamp
}

export interface Document {
  id: string;
  name: string;
  type: string; // Mime type or extension e.g., 'application/pdf', 'pdf'
  url: string; // Storage download URL
  storagePath: string; // Full path in Firebase Storage (e.g., documents/userId/docId/fileName.pdf)
  uploadedBy: string; // User ID, maps to createdBy logic
  createdAt: string; // ISO date string from Firestore Timestamp
  size: number; // in bytes
}

export interface WorkspaceSettings {
  id: string; // e.g., 'default_workspace'
  institutionName: string;
  logoUrl?: string; // URL of the uploaded logo
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state: string; // UF
  zipCode: string; // CEP
  phone?: string;
  email?: string;
  website?: string;
  updatedAt: string; // ISO date string
}

export interface PoliticianSocialMedia {
  platform: string;
  username: string;
  url: string;
  connected: boolean;
  lastSync?: string; // ISO date string
}

export interface SocialMediaAnalytics {
  platform: string;
  followers: number;
  engagement: number;
  reach: number;
  sentiment: {
    positive: number;
    neutral: number;
    negative: number;
  };
}

export interface PoliticianProfile {
  id: string; // e.g., 'main_profile'
  name: string;
  photoUrl?: string;
  politicalParty?: string;
  birthDate?: string; // ISO date string
  bio?: string; // Short biography
  contact?: {
    phone?: string;
    email?: string;
    officePhone?: string;
  };
  address?: {
    street?: string;
    number?: string;
    complement?: string;
    neighborhood?: string;
    city?: string;
    state?: string; // UF
    zipCode?: string; // CEP
  };
  socialMedia?: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
    linkedin?: string;
    tiktok?: string;
    youtube?: string;
    website?: string;
  };
  updatedAt: string; // ISO date string
}


// Props for UI components
export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'danger' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  isLoading?: boolean;
  loadingText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
}

export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

export interface DemandStatusReport {
  status: string;
  count: number;
  percentage: number;
}

export interface CitizenEngagementReport {
  month: string;
  interactions: number;
  newCitizens: number;
  activeContacts: number;
}

export interface TeamPerformanceReport {
  userId: string;
  userName: string;
  completedDemands: number;
  averageResponseTime: number;
  overdueTasks: number;
  totalDemands: number;
}

export interface SocialMediaReport {
  platform: string;
  followers: number;
  engagement: number;
  reach: number;
  sentiment: {
    positive: number;
    neutral: number;
    negative: number;
  };
  posts: {
    total: number;
    engagementRate: number;
  };
}

export interface ReportData {
  demandStatus: DemandStatusReport[];
  citizenEngagement: CitizenEngagementReport[];
  teamPerformance: TeamPerformanceReport[];
  socialMedia: SocialMediaReport[];
  dateRange: {
    start: string;
    end: string;
  };
}

// Definição dos enums para Service
export enum ServiceCategory {
  INFRASTRUCTURE = 'infrastructure',
  HEALTH = 'health',
  EDUCATION = 'education',
  SECURITY = 'security',
  ENVIRONMENT = 'environment',
  SOCIAL = 'social',
  OTHER = 'other'
}

export enum ServiceStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

// Definição da interface Service
export interface Service {
  id: string;
  title: string;
  description: string;
  category: ServiceCategory;
  status: ServiceStatus;
  priority: 'low' | 'medium' | 'high';
  deadline?: string; // ISO date string
  assignedTo?: string; // User ID
  createdBy: string; // User ID
  createdAt: string; // ISO date string
  updatedAt: string; // ISO date string
  completedAt?: string; // ISO date string opcional
  notes?: string;
  attachments?: string[];
}

export interface BulkMessage {
  id: string;
  title: string;
  body: string;
  recipients: string[]; // IDs dos cidadãos
  status: 'draft' | 'sent' | 'scheduled';
  scheduledFor?: string; // ISO date string
  sentAt?: string; // ISO date string
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}
