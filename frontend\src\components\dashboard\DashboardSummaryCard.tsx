
import React from 'react';
import { Card } from '../ui/Card';

interface DashboardSummaryCardProps {
  title: string;
  value: string | number | React.ReactNode;
  icon: React.ReactElement<React.SVGProps<SVGSVGElement>>;
  colorClass?: string; 
  actionLink?: {
    text: string;
    href: string;
  };
}

export const DashboardSummaryCard: React.FC<DashboardSummaryCardProps> = ({
  title,
  value,
  icon,
  colorClass = 'bg-primary',
  actionLink,
}) => {
  return (
    <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-500 dark:text-neutral-DEFAULT truncate">{title}</p>
          <p className="mt-1 text-3xl font-semibold text-neutral-dark dark:text-neutral-extralight">{value}</p>
        </div>
        <div className={`p-3 rounded-full ${colorClass} text-white`}>
          {React.cloneElement(icon, { className: 'w-6 h-6' })}
        </div>
      </div>
      {actionLink && (
        <div className="mt-4">
          <a
            href={actionLink.href}
            className="text-sm font-medium text-primary hover:text-primary-dark dark:text-primary-light dark:hover:text-primary"
          >
            {actionLink.text} &rarr;
          </a>
        </div>
      )}
    </Card>
  );
};