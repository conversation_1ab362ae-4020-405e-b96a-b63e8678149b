import path from 'path';
import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, '.', '');
    return {
      plugins: [react()],
      define: {
        'process.env.API_KEY': JSON.stringify(env.GEMINI_API_KEY),
        'process.env.GEMINI_API_KEY': JSON.stringify(env.GEMINI_API_KEY),
        'process.env.NODE_ENV': JSON.stringify(mode)
      },
      resolve: {
        alias: {
          '@': path.resolve(__dirname, './src'),
        }
      },
      server: {
        cors: true,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
          'Access-Control-Allow-Headers': [
            'Origin',
            'Content-Type',
            'Accept',
            'Authorization',
            'X-Requested-With',
            'x-goog-upload-protocol',
            'x-goog-upload-command',
            'x-goog-upload-offset',
            'x-goog-upload-url',
            'x-goog-meta-*',
            'x-goog-content-length-range',
            'x-goog-upload-content-type',
            'x-goog-upload-content-length',
            'x-goog-hash',
            'x-goog-resumable'
          ].join(', '),
          'Access-Control-Expose-Headers': [
            'Content-Length',
            'ETag',
            'x-goog-generation',
            'x-goog-metageneration',
            'x-goog-stored-content-encoding',
            'x-goog-stored-content-length',
            'x-goog-storage-class',
            'x-goog-hash',
            'x-goog-upload-url',
            'x-goog-upload-status'
          ].join(',')
        },
        proxy: {
          '^/storage/.*': {
            target: 'https://firebasestorage.googleapis.com',
            changeOrigin: true,
            secure: false,
            rewrite: (path) => path.replace(/^\/storage/, ''),
            configure: (proxy) => {
              proxy.on('proxyReq', (proxyReq, req) => {
                // Preserve original headers
                Object.keys(req.headers).forEach(key => {
                  if (
                    (key.toLowerCase().startsWith('x-goog-') ||
                      key === 'authorization' ||
                      key === 'content-type') &&
                    typeof req.headers[key] !== 'undefined'
                  ) {
                    proxyReq.setHeader(key, req.headers[key] as string | number | readonly string[]);
                  }
                });

                // Add required headers for Firebase Storage
                proxyReq.setHeader('Origin', 'https://firebasestorage.googleapis.com');
                proxyReq.setHeader('Host', 'firebasestorage.googleapis.com');
              });

              proxy.on('proxyRes', (proxyRes) => {
                // Ensure CORS headers are set
                proxyRes.headers['access-control-allow-origin'] = '*';
                proxyRes.headers['access-control-allow-methods'] = 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS';
                proxyRes.headers['access-control-allow-headers'] = [
                  'Origin',
                  'Content-Type',
                  'Accept',
                  'Authorization',
                  'X-Requested-With',
                  'x-goog-upload-protocol',
                  'x-goog-upload-command',
                  'x-goog-upload-offset',
                  'x-goog-upload-url',
                  'x-goog-meta-*',
                  'x-goog-content-length-range',
                  'x-goog-upload-content-type',
                  'x-goog-upload-content-length',
                  'x-goog-hash'
                ].join(', ');
                proxyRes.headers['access-control-expose-headers'] = '*';
                proxyRes.headers['access-control-max-age'] = '3600';
              });
            }
          }
        }
      },
      optimizeDeps: {
        exclude: ['firebase', 'firebase/app', 'firebase/auth', 'firebase/firestore', 'firebase/storage']
      }
    };
});
