@echo off
echo ========================================
echo     DEPLOY BACKEND - VERCEL
echo ========================================
echo.

echo [1/4] Verificando Vercel CLI...
vercel --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Vercel CLI nao encontrado. Instalando...
    npm install -g vercel
)

echo Vercel CLI encontrado!
echo.

echo [2/4] Fazendo login no Vercel...
vercel login

echo.
echo [3/4] Criando configuracao Vercel...
cd backend

echo { > vercel.json
echo   "version": 2, >> vercel.json
echo   "builds": [ >> vercel.json
echo     { >> vercel.json
echo       "src": "server.js", >> vercel.json
echo       "use": "@vercel/node" >> vercel.json
echo     } >> vercel.json
echo   ], >> vercel.json
echo   "routes": [ >> vercel.json
echo     { >> vercel.json
echo       "src": "/(.*)", >> vercel.json
echo       "dest": "/server.js" >> vercel.json
echo     } >> vercel.json
echo   ], >> vercel.json
echo   "env": { >> vercel.json
echo     "NODE_ENV": "production" >> vercel.json
echo   } >> vercel.json
echo } >> vercel.json

echo.
echo [4/4] Fazendo deploy...
vercel --prod

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo       BACKEND DEPLOYADO COM SUCESSO!
    echo ========================================
    echo.
    echo URL sera exibida acima.
    echo.
    echo Comandos uteis:
    echo - Ver deployments: vercel ls
    echo - Ver logs: vercel logs
    echo - Configurar vars: vercel env add
    echo.
) else (
    echo.
    echo ERRO: Deploy falhou!
    echo Verifique os logs acima.
)

cd ..
pause
