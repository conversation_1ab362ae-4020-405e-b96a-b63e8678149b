import jwt from 'jsonwebtoken';
import config from '../config.js';

class JWTUtils {
  // Gerar token de acesso
  static generateAccessToken(user) {
    const payload = {
      userId: user.id,
      email: user.email,
      role: user.role,
      permissions: user.permissions,
      type: 'access'
    };

    return jwt.sign(payload, config.jwt.secret, {
      expiresIn: config.jwt.expiresIn,
      issuer: 'promandato-auth',
      audience: 'promandato-app'
    });
  }

  // Gerar token de refresh
  static generateRefreshToken(user) {
    const payload = {
      userId: user.id,
      email: user.email,
      type: 'refresh'
    };

    return jwt.sign(payload, config.jwt.secret, {
      expiresIn: config.jwt.refreshExpiresIn,
      issuer: 'promandato-auth',
      audience: 'promandato-app'
    });
  }

  // Gerar ambos os tokens
  static generateTokens(user) {
    return {
      accessToken: this.generateAccessToken(user),
      refreshToken: this.generateRefreshToken(user),
      expiresIn: config.jwt.expiresIn,
      tokenType: 'Bearer'
    };
  }

  // Verificar token
  static verifyToken(token) {
    try {
      return jwt.verify(token, config.jwt.secret, {
        issuer: 'promandato-auth',
        audience: 'promandato-app'
      });
    } catch (error) {
      throw error;
    }
  }

  // Decodificar token sem verificar (para debug)
  static decodeToken(token) {
    return jwt.decode(token, { complete: true });
  }

  // Verificar se o token está expirado
  static isTokenExpired(token) {
    try {
      const decoded = jwt.decode(token);
      if (!decoded || !decoded.exp) return true;
      
      const currentTime = Math.floor(Date.now() / 1000);
      return decoded.exp < currentTime;
    } catch (error) {
      return true;
    }
  }

  // Obter tempo restante do token em segundos
  static getTokenTimeRemaining(token) {
    try {
      const decoded = jwt.decode(token);
      if (!decoded || !decoded.exp) return 0;
      
      const currentTime = Math.floor(Date.now() / 1000);
      const timeRemaining = decoded.exp - currentTime;
      
      return Math.max(0, timeRemaining);
    } catch (error) {
      return 0;
    }
  }

  // Gerar token para reset de senha
  static generatePasswordResetToken(user) {
    const payload = {
      userId: user.id,
      email: user.email,
      type: 'password_reset',
      timestamp: Date.now()
    };

    return jwt.sign(payload, config.jwt.secret, {
      expiresIn: '1h', // Token de reset expira em 1 hora
      issuer: 'promandato-auth',
      audience: 'promandato-app'
    });
  }

  // Gerar token para verificação de email
  static generateEmailVerificationToken(user) {
    const payload = {
      userId: user.id,
      email: user.email,
      type: 'email_verification',
      timestamp: Date.now()
    };

    return jwt.sign(payload, config.jwt.secret, {
      expiresIn: '24h', // Token de verificação expira em 24 horas
      issuer: 'promandato-auth',
      audience: 'promandato-app'
    });
  }

  // Extrair informações do token
  static extractTokenInfo(token) {
    try {
      const decoded = this.verifyToken(token);
      return {
        userId: decoded.userId,
        email: decoded.email,
        role: decoded.role,
        permissions: decoded.permissions,
        type: decoded.type,
        issuedAt: new Date(decoded.iat * 1000),
        expiresAt: new Date(decoded.exp * 1000),
        timeRemaining: this.getTokenTimeRemaining(token)
      };
    } catch (error) {
      return null;
    }
  }
}

export default JWTUtils;