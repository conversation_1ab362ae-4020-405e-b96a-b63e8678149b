import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Button } from './Button';
import { Card } from './Card';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Call the onError callback if provided
    this.props.onError?.(error, errorInfo);

    // Check if this is a JSON parsing error from browser extensions
    if (error.message.includes('not valid JSON') || 
        error.message.includes('JSON.parse') ||
        error.stack?.includes('content.js')) {
      console.warn('Detected browser extension JSON parsing conflict. This is not an application error.');
      
      // Try to clear potentially corrupted localStorage data
      this.clearCorruptedStorage();
    }
  }

  clearCorruptedStorage = () => {
    try {
      const keysToCheck = [
        'dashboard-widgets',
        'user-preferences',
        'theme-preference',
        'tour-completed'
      ];

      keysToCheck.forEach(key => {
        try {
          const value = localStorage.getItem(key);
          if (value) {
            JSON.parse(value); // Test if it's valid JSON
          }
        } catch (parseError) {
          console.warn(`Removing corrupted localStorage key: ${key}`);
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
      console.error('Error while clearing corrupted storage:', error);
    }
  };

  handleReload = () => {
    window.location.reload();
  };

  handleReset = () => {
    this.clearCorruptedStorage();
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    });
  };

  render() {
    if (this.state.hasError) {
      // Check if this is likely a browser extension error
      const isExtensionError = this.state.error?.stack?.includes('content.js') ||
                              this.state.error?.message.includes('not valid JSON');

      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen bg-neutral-light dark:bg-neutral-darkest flex items-center justify-center p-4">
          <Card className="max-w-2xl w-full">
            <div className="p-8 text-center">
              <div className="mb-6">
                <svg 
                  className="w-16 h-16 text-red-500 mx-auto mb-4" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" 
                  />
                </svg>
                
                <h1 className="text-2xl font-bold text-gray-900 dark:text-neutral-light mb-2">
                  {isExtensionError ? 'Conflito de Extensão Detectado' : 'Algo deu errado'}
                </h1>
                
                <p className="text-gray-600 dark:text-neutral-DEFAULT">
                  {isExtensionError 
                    ? 'Uma extensão do navegador está interferindo com o aplicativo. Isso não afeta a funcionalidade principal.'
                    : 'Ocorreu um erro inesperado. Tente recarregar a página ou entre em contato com o suporte.'
                  }
                </p>
              </div>

              {isExtensionError && (
                <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
                  <div className="flex items-start">
                    <svg className="w-5 h-5 text-yellow-600 dark:text-yellow-400 mt-0.5 mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    <div className="text-sm text-yellow-800 dark:text-yellow-200">
                      <p className="font-medium">Dica:</p>
                      <p>Considere desabilitar extensões que modificam páginas web ou usar o modo incógnito para uma experiência mais estável.</p>
                    </div>
                  </div>
                </div>
              )}

              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button 
                  variant="primary" 
                  onClick={this.handleReload}
                  className="flex items-center justify-center"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Recarregar Página
                </Button>
                
                <Button 
                  variant="outline" 
                  onClick={this.handleReset}
                  className="flex items-center justify-center"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  Limpar Cache e Tentar Novamente
                </Button>
              </div>

              {process.env.NODE_ENV === 'development' && this.state.error && (
                <details className="mt-6 text-left">
                  <summary className="cursor-pointer text-sm font-medium text-gray-700 dark:text-neutral-DEFAULT mb-2">
                    Detalhes do Erro (Desenvolvimento)
                  </summary>
                  <div className="bg-gray-100 dark:bg-neutral-darker rounded-lg p-4 text-xs font-mono overflow-auto">
                    <div className="text-red-600 dark:text-red-400 mb-2">
                      {this.state.error.toString()}
                    </div>
                    {this.state.errorInfo && (
                      <div className="text-gray-600 dark:text-neutral-medium">
                        {this.state.errorInfo.componentStack}
                      </div>
                    )}
                  </div>
                </details>
              )}
            </div>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

// Hook version for functional components
export const useErrorHandler = () => {
  const handleError = (error: Error, errorInfo?: ErrorInfo) => {
    console.error('Error caught by useErrorHandler:', error, errorInfo);
    
    // Check if this is a JSON parsing error from browser extensions
    if (error.message.includes('not valid JSON') || 
        error.message.includes('JSON.parse') ||
        error.stack?.includes('content.js')) {
      console.warn('Detected browser extension JSON parsing conflict. Ignoring error.');
      return; // Don't propagate extension errors
    }
    
    // For other errors, you might want to show a toast or handle differently
    throw error;
  };

  return { handleError };
};
