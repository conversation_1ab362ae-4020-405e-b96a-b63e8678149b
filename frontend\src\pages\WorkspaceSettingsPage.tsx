
import React, { useState, useEffect, useCallback } from 'react';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/Input';
import { ICONS } from '../constants';
import { WorkspaceSettings } from '../types';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';
import { 
  getWorkspaceSettings, 
  saveWorkspaceSettings, 
  uploadFileToStorage,
  deleteFileFromStorage
} from '../services/firebaseService';

const DEFAULT_WORKSPACE_ID = 'default_settings';
const LOGO_STORAGE_PATH = `workspace_logos/${DEFAULT_WORKSPACE_ID}/logo`; 

const initialSettings: WorkspaceSettings = {
  id: DEFAULT_WORKSPACE_ID,
  institutionName: '',
  logoUrl: '',
  addressLine1: '',
  addressLine2: '',
  city: '',
  state: '',
  zipCode: '',
  phone: '',
  email: '',
  website: '',
  updatedAt: new Date().toISOString(),
};

const WorkspaceSettingsPage: React.FC = () => {
  const [settings, setSettings] = useState<WorkspaceSettings>(initialSettings);
  const [selectedLogoFile, setSelectedLogoFile] = useState<File | null>(null);
  const [logoPreviewUrl, setLogoPreviewUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);


  const fetchSettings = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const fetchedSettings = await getWorkspaceSettings(DEFAULT_WORKSPACE_ID);
      if (fetchedSettings) {
        setSettings(fetchedSettings);
        if (fetchedSettings.logoUrl) {
          setLogoPreviewUrl(fetchedSettings.logoUrl);
        } else {
          setLogoPreviewUrl(null);
        }
      } else {
        setSettings(initialSettings);
        setLogoPreviewUrl(null);
      }
    } catch (err) {
      console.error("Failed to fetch workspace settings:", err);
      setError("Falha ao carregar as configurações. Tente novamente.");
      setSettings(initialSettings); 
      setLogoPreviewUrl(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  useEffect(() => {
    return () => {
      if (logoPreviewUrl && logoPreviewUrl.startsWith('blob:')) {
        URL.revokeObjectURL(logoPreviewUrl);
      }
    };
  }, [logoPreviewUrl]);


  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setSettings(prev => ({ ...prev, [name]: value }));
  };

  const handleLogoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      if (file.size > 2 * 1024 * 1024) { 
          alert("O arquivo é muito grande. Máximo 2MB.");
          return;
      }
      if (!['image/png', 'image/jpeg', 'image/svg+xml', 'image/webp'].includes(file.type)) {
          alert("Formato de arquivo inválido. Use PNG, JPG, SVG ou WEBP.");
          return;
      }

      setSelectedLogoFile(file);
      if (logoPreviewUrl && logoPreviewUrl.startsWith('blob:')) {
        URL.revokeObjectURL(logoPreviewUrl); 
      }
      const newPreviewUrl = URL.createObjectURL(file);
      setLogoPreviewUrl(newPreviewUrl);
    }
  };
  
  const handleRemoveLogo = () => {
    setSelectedLogoFile(null);
    if (logoPreviewUrl && logoPreviewUrl.startsWith('blob:')) {
      URL.revokeObjectURL(logoPreviewUrl);
    }
    setLogoPreviewUrl(null); 
    const fileInput = document.getElementById('logoUrlInput') as HTMLInputElement;
    if (fileInput) {
      fileInput.value = "";
    }
  };

  const handleSaveChanges = async () => {
    setIsSaving(true);
    setError(null);
    
    let currentLogoUrl = settings.logoUrl; 

    try {
      if (selectedLogoFile) {
        if (settings.logoUrl && (settings.logoUrl.includes('firebasestorage.googleapis.com'))) {
            try {
                const pathSegments = new URL(settings.logoUrl).pathname.split('/');
                const fullPath = pathSegments.slice(pathSegments.indexOf('o') + 1).join('/').split('?')[0];
                await deleteFileFromStorage(decodeURIComponent(fullPath));
            } catch (deleteError) {
                console.warn("Could not delete old logo, proceeding with upload:", deleteError);
            }
        }
        const fileExtension = selectedLogoFile.name.split('.').pop() || 'png';
        const newLogoPath = `${LOGO_STORAGE_PATH}.${fileExtension}`;
        currentLogoUrl = await uploadFileToStorage(selectedLogoFile, newLogoPath);
      } else if (!logoPreviewUrl && settings.logoUrl) {
         if (settings.logoUrl && (settings.logoUrl.includes('firebasestorage.googleapis.com'))) {
            try {
                 const pathSegments = new URL(settings.logoUrl).pathname.split('/');
                 const fullPath = pathSegments.slice(pathSegments.indexOf('o') + 1).join('/').split('?')[0];
                 await deleteFileFromStorage(decodeURIComponent(fullPath));
            } catch (deleteError) {
                console.warn("Could not delete old logo during removal:", deleteError);
            }
        }
        currentLogoUrl = ''; 
      }

      const settingsToSave: Partial<WorkspaceSettings> = {
        ...settings,
        logoUrl: currentLogoUrl,
      };

      await saveWorkspaceSettings(settingsToSave, DEFAULT_WORKSPACE_ID);
      
      setSettings(prev => ({ ...prev, ...settingsToSave, logoUrl: currentLogoUrl, updatedAt: new Date().toISOString() }));
      setSelectedLogoFile(null); 
      if (currentLogoUrl) {
        setLogoPreviewUrl(currentLogoUrl); 
      } else {
        setLogoPreviewUrl(null);
      }
      alert("Configurações do Local de Trabalho salvas com sucesso!");
    } catch (err) {
      console.error("Failed to save workspace settings:", err);
      setError("Falha ao salvar configurações. Verifique os dados e tente novamente.");
    } finally {
      setIsSaving(false);
    }
  };
  
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[calc(100vh-200px)]">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold text-neutral-dark dark:text-neutral-light">Configurações do Local de Trabalho</h1>
      <p className="text-sm text-gray-600 dark:text-neutral-DEFAULT">
        Insira os dados da sua organização ou gabinete. Essas informações poderão ser usadas em documentos e outras comunicações.
      </p>

      {error && <div className="p-3 bg-red-100 dark:bg-red-900 dark:bg-opacity-30 text-red-700 dark:text-red-300 rounded-md">{error}</div>}

      <Card>
        <form onSubmit={(e) => { e.preventDefault(); handleSaveChanges(); }} className="space-y-6 p-2">
          
          <Input
            label="Nome da Instituição/Gabinete"
            name="institutionName"
            value={settings.institutionName}
            onChange={handleInputChange}
            placeholder="Ex: Câmara de Vereadores de Cidade Exemplo"
            required
            className="text-lg"
            disabled={isSaving}
          />

          <div>
            <label htmlFor="logoUrlInput" className="block text-sm font-medium text-neutral-dark dark:text-neutral-light mb-1">
              Logo da Instituição (Opcional)
            </label>
            <Input
              id="logoUrlInput" 
              name="logoFile" 
              type="file"
              accept="image/png, image/jpeg, image/svg+xml, image/webp"
              onChange={handleLogoChange}
              className="block w-full text-sm text-gray-500 dark:text-neutral-DEFAULT 
                         file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 
                         file:text-sm file:font-semibold 
                         file:bg-primary-light file:text-primary 
                         dark:file:bg-primary-dark dark:file:text-neutral-extralight
                         hover:file:bg-primary dark:hover:file:bg-primary-light
                         dark:bg-neutral-dark dark:border-neutral-medium"
              disabled={isSaving}
            />
            {logoPreviewUrl && (
              <div className="mt-3 p-2 border dark:border-neutral-medium rounded-md inline-block relative bg-gray-50 dark:bg-neutral-dark">
                <img src={logoPreviewUrl} alt="Prévia do Logo" className="h-24 w-auto max-w-xs object-contain rounded" />
                <Button 
                    type="button" 
                    onClick={handleRemoveLogo} 
                    variant="secondary" 
                    size="sm"
                    className="absolute -top-2 -right-2 !p-1 h-auto leading-none rounded-full shadow-md"
                    aria-label="Remover logo"
                    disabled={isSaving}
                >
                  {React.cloneElement(ICONS.TRASH, { className: "w-4 h-4" })}
                </Button>
              </div>
            )}
            <p className="text-xs text-gray-500 dark:text-neutral-DEFAULT mt-1">PNG, JPG, SVG, WEBP. Máx 2MB. Recomendado: fundo transparente.</p>
          </div>

          <fieldset className="mt-6 border-t border-gray-200 dark:border-neutral-dark pt-6" disabled={isSaving}>
            <legend className="text-lg font-medium text-neutral-dark dark:text-neutral-light mb-3">Endereço</legend>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Endereço (Linha 1)"
                name="addressLine1"
                value={settings.addressLine1}
                onChange={handleInputChange}
                placeholder="Rua, Avenida, Praça, Número"
                required
              />
              <Input
                label="Endereço (Linha 2 - Complemento)"
                name="addressLine2"
                value={settings.addressLine2 || ''}
                onChange={handleInputChange}
                placeholder="Sala, Bloco, Andar (Opcional)"
              />
              <Input
                label="Cidade"
                name="city"
                value={settings.city}
                onChange={handleInputChange}
                required
              />
                <Input
                label="Estado (UF)"
                name="state"
                value={settings.state}
                onChange={handleInputChange}
                maxLength={2}
                placeholder="Ex: SP"
                required
              />
              <Input
                label="CEP"
                name="zipCode"
                value={settings.zipCode}
                onChange={handleInputChange}
                placeholder="00000-000"
                required
              />
            </div>
          </fieldset>

           <fieldset className="mt-6 border-t border-gray-200 dark:border-neutral-dark pt-6" disabled={isSaving}>
            <legend className="text-lg font-medium text-neutral-dark dark:text-neutral-light mb-3">Contatos</legend>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input
                label="Telefone Principal"
                name="phone"
                type="tel"
                value={settings.phone || ''}
                onChange={handleInputChange}
                placeholder="(XX) XXXX-XXXX ou (XX) 9XXXX-XXXX"
              />
              <Input
                label="Email de Contato"
                name="email"
                type="email"
                value={settings.email || ''}
                onChange={handleInputChange}
                placeholder="<EMAIL>"
              />
              <Input
                label="Website (URL)"
                name="website"
                type="url"
                value={settings.website || ''}
                onChange={handleInputChange}
                placeholder="https://www.instituicao.gov.br"
                className="md:col-span-2"
              />
            </div>
          </fieldset>
          
          <div className="pt-6 flex justify-end">
            <Button type="submit" isLoading={isSaving} size="lg" disabled={isSaving || isLoading}>
              {isSaving ? "Salvando..." : "Salvar Configurações"}
            </Button>
          </div>
           {settings.updatedAt && !isLoading && (
             <p className="text-xs text-gray-500 dark:text-neutral-DEFAULT text-right mt-2">
               Última atualização: {new Date(settings.updatedAt).toLocaleString('pt-BR')}
             </p>
           )}
        </form>
      </Card>
    </div>
  );
};

export default WorkspaceSettingsPage;