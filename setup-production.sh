#!/bin/bash

# ===========================================
# SCRIPT DE CONFIGURAÇÃO DE AMBIENTE
# ProMandato - Configuração de Produção
# ===========================================

set -e

echo "🚀 Configurando ambiente de produção do ProMandato..."

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para log colorido
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Verificar se está no diretório correto
if [ ! -f "package.json" ] && [ ! -f "backend/package.json" ]; then
    log_error "Execute este script no diretório raiz do projeto"
    exit 1
fi

# ===========================================
# CONFIGURAÇÃO DO BACKEND
# ===========================================

log_info "Configurando variáveis de ambiente do backend..."

# Criar diretório backend se não existir
mkdir -p backend

# Copiar arquivo de exemplo se não existir .env
if [ ! -f "backend/.env" ]; then
    if [ -f ".env.example" ]; then
        cp .env.example backend/.env
        log_success "Arquivo .env criado no backend"
    else
        log_error "Arquivo .env.example não encontrado"
        exit 1
    fi
else
    log_warning "Arquivo backend/.env já existe, não será sobrescrito"
fi

# ===========================================
# CONFIGURAÇÃO DO FRONTEND
# ===========================================

log_info "Configurando variáveis de ambiente do frontend..."

# Criar diretório frontend se não existir
mkdir -p frontend

# Verificar se os arquivos de ambiente existem
if [ ! -f "frontend/.env.production" ]; then
    log_warning "Arquivo frontend/.env.production não encontrado"
    log_info "Criando arquivo de exemplo..."
    
    cat > frontend/.env.production << 'EOF'
VITE_API_URL=https://promandato-backend-517140455601.southamerica-east1.run.app/api
VITE_STRIPE_PUBLIC_KEY=pk_live_sua_chave_publica_stripe
VITE_FIREBASE_API_KEY=sua_firebase_api_key
VITE_FIREBASE_AUTH_DOMAIN=promandato-9a4cf.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=promandato-9a4cf
VITE_FIREBASE_STORAGE_BUCKET=promandato-9a4cf.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=517140455601
VITE_FIREBASE_APP_ID=sua_firebase_app_id
VITE_FIREBASE_MEASUREMENT_ID=sua_measurement_id
VITE_APP_ENVIRONMENT=production
EOF
    
    log_success "Arquivo frontend/.env.production criado"
fi

# ===========================================
# CONFIGURAÇÃO DO DOCKER
# ===========================================

log_info "Configurando Docker para produção..."

# Criar docker-compose.prod.yml se não existir
if [ ! -f "docker-compose.prod.yml" ]; then
    cat > docker-compose.prod.yml << 'EOF'
version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=production
      - PORT=8080
    env_file:
      - ./backend/.env
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        - NODE_ENV=production
    ports:
      - "80:80"
    depends_on:
      - backend
    restart: unless-stopped

networks:
  default:
    driver: bridge
EOF
    
    log_success "Arquivo docker-compose.prod.yml criado"
fi

# ===========================================
# CONFIGURAÇÃO DO NGINX (se necessário)
# ===========================================

log_info "Configurando Nginx para produção..."

mkdir -p nginx

if [ ! -f "nginx/nginx.conf" ]; then
    cat > nginx/nginx.conf << 'EOF'
server {
    listen 80;
    server_name promandato.com.br www.promandato.com.br;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name promandato.com.br www.promandato.com.br;
    
    # SSL Configuration
    ssl_certificate /etc/ssl/certs/promandato.crt;
    ssl_certificate_key /etc/ssl/private/promandato.key;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Frontend
    location / {
        proxy_pass http://frontend:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Backend API
    location /api/ {
        proxy_pass http://backend:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # CORS headers
        add_header Access-Control-Allow-Origin "https://promandato.com.br" always;
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Authorization, Content-Type" always;
    }
}
EOF
    
    log_success "Configuração do Nginx criada"
fi

# ===========================================
# SCRIPTS DE DEPLOY
# ===========================================

log_info "Criando scripts de deploy..."

# Script de deploy
cat > deploy.sh << 'EOF'
#!/bin/bash

set -e

echo "🚀 Iniciando deploy do ProMandato..."

# Build do frontend
echo "📦 Building frontend..."
cd frontend
npm ci --only=production
npm run build
cd ..

# Build do backend
echo "📦 Building backend..."
cd backend
npm ci --only=production
cd ..

# Deploy com Docker
echo "🐳 Deploying with Docker..."
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml build --no-cache
docker-compose -f docker-compose.prod.yml up -d

echo "✅ Deploy concluído!"
echo "🔍 Verificando status dos serviços..."
docker-compose -f docker-compose.prod.yml ps

echo "🌐 Aplicação disponível em:"
echo "  - Frontend: http://localhost"
echo "  - Backend: http://localhost:8080"
echo "  - Health Check: http://localhost:8080/api/health"
EOF

chmod +x deploy.sh
log_success "Script de deploy criado"

# Script de backup
cat > backup.sh << 'EOF'
#!/bin/bash

set -e

BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

echo "💾 Criando backup em $BACKUP_DIR..."

# Backup dos dados
if [ -d "backend/data" ]; then
    cp -r backend/data "$BACKUP_DIR/"
    echo "✅ Dados do backend copiados"
fi

# Backup das configurações
cp backend/.env "$BACKUP_DIR/.env.backend" 2>/dev/null || echo "⚠️  Arquivo backend/.env não encontrado"
cp frontend/.env.production "$BACKUP_DIR/.env.frontend" 2>/dev/null || echo "⚠️  Arquivo frontend/.env.production não encontrado"

# Backup dos logs
if [ -d "logs" ]; then
    cp -r logs "$BACKUP_DIR/"
    echo "✅ Logs copiados"
fi

echo "✅ Backup concluído em $BACKUP_DIR"
EOF

chmod +x backup.sh
log_success "Script de backup criado"

# ===========================================
# VERIFICAÇÕES FINAIS
# ===========================================

log_info "Executando verificações finais..."

# Verificar se Node.js está instalado
if ! command -v node &> /dev/null; then
    log_error "Node.js não está instalado"
    exit 1
fi

# Verificar se Docker está instalado
if ! command -v docker &> /dev/null; then
    log_warning "Docker não está instalado - necessário para deploy"
fi

# Verificar se as dependências estão instaladas
if [ -f "backend/package.json" ] && [ ! -d "backend/node_modules" ]; then
    log_warning "Dependências do backend não instaladas - execute: cd backend && npm install"
fi

if [ -f "frontend/package.json" ] && [ ! -d "frontend/node_modules" ]; then
    log_warning "Dependências do frontend não instaladas - execute: cd frontend && npm install"
fi

# ===========================================
# INSTRUÇÕES FINAIS
# ===========================================

echo ""
log_success "Configuração de ambiente concluída!"
echo ""
echo "📋 PRÓXIMOS PASSOS:"
echo ""
echo "1. 🔐 Configure as variáveis sensíveis:"
echo "   - Edite backend/.env com suas chaves reais"
echo "   - Configure STRIPE_SECRET_KEY, JWT_SECRET, etc."
echo ""
echo "2. 🔑 Configure as chaves do Stripe:"
echo "   - Acesse https://dashboard.stripe.com"
echo "   - Copie as chaves de produção"
echo "   - Atualize os arquivos .env"
echo ""
echo "3. 🚀 Execute o deploy:"
echo "   - ./deploy.sh (para deploy com Docker)"
echo "   - ou siga as instruções específicas da sua plataforma"
echo ""
echo "4. 🔍 Verifique o funcionamento:"
echo "   - Teste o health check: curl http://localhost:8080/api/health"
echo "   - Teste o frontend: http://localhost"
echo ""
echo "5. 💾 Configure backups automáticos:"
echo "   - Execute ./backup.sh para backup manual"
echo "   - Configure cron job para backups automáticos"
echo ""
log_warning "IMPORTANTE: Nunca commite arquivos .env com dados sensíveis!"
echo ""