@echo off
echo ========================================
echo      INICIAR BACKEND PROMANDATO
echo ========================================
echo.

echo [1/4] Verificando se o backend esta rodando...
netstat -an | findstr :3002 >nul
if %errorlevel% equ 0 (
    echo Backend ja esta rodando na porta 3002!
    echo Acesse: http://localhost:3002
    goto menu
)

echo Backend nao esta rodando.
echo.

echo [2/4] Verificando dependencias...
cd backend
if not exist "node_modules" (
    echo Instalando dependencias...
    npm install
)

echo.
echo [3/4] Verificando dashboard...
if not exist "dashboard\dist" (
    echo Buildando dashboard...
    npm run build
)

echo.
echo [4/4] Iniciando servidor...
echo.
echo ========================================
echo         SERVIDOR INICIANDO...
echo ========================================
echo.
echo URLs disponiveis:
echo - Dashboard Admin: http://localhost:3002
echo - API Health: http://localhost:3002/api/health
echo - Landing Page: http://localhost:3002/landingpage
echo - API Docs: http://localhost:3002/api
echo.
echo Pressione Ctrl+C para parar o servidor
echo.

npm start

goto end

:menu
echo.
echo O que deseja fazer?
echo.
echo 1. Abrir Dashboard Admin
echo 2. Abrir API Health Check
echo 3. Abrir Landing Page via Backend
echo 4. Ver logs do servidor
echo 5. Parar servidor
echo 6. Sair
echo.
set /p choice="Digite sua escolha (1-6): "

if "%choice%"=="1" (
    start http://localhost:3002
    goto menu
)
if "%choice%"=="2" (
    start http://localhost:3002/api/health
    goto menu
)
if "%choice%"=="3" (
    start http://localhost:3002/landingpage
    goto menu
)
if "%choice%"=="4" (
    echo Verificando logs...
    curl http://localhost:3002/api/health
    goto menu
)
if "%choice%"=="5" (
    echo Parando servidor...
    taskkill /F /IM node.exe 2>nul
    echo Servidor parado.
    goto end
)
if "%choice%"=="6" (
    goto end
)

echo Opcao invalida!
goto menu

:end
echo.
echo Script finalizado.
pause
