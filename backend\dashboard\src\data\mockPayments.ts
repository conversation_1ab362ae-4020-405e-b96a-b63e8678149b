import { PaymentData, PaymentMetrics } from '../types/payment.types';

export const mockPayments: PaymentData[] = [
  {
    id: 'pi_1234567890',
    customerName: '<PERSON>',
    customerEmail: '<EMAIL>',
    planType: 'professional',
    amount: 19990,
    currency: 'brl',
    status: 'succeeded',
    billingCycle: 'monthly',
    createdAt: '2024-01-15T10:30:00Z',
    subscriptionId: 'sub_1234567890',
    invoiceId: 'in_1234567890'
  },
  {
    id: 'pi_1234567891',
    customerName: '<PERSON>',
    customerEmail: '<EMAIL>',
    planType: 'standard',
    amount: 107900,
    currency: 'brl',
    status: 'succeeded',
    billingCycle: 'yearly',
    createdAt: '2024-01-14T15:20:00Z',
    subscriptionId: 'sub_1234567891',
    invoiceId: 'in_1234567891'
  },
  {
    id: 'pi_1234567892',
    customerName: '<PERSON>',
    customerEmail: '<EMAIL>',
    planType: 'basic',
    amount: 4990,
    currency: 'brl',
    status: 'failed',
    billingCycle: 'monthly',
    createdAt: '2024-01-14T09:45:00Z',
    subscriptionId: 'sub_1234567892',
    invoiceId: 'in_1234567892'
  },
  {
    id: 'pi_1234567893',
    customerName: 'Ana Ferreira',
    customerEmail: '<EMAIL>',
    planType: 'standard',
    amount: 9990,
    currency: 'brl',
    status: 'pending',
    billingCycle: 'monthly',
    createdAt: '2024-01-13T14:10:00Z',
    subscriptionId: 'sub_1234567893',
    invoiceId: 'in_1234567893'
  }
];

export const mockMetrics: PaymentMetrics = {
  totalRevenue: 142870,
  monthlyRecurring: 34970,
  yearlyRecurring: 107900,
  totalCustomers: 4,
  successRate: 75,
  churnRate: 5.2,
  averageRevenuePerUser: 35717.5,
  conversionRate: 12.5
};
