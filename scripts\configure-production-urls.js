#!/usr/bin/env node

/**
 * Script para configurar URLs de produção
 * 
 * Uso:
 * node scripts/configure-production-urls.js
 */

import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Cores para output
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    cyan: '\x1b[36m'
};

function colorize(text, color) {
    return `${colors[color]}${text}${colors.reset}`;
}

// URLs padrão (você pode alterar aqui)
const PRODUCTION_URLS = {
    FRONTEND_URL: 'https://app.promandato.com.br',
    BACKEND_URL: 'https://api.promandato.com.br',
    LANDING_URL: 'https://promandato.com.br',
    ADMIN_URL: 'https://admin.promandato.com.br'
};

// Arquivos que precisam ser atualizados
const FILES_TO_UPDATE = [
    {
        path: 'landingpage/stripe-integration.js',
        replacements: [
            {
                from: "this.apiBaseUrl = 'http://localhost:3002/api';",
                to: `this.apiBaseUrl = '${PRODUCTION_URLS.BACKEND_URL}/api';`
            }
        ]
    },
    {
        path: 'landingpage/api-service.js',
        replacements: [
            {
                from: "this.baseURL = 'http://localhost:3002/api';",
                to: `this.baseURL = '${PRODUCTION_URLS.BACKEND_URL}/api';`
            }
        ]
    },
    {
        path: 'landingpage/script.js',
        replacements: [
            {
                from: 'http://localhost:3002',
                to: PRODUCTION_URLS.BACKEND_URL
            }
        ]
    },
    {
        path: 'landingpage/success.html',
        replacements: [
            {
                from: 'http://localhost:5174',
                to: PRODUCTION_URLS.FRONTEND_URL
            },
            {
                from: 'http://localhost:3002',
                to: PRODUCTION_URLS.BACKEND_URL
            }
        ]
    }
];

async function updateFile(fileConfig) {
    const filePath = path.resolve(fileConfig.path);
    
    try {
        if (!await fs.pathExists(filePath)) {
            console.log(colorize(`⚠️  Arquivo não encontrado: ${fileConfig.path}`, 'yellow'));
            return false;
        }

        let content = await fs.readFile(filePath, 'utf8');
        let updated = false;

        for (const replacement of fileConfig.replacements) {
            if (content.includes(replacement.from)) {
                content = content.replace(new RegExp(replacement.from.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), replacement.to);
                updated = true;
            }
        }

        if (updated) {
            await fs.writeFile(filePath, content);
            console.log(colorize(`✅ Atualizado: ${fileConfig.path}`, 'green'));
            return true;
        } else {
            console.log(colorize(`ℹ️  Nenhuma alteração necessária: ${fileConfig.path}`, 'blue'));
            return false;
        }

    } catch (error) {
        console.log(colorize(`❌ Erro ao atualizar ${fileConfig.path}: ${error.message}`, 'red'));
        return false;
    }
}

async function createEnvFile() {
    const envPath = path.resolve('backend/.env');
    
    const envContent = `# URLs de Produção - Pro-Mandato
FRONTEND_URL=${PRODUCTION_URLS.FRONTEND_URL}
BACKEND_URL=${PRODUCTION_URLS.BACKEND_URL}
LANDING_URL=${PRODUCTION_URLS.LANDING_URL}
ADMIN_URL=${PRODUCTION_URLS.ADMIN_URL}

# Stripe (substitua pelas suas chaves reais)
STRIPE_SECRET_KEY=***********************************************************************************************************
STRIPE_WEBHOOK_SECRET=whsec_...

# Email (configure para produção)
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=sua-senha-app
EMAIL_FROM=<EMAIL>

# Banco de Dados (se aplicável)
DATABASE_URL=postgresql://user:password@localhost:5432/promandato

# Outras configurações
NODE_ENV=production
PORT=3002
JWT_SECRET=seu-jwt-secret-super-seguro
`;

    try {
        await fs.writeFile(envPath, envContent);
        console.log(colorize(`✅ Arquivo .env criado: backend/.env`, 'green'));
        return true;
    } catch (error) {
        console.log(colorize(`❌ Erro ao criar .env: ${error.message}`, 'red'));
        return false;
    }
}

async function main() {
    console.log(colorize('\n🚀 CONFIGURADOR DE URLs DE PRODUÇÃO - PRO-MANDATO', 'cyan'));
    console.log(colorize('=' .repeat(60), 'cyan'));
    
    console.log(colorize('\n📋 URLs que serão configuradas:', 'yellow'));
    Object.entries(PRODUCTION_URLS).forEach(([key, url]) => {
        console.log(`  ${key}: ${url}`);
    });

    console.log(colorize('\n🔄 Atualizando arquivos...', 'blue'));
    
    let updatedCount = 0;
    for (const fileConfig of FILES_TO_UPDATE) {
        const updated = await updateFile(fileConfig);
        if (updated) updatedCount++;
    }

    console.log(colorize('\n📝 Criando arquivo .env...', 'blue'));
    await createEnvFile();

    console.log(colorize('\n📊 RESUMO:', 'cyan'));
    console.log(`  Arquivos atualizados: ${updatedCount}/${FILES_TO_UPDATE.length}`);
    console.log(`  Arquivo .env: Criado`);

    console.log(colorize('\n✅ CONFIGURAÇÃO CONCLUÍDA!', 'green'));
    console.log(colorize('\n📋 PRÓXIMOS PASSOS:', 'yellow'));
    console.log('1. Verifique as URLs no arquivo backend/.env');
    console.log('2. Configure suas chaves reais do Stripe');
    console.log('3. Configure o serviço de email');
    console.log('4. Teste em ambiente de staging antes da produção');
    console.log('5. Configure SSL/HTTPS nos seus domínios');

    console.log(colorize('\n🌐 URLs configuradas:', 'blue'));
    console.log(`  Landing Page: ${PRODUCTION_URLS.LANDING_URL}`);
    console.log(`  Sistema: ${PRODUCTION_URLS.FRONTEND_URL}`);
    console.log(`  API: ${PRODUCTION_URLS.BACKEND_URL}`);
    console.log(`  Admin: ${PRODUCTION_URLS.ADMIN_URL}`);
}

// Permitir customização via argumentos
if (process.argv.length > 2) {
    const customDomain = process.argv[2];
    PRODUCTION_URLS.FRONTEND_URL = `https://app.${customDomain}`;
    PRODUCTION_URLS.BACKEND_URL = `https://api.${customDomain}`;
    PRODUCTION_URLS.LANDING_URL = `https://${customDomain}`;
    PRODUCTION_URLS.ADMIN_URL = `https://admin.${customDomain}`;
    
    console.log(colorize(`\n🎯 Usando domínio customizado: ${customDomain}`, 'cyan'));
}

main().catch(error => {
    console.error(colorize(`\n❌ Erro: ${error.message}`, 'red'));
    process.exit(1);
});
