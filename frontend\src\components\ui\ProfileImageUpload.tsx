import React, { useState, useRef } from 'react';
import { Button } from './Button';
import { LoadingSpinner } from './LoadingSpinner';
import { uploadFileToStorage, deleteFileFromStorage } from '../../services/firebaseService';

interface ProfileImageUploadProps {
  currentImageUrl?: string;
  onImageUpdate: (newImageUrl: string) => void;
  userId: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

const sizeClasses = {
  sm: 'h-16 w-16',
  md: 'h-24 w-24',
  lg: 'h-32 w-32',
  xl: 'h-40 w-40'
};

export const ProfileImageUpload: React.FC<ProfileImageUploadProps> = ({
  currentImageUrl,
  onImageUpdate,
  userId,
  className = '',
  size = 'lg'
}) => {
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): string | null => {
    // Verificar tipo de arquivo
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return 'Formato não suportado. Use JPEG, PNG ou WebP.';
    }

    // Verificar tamanho (máximo 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return 'Arquivo muito grande. Máximo 5MB.';
    }

    return null;
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const validationError = validateFile(file);
    if (validationError) {
      setError(validationError);
      return;
    }

    setError(null);
    
    // Criar preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreviewUrl(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    // Fazer upload
    handleUpload(file);
  };

  const handleUpload = async (file: File) => {
    setIsUploading(true);
    setError(null);

    try {
      // Gerar caminho único para o arquivo
      const fileExtension = file.name.split('.').pop()?.toLowerCase() || 'jpg';
      const timestamp = Date.now();
      const fileName = `profile_${timestamp}.${fileExtension}`;
      const storagePath = `user_avatars/${userId}/${fileName}`;

      console.log('Iniciando upload da foto de perfil:', {
        userId,
        fileName,
        storagePath,
        fileSize: file.size,
        fileType: file.type
      });

      // Fazer upload
      const downloadUrl = await uploadFileToStorage(file, storagePath);
      
      console.log('Upload concluído com sucesso:', downloadUrl);

      // Atualizar a imagem
      onImageUpdate(downloadUrl);
      setPreviewUrl(null);

      // Limpar input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

    } catch (error) {
      console.error('Erro no upload da foto:', error);
      setError(error instanceof Error ? error.message : 'Erro ao fazer upload da foto');
      setPreviewUrl(null);
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemoveImage = async () => {
    if (!currentImageUrl) return;

    try {
      setIsUploading(true);
      
      // Se a URL atual é do Firebase Storage, tentar deletar
      if (currentImageUrl.includes('firebasestorage.googleapis.com')) {
        try {
          await deleteFileFromStorage(currentImageUrl);
        } catch (deleteError) {
          console.warn('Não foi possível deletar a imagem anterior:', deleteError);
        }
      }

      // Usar avatar padrão
      const defaultAvatar = `https://ui-avatars.com/api/?name=${encodeURIComponent(userId)}&background=random&size=200`;
      onImageUpdate(defaultAvatar);

    } catch (error) {
      console.error('Erro ao remover foto:', error);
      setError('Erro ao remover foto');
    } finally {
      setIsUploading(false);
    }
  };

  const displayImageUrl = previewUrl || currentImageUrl || `https://ui-avatars.com/api/?name=${encodeURIComponent(userId)}&background=random&size=200`;

  return (
    <div className={`flex flex-col items-center space-y-4 ${className}`}>
      {/* Imagem atual/preview */}
      <div className={`relative ${sizeClasses[size]} rounded-full overflow-hidden bg-gray-200 dark:bg-neutral-medium`}>
        <img
          src={displayImageUrl}
          alt="Foto de perfil"
          className="w-full h-full object-cover"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(userId)}&background=random&size=200`;
          }}
        />
        
        {/* Overlay de loading */}
        {isUploading && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <LoadingSpinner size="sm" />
          </div>
        )}
      </div>

      {/* Botões de ação */}
      <div className="flex flex-col sm:flex-row gap-2">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={() => fileInputRef.current?.click()}
          disabled={isUploading}
          className="text-sm"
        >
          {currentImageUrl && !currentImageUrl.includes('ui-avatars.com') ? 'Alterar Foto' : 'Adicionar Foto'}
        </Button>

        {currentImageUrl && !currentImageUrl.includes('ui-avatars.com') && (
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={handleRemoveImage}
            disabled={isUploading}
            className="text-sm text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
          >
            Remover
          </Button>
        )}
      </div>

      {/* Input de arquivo (oculto) */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/jpeg,image/jpg,image/png,image/webp"
        onChange={handleFileSelect}
        className="hidden"
        aria-label="Selecionar foto de perfil"
      />

      {/* Mensagem de erro */}
      {error && (
        <div className="text-sm text-red-600 dark:text-red-400 text-center max-w-xs">
          {error}
        </div>
      )}

      {/* Dicas */}
      <div className="text-xs text-gray-500 dark:text-neutral-medium text-center max-w-xs">
        Formatos aceitos: JPEG, PNG, WebP<br />
        Tamanho máximo: 5MB
      </div>
    </div>
  );
};