@echo off
echo ========================================
echo   GERAR TOKEN PARA GITHUB ACTIONS
echo ========================================
echo.

echo [1/3] Verificando Firebase CLI...
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Firebase CLI nao encontrado. Instalando...
    npm install -g firebase-tools
)

echo.
echo [2/3] Fazendo login no Firebase...
echo (Se ja estiver logado, pode pular)
firebase login

echo.
echo [3/3] Gerando token para GitHub Actions...
echo.
echo ========================================
echo        IMPORTANTE: COPIE O TOKEN!
echo ========================================
echo.
echo O token que aparecer abaixo deve ser:
echo 1. COPIADO completamente
echo 2. CONFIGURADO no GitHub como secret
echo 3. Nome do secret: FIREBASE_TOKEN
echo.
echo Pressione qualquer tecla para gerar...
pause >nul

firebase login:ci

echo.
echo ========================================
echo           TOKEN GERADO!
echo ========================================
echo.
echo Proximos passos:
echo.
echo 1. COPIE o token acima
echo 2. Va para GitHub > Settings > Secrets > Actions
echo 3. Clique em "New repository secret"
echo 4. Nome: FIREBASE_TOKEN
echo 5. Valor: Cole o token
echo 6. Salve o secret
echo.
echo Depois faca um commit para testar:
echo git add .
echo git commit -m "test: github actions"
echo git push origin main
echo.

pause
