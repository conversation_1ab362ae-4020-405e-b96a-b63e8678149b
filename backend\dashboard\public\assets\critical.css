/* Critical CSS for initial page load */

/* Reset and base styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON><PERSON>', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #ffffff;
  color: #1f2937;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Dark mode */
body.dark {
  background-color: #111827;
  color: #f9fafb;
}

/* Loading spinner styles */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Critical layout styles */
#root {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Hide content until fully loaded to prevent FOUC */
.app-loading {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.app-loaded {
  opacity: 1;
}

/* Basic button styles */
button {
  cursor: pointer;
  border: none;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Basic form styles */
input, textarea, select {
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  padding: 0.5rem;
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Dark mode form styles */
.dark input, .dark textarea, .dark select {
  background-color: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

.dark input:focus, .dark textarea:focus, .dark select:focus {
  border-color: #60a5fa;
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
}
