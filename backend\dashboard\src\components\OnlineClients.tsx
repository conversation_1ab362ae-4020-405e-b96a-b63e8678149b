import React, { useState, useEffect } from 'react';
import {
  Users,
  Search,
  Globe,
  Clock,
  Activity,
  Eye,
  Wifi,
  Monitor,
  Smartphone,
  Tablet,
  RefreshCw,
  MapPin,
  Timer
} from 'lucide-react';
import { getClientsStatus, subscribeToOnlineUsers, ClientStatus } from '../services/firebaseService';

// Interface removida - usando ClientStatus do firebaseService

const OnlineClients: React.FC = () => {
  console.log('🔄 OnlineClients component iniciado');

  const [onlineClients, setOnlineClients] = useState<ClientStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [planFilter, setPlanFilter] = useState<string>('all');
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Carregar dados iniciais
  useEffect(() => {
    const loadClientsData = async () => {
      try {
        setLoading(true);
        setError(null);
        console.log('🔄 Carregando dados reais dos clientes do Firestore...');

        // Carregar dados reais do Firebase
        const clientsData = await getClientsStatus();
        console.log('✅ Dados reais carregados:', clientsData.length, 'clientes');

        if (clientsData.length === 0) {
          console.log('⚠️ Nenhum cliente encontrado. Verifique se os usuários estão criados no Firestore.');
          setError('Nenhum cliente encontrado. Os usuários demo serão criados automaticamente.');
        }

        setOnlineClients(clientsData);

      } catch (err) {
        console.error('❌ Erro ao carregar dados do Firebase:', err);
        setError('Erro ao carregar dados dos clientes do Firestore');
        setOnlineClients([]); // Não usar dados fake
      } finally {
        setLoading(false);
      }
    };

    loadClientsData();
  }, []);

  // Configurar listener para atualizações em tempo real
  useEffect(() => {
    console.log('🔄 Configurando listener para atualizações em tempo real...');

    const unsubscribe = subscribeToOnlineUsers(async (onlineUsers) => {
      try {
        console.log('📡 Atualizações recebidas:', onlineUsers.length, 'usuários online');
        // Recarregar dados completos quando houver mudanças
        const clientsData = await getClientsStatus();
        setOnlineClients(clientsData);
      } catch (err) {
        console.error('❌ Erro ao atualizar dados:', err);
      }
    });

    return () => {
      console.log('🔚 Removendo listener...');
      unsubscribe();
    };
  }, []);

  // Filtrar clientes
  const filteredClients = onlineClients.filter(client => {
    const matchesSearch = (client.name || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (client.email || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (client.organization || '').toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || client.status === statusFilter;
    const matchesPlan = planFilter === 'all' || client.plan === planFilter;

    return matchesSearch && matchesStatus && matchesPlan;
  });

  // Atualizar dados
  const refreshData = async () => {
    setIsRefreshing(true);
    try {
      console.log('🔄 Atualizando dados manualmente...');
      const clientsData = await getClientsStatus();
      setOnlineClients(clientsData);
      console.log('✅ Dados atualizados:', clientsData.length, 'clientes');
    } catch (err) {
      console.error('❌ Erro ao atualizar dados:', err);
      setError('Erro ao atualizar dados');
    } finally {
      setIsRefreshing(false);
    }
  };

  // Funções de utilidade
  const formatDuration = (minutes?: number) => {
    if (!minutes || minutes === 0) return '0m';
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const formatLastActivity = (timestamp?: string) => {
    if (!timestamp) return 'Nunca';

    try {
      const now = new Date();
      const activity = new Date(timestamp);
      const diffMinutes = Math.floor((now.getTime() - activity.getTime()) / (1000 * 60));

      if (diffMinutes < 1) return 'Agora';
      if (diffMinutes < 60) return `${diffMinutes}m atrás`;
      const hours = Math.floor(diffMinutes / 60);
      return `${hours}h atrás`;
    } catch (error) {
      return 'Data inválida';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/30';
      case 'offline': return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-800/50';
      case 'away': return 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/30';
      case 'busy': return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/30';
      default: return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-800/50';
    }
  };

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'basic': return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-800/50';
      case 'standard': return 'text-blue-600 bg-blue-100 dark:text-blue-400 dark:bg-blue-900/30';
      case 'professional': return 'text-purple-600 bg-purple-100 dark:text-purple-400 dark:bg-purple-900/30';
      default: return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-800/50';
    }
  };

  const getDeviceIcon = (device: string) => {
    switch (device.toLowerCase()) {
      case 'mobile': return <Smartphone className="h-4 w-4 text-gray-600 dark:text-gray-400" />;
      case 'tablet': return <Tablet className="h-4 w-4 text-gray-600 dark:text-gray-400" />;
      default: return <Monitor className="h-4 w-4 text-gray-600 dark:text-gray-400" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online': return <Wifi className="h-4 w-4 text-green-600 dark:text-green-400" />;
      case 'offline': return <Globe className="h-4 w-4 text-gray-600 dark:text-gray-400" />;
      case 'away': return <Clock className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />;
      case 'busy': return <Activity className="h-4 w-4 text-red-600 dark:text-red-400" />;
      default: return <Globe className="h-4 w-4 text-gray-600 dark:text-gray-400" />;
    }
  };

  // Mostrar loading
  if (loading) {
    return (
      <div className="min-h-screen">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 animate-spin text-blue-600 dark:text-blue-400 mx-auto mb-4" />
              <p className="text-gray-600 dark:text-gray-300">Carregando dados dos clientes...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Mensagem de erro */}
        {error && (
          <div className="mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div className="flex items-center">
              <Activity className="h-5 w-5 text-red-500 dark:text-red-400 mr-2" />
              <p className="text-red-700 dark:text-red-300">{error}</p>
              <button
                onClick={refreshData}
                className="ml-auto text-red-600 dark:text-red-400 hover:text-red-800 dark:hover:text-red-300"
              >
                Tentar novamente
              </button>
            </div>
          </div>
        )}
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white flex items-center">
              <Users className="h-6 w-6 mr-2 text-blue-600 dark:text-blue-400" />
              Clientes Online
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              {filteredClients.length} clientes conectados agora
            </p>
          </div>
          <button
            onClick={refreshData}
            disabled={isRefreshing}
            className="flex items-center px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Atualizar
          </button>
        </div>

        {/* Estatísticas Rápidas */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow dark:shadow-gray-900/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Online</p>
                <p className="text-2xl font-bold text-green-600 dark:text-green-400">{onlineClients.filter(c => c.status === 'online').length}</p>
              </div>
              <Wifi className="h-8 w-8 text-green-600 dark:text-green-400" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow dark:shadow-gray-900/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Offline</p>
                <p className="text-2xl font-bold text-gray-600 dark:text-gray-400">{onlineClients.filter(c => c.status === 'offline').length}</p>
              </div>
              <Globe className="h-8 w-8 text-gray-600 dark:text-gray-400" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow dark:shadow-gray-900/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Clientes</p>
                <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">{onlineClients.length}</p>
              </div>
              <Users className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow dark:shadow-gray-900/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Sessão Média</p>
                <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                  {onlineClients.filter(c => c.sessionDuration).length > 0
                    ? formatDuration(Math.round(onlineClients.filter(c => c.sessionDuration).reduce((acc, c) => acc + (c.sessionDuration || 0), 0) / onlineClients.filter(c => c.sessionDuration).length))
                    : '0m'
                  }
                </p>
              </div>
              <Timer className="h-8 w-8 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </div>

        {/* Filtros */}
        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow dark:shadow-gray-900/20 mb-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="h-4 w-4 absolute left-3 top-3 text-gray-400 dark:text-gray-500" />
                <input
                  type="text"
                  placeholder="Buscar clientes..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                />
              </div>
            </div>
            <select
              aria-label="Filtrar por status"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="all">Todos os status</option>
              <option value="online">Online</option>
              <option value="offline">Offline</option>
              <option value="away">Ausente</option>
              <option value="busy">Ocupado</option>
            </select>
            <select
              aria-label="Filtrar por plano"
              value={planFilter}
              onChange={(e) => setPlanFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="all">Todos os planos</option>
              <option value="basic">Básico</option>
              <option value="standard">Padrão</option>
              <option value="professional">Profissional</option>
            </select>
          </div>
        </div>

        {/* Lista de Clientes */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow dark:shadow-gray-900/20 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Cliente
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Plano
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Sessão
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Dispositivo
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Localização
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Página Atual
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Ações
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredClients.map((client) => (
                  <tr key={client.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900 dark:text-white">{client.name}</div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">{client.email}</div>
                        <div className="text-xs text-gray-400 dark:text-gray-500">{client.organization}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {getStatusIcon(client.status)}
                        <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(client.status)}`}>
                          {client.status}
                        </span>
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {formatLastActivity(client.lastActivity)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPlanColor(client.plan)}`}>
                        {client.plan}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      <div>{formatDuration(client.sessionDuration)}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{client.actionsToday || 0} ações hoje</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-900 dark:text-white">
                        {getDeviceIcon(client.device || 'Desktop')}
                        <span className="ml-2">{client.device || 'Não informado'}</span>
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{client.browser || 'Não informado'}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-900 dark:text-white">
                        <MapPin className="h-3 w-3 mr-1 text-gray-500 dark:text-gray-400" />
                        {client.location || 'Não informado'}
                      </div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{client.ipAddress || 'IP não disponível'}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                      <code className="bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded text-xs text-gray-800 dark:text-gray-200">
                        {client.currentPage || '/'}
                      </code>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300">
                        <Eye className="h-4 w-4" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {filteredClients.length === 0 && (
          <div className="text-center py-12">
            <Users className="h-12 w-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Nenhum cliente encontrado</h3>
            <p className="text-gray-600 dark:text-gray-300">Tente ajustar os filtros ou aguarde novos clientes se conectarem.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default OnlineClients;
