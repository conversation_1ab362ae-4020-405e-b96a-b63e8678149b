export interface User {
  id: string;
  name: string;
  email: string;
  role: 'ADMIN' | 'MANAGER' | 'USER';
  permissions: string[];
  isActive: boolean;
  emailVerified: boolean;
  twoFactorEnabled?: boolean;
  lastLogin: string | null;
  planId?: string;
  plan?: {
    id: string;
    name: string;
    features: {
      aiFeatures: Array<{
        id: string;
        name: string;
        description: string;
        category: string;
        enabled: boolean;
      }>;
    };
  };
  profile: {
    avatar?: string;
    phone?: string;
    department?: string;
    position?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface UpdateProfileData {
  name: string;
  email: string;
  currentPassword?: string;
  newPassword?: string;
}