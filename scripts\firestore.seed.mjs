import { initializeApp } from 'firebase/app';
import { getFirestore, collection, addDoc, Timestamp, doc, setDoc, serverTimestamp } from 'firebase/firestore';
import { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword } from 'firebase/auth';

// Configuração do Firebase para o script de seed
// IMPORTANTE: Verifique se storageBucket corresponde ao que está funcionando no seu app principal (constants.tsx)
const FIREBASE_CONFIG = {
  apiKey: "AIzaSyB04qDOcU5jdZAYy-yaYQRUrrYQ_l49vcY",
  authDomain: "promandato-9a4cf.firebaseapp.com",
  projectId: "promandato-9a4cf",
  storageBucket: "promandato-9a4cf.firebasestorage.app", // Corrigido para o formato correto do Firebase Storage
  messagingSenderId: "517140455601",
  appId: "1:517140455601:web:fa2eb0ec2f88b506594290",
  measurementId: "G-MXYM2GKJS5"
};

// Inicializa o Firebase
const app = initializeApp(FIREBASE_CONFIG);
const db = getFirestore(app);
const auth = getAuth(app);

// --- Dados dos Usuários ---
const adminUserData = {
  email: '<EMAIL>',
  password: 'admin123',
  fullName: 'Administrador Principal',
  role: 'admin'  // Certifique-se de que é 'admin' em minúsculas
};

const staffUserData = {
  email: '<EMAIL>',
  password: 'staff123',
  fullName: 'Membro da Equipe',
  role: 'staff',  // Certifique-se de que é 'staff' em minúsculas
  masterId: '' // Será preenchido com o ID do admin após a criação
};

// Adicione um usuário master explícito se necessário
const masterUserData = {
  email: '<EMAIL>',
  password: 'master123',
  fullName: 'Usuário Master',
  role: 'master'  // Certifique-se de que é 'master' em minúsculas
};

// --- Dados do Perfil do Político ---
const politicianProfileData = {
  id: 'main_profile', // ID padrão para o perfil principal
  name: 'Dr. Carlos Silva',
  politicalParty: 'Partido da Inovação (INOVA)',
  birthDate: '1975-03-15',
  bio: 'Advogado e professor universitário com mais de 20 anos de experiência em direito público. Dedicado à luta por uma sociedade mais justa, com foco em educação, saúde e transparência pública. Atualmente vereador em segundo mandato, buscando sempre a inovação na gestão pública e o diálogo constante com a comunidade.',
  photoUrl: 'https://picsum.photos/seed/politicopro/400/400', // URL de placeholder para a foto
  contact: {
    phone: '(11) 99999-1001',
    email: '<EMAIL>',
    officePhone: '(11) 3030-2020'
  },
  address: {
    street: 'Praça da Democracia',
    number: '100',
    complement: 'Gabinete 10',
    neighborhood: 'Centro Cívico',
    city: 'Metrópolis',
    state: 'SP',
    zipCode: '01001-000'
  },
  socialMedia: {
    facebook: 'https://facebook.com/dr.carlossilva.oficial',
    instagram: '@dr.carlossilva.oficial',
    twitter: '@DrCarlosSilva',
    linkedin: 'https://linkedin.com/in/drcarlossilva',
    tiktok: '@drcarlossilva.inovacao',
    youtube: 'https://youtube.com/c/DrCarlosSilvaChannel',
    website: 'https://www.drcarlossilva.com.br'
  }
};

// --- Dados das Demandas ---
const demandsData = [
  { title: 'Reforma da Escola Municipal Aprender Mais', description: 'Solicitação urgente de reforma estrutural e modernização dos equipamentos da Escola Municipal Aprender Mais, no bairro Educação.', status: 'pending', priority: 'high', deadline: '2025-07-30' },
  { title: 'Ampliação do Posto de Saúde Vida Plena', description: 'Necessidade de ampliação do Posto de Saúde Vida Plena para atender a crescente demanda do bairro Bem Estar, incluindo novas salas de consulta e equipamentos.', status: 'in_progress', priority: 'high', deadline: '2025-09-15' },
  { title: 'Criação de Ciclovia na Avenida Progresso', description: 'Projeto para implementação de ciclovia segura e bem sinalizada ao longo da Avenida Progresso, incentivando o transporte sustentável.', status: 'pending', priority: 'medium', deadline: '2025-08-20' },
  { title: 'Instalação de Wi-Fi Público na Praça da Juventude', description: 'Disponibilização de acesso gratuito à internet na Praça da Juventude, fomentando a inclusão digital.', status: 'completed', priority: 'low', deadline: '2025-04-10' },
  { title: 'Programa de Coleta Seletiva no Bairro Recanto Verde', description: 'Implementação de um programa eficiente de coleta seletiva e compostagem para o Bairro Recanto Verde.', status: 'pending', priority: 'medium', deadline: '2025-10-01' },
  { title: 'Cancelamento de Evento Comunitário (COVID-19)', description: 'Evento "Festa da Primavera" cancelado devido a restrições sanitárias.', status: 'cancelled', priority: 'low', deadline: '2025-03-01' }
];

// --- Dados dos Cidadãos ---
const citizensData = [
  { fullName: 'Ana Pereira', email: '<EMAIL>', phone: '(11) 98888-1111', address: 'Rua das Palmeiras, 45', neighborhood: 'Bairro Educação', tags: ['Apoiadora Fiel', 'Professora'], workplace: 'Escola Aprender Mais', profession: 'Educadora' },
  { fullName: 'Bruno Costa', email: '<EMAIL>', phone: '(11) 97777-2222', address: 'Avenida das Acácias, 78', neighborhood: 'Bairro Bem Estar', tags: ['Líder Comunitário', 'Saúde'], socialMedia: 'https://facebook.com/bruno.costa.saude', notes: 'Muito ativo nas reuniões do conselho de saúde.' },
  { fullName: 'Clara Dias', email: '<EMAIL>', phone: '(11) 96666-3333', address: 'Alameda dos Ipês, 101', neighborhood: 'Centro Cívico', tags: ['Jovem Liderança', 'Estudante'], birthDate: '2002-06-25' },
  { fullName: 'Daniel Andrade', email: '<EMAIL>', phone: '(11) 95555-4444', address: 'Travessa das Orquídeas, 33', neighborhood: 'Bairro Recanto Verde', tags: ['Empresário Local', 'Sustentabilidade'], whatsapp: 'Sim' },
  { fullName: 'Elena Bernardes', email: '<EMAIL>', phone: '(11) 94444-5555', address: 'Rua dos Girassóis, 210', neighborhood: 'Vila Esperança', tags: ['Aposentada', 'Voluntária'], notes: 'Participa ativamente de projetos sociais.' }
];

// --- Dados dos Eventos da Agenda ---
const getAgendaEvents = [
  { title: 'Reunião com Secretário de Educação', description: 'c.', start: '2025-06-05T10:00:00', end: '2025-06-05T11:30:00', location: 'Secretaria de Educação', attendees: ['Secretário de Educação', 'Diretora da Escola'] },
  { title: 'Visita ao Posto de Saúde Vida Plena', description: 'Verificar o andamento das obras de ampliação.', start: '2025-06-10T15:00:00', end: '2025-06-10T16:00:00', location: 'Posto de Saúde Vida Plena' },
  { title: 'Audiência Pública sobre Mobilidade Urbana', description: 'Debate sobre a nova ciclovia da Avenida Progresso.', start: '2025-06-15T19:00:00', end: '2025-06-15T21:00:00', location: 'Câmara Municipal', isAllDay: false },
  { title: 'Inauguração da Praça Digital', description: 'Entrega do Wi-Fi público na Praça da Juventude.', start: '2025-04-08T17:00:00', end: '2025-04-08T18:00:00', location: 'Praça da Juventude' }, // Evento passado
  { title: 'Workshop de Planejamento Estratégico', description: 'Definição de metas para o próximo semestre.', start: '2025-07-01T09:00:00', end: '2025-07-01T17:00:00', location: 'Gabinete', isAllDay: true }
];

// --- Dados dos Documentos (Metadados) ---
const documentsData = [
  { name: 'Ata da Reunião - Conselho de Saúde 05.2025.pdf', type: 'application/pdf', url: 'https://firebasestorage.googleapis.com/v0/b/promandato-9a4cf.appspot.com/o/placeholder%2Fata_reuniao.pdf?alt=media', storagePath: 'placeholder/ata_reuniao.pdf', size: 786432 },
  { name: 'Relatório de Impacto Ambiental - Ciclovia Progresso.docx', type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', url: 'https://firebasestorage.googleapis.com/v0/b/promandato-9a4cf.appspot.com/o/placeholder%2Frelatorio_ciclovia.docx?alt=media', storagePath: 'placeholder/relatorio_ciclovia.docx', size: 1205824 },
  { name: 'Orçamento Detalhado - Reforma Escola Aprender Mais.xlsx', type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', url: 'https://firebasestorage.googleapis.com/v0/b/promandato-9a4cf.appspot.com/o/placeholder%2Forcamento_escola.xlsx?alt=media', storagePath: 'placeholder/orcamento_escola.xlsx', size: 302148 },
  { name: 'Apresentação do Projeto - Praça Digital.pptx', type: 'application/vnd.openxmlformats-officedocument.presentationml.presentation', url: 'https://firebasestorage.googleapis.com/v0/b/promandato-9a4cf.appspot.com/o/placeholder%2Fapresentacao_praca.pptx?alt=media', storagePath: 'placeholder/apresentacao_praca.pptx', size: 2100450 },
  { name: 'Minuta do Projeto de Lei - Incentivo ao Esporte Amador.pdf', type: 'application/pdf', url: 'https://firebasestorage.googleapis.com/v0/b/promandato-9a4cf.appspot.com/o/placeholder%2Fpl_esporte.pdf?alt=media', storagePath: 'placeholder/pl_esporte.pdf', size: 540080 }
];
// Note: As URLs dos documentos acima são placeholders. Para uma apresentação real, você pode querer subir alguns arquivos de exemplo para esses caminhos.

// --- Dados das Configurações do Local de Trabalho (Workspace) ---
const workspaceSettingsData = {
  id: 'default_settings', // ID padrão
  institutionName: 'Gabinete Vereador Dr. Carlos Silva',
  logoUrl: 'https://picsum.photos/seed/gabinete_logo/200/100?grayscale', // Placeholder
  addressLine1: 'Praça da Democracia, 100',
  addressLine2: 'Gabinete 10, Anexo B',
  city: 'Metrópolis',
  state: 'SP',
  zipCode: '01001-000',
  phone: '(11) 3030-2020',
  email: '<EMAIL>',
  website: 'https://www.gabinetedrcarlossilva.com.br'
};

// --- Dados dos Membros da Equipe ---
const teamMembersData = [
  {
    name: 'Ana Silva',
    roleInTeam: 'Assessora de Comunicação',
    permissions: ['view_demands', 'edit_demands', 'manage_social_media'],
    joinedAt: new Date().toISOString(),
    userId: 'user-ana-silva',
  },
  {
    name: 'Carlos Oliveira',
    roleInTeam: 'Assessor Jurídico',
    permissions: ['view_demands', 'edit_demands', 'view_documents'],
    joinedAt: new Date().toISOString(),
    userId: 'user-carlos-oliveira',
  },
  {
    name: 'Mariana Santos',
    roleInTeam: 'Coordenadora de Gabinete',
    permissions: ['view_demands', 'edit_demands', 'manage_citizens', 'manage_agenda', 'view_documents', 'manage_social_media', 'view_reports'],
    joinedAt: new Date().toISOString(),
    userId: 'user-mariana-santos',
  }
];

// --- Função Principal de Seed ---
async function seedDatabase() {
  let adminUid = '';
  let staffUid = '';

  try {
    console.log('Iniciando seed do banco de dados...');

    // 1. Criar/Logar Usuário Admin
    try {
      const adminCredential = await createUserWithEmailAndPassword(auth, adminUserData.email, adminUserData.password);
      adminUid = adminCredential.user.uid;
      await setDoc(doc(db, 'users', adminUid), { ...adminUserData, uid: adminUid, id: adminUid, createdAt: serverTimestamp(), updatedAt: serverTimestamp() });
      console.log('Usuário admin criado com sucesso:', adminUserData.email);
    } catch (error) {
      if (error.code === 'auth/email-already-in-use') {
        const adminCredential = await signInWithEmailAndPassword(auth, adminUserData.email, adminUserData.password);
        adminUid = adminCredential.user.uid;
        // Garante que os dados do admin (como a role) estão corretos no Firestore
        await setDoc(doc(db, 'users', adminUid), { ...adminUserData, uid: adminUid, id: adminUid, updatedAt: serverTimestamp() }, { merge: true });
        console.log('Usuário admin já existe, login realizado:', adminUserData.email);
      } else { throw error; }
    }

    // Atualizar o masterId no staffUserData
    staffUserData.masterId = adminUid;

    // 2. Criar/Logar Usuário Staff
    try {
      const staffCredential = await createUserWithEmailAndPassword(auth, staffUserData.email, staffUserData.password);
      staffUid = staffCredential.user.uid;
      await setDoc(doc(db, 'users', staffUid), { ...staffUserData, uid: staffUid, id: staffUid, createdAt: serverTimestamp(), updatedAt: serverTimestamp() });
      console.log('Usuário staff criado com sucesso:', staffUserData.email);
    } catch (error) {
      if (error.code === 'auth/email-already-in-use') {
        const staffCredential = await signInWithEmailAndPassword(auth, staffUserData.email, staffUserData.password);
        staffUid = staffCredential.user.uid;
        await setDoc(doc(db, 'users', staffUid), { ...staffUserData, uid: staffUid, id: staffUid, updatedAt: serverTimestamp() }, { merge: true });
        console.log('Usuário staff já existe, login realizado:', staffUserData.email);
      } else { console.warn('Não foi possível criar usuário staff, continuando com admin apenas para createdBy:', error.message); }
    }
    
    const creatorId = adminUid; // Usar adminUid para todos os createdBy para simplificar

    // Criar usuário master
    try {
      const masterCredential = await createUserWithEmailAndPassword(auth, masterUserData.email, masterUserData.password);
      const masterUid = masterCredential.user.uid;
      await setDoc(doc(db, 'users', masterUid), { 
        ...masterUserData, 
        uid: masterUid, 
        id: masterUid, 
        createdAt: serverTimestamp(), 
        updatedAt: serverTimestamp() 
      });
      console.log('Usuário master criado com sucesso:', masterUserData.email);
    } catch (error) {
      if (error.code === 'auth/email-already-in-use') {
        const masterCredential = await signInWithEmailAndPassword(auth, masterUserData.email, masterUserData.password);
        const masterUid = masterCredential.user.uid;
        await setDoc(doc(db, 'users', masterUid), { 
          ...masterUserData, 
          uid: masterUid, 
          id: masterUid, 
          updatedAt: serverTimestamp() 
        }, { merge: true });
        console.log('Usuário master já existe, login realizado:', masterUserData.email);
      } else { 
        console.warn('Não foi possível criar usuário master:', error.message); 
      }
    }

    // 3. Salvar Perfil do Político (vinculado ao adminUid)
    await setDoc(doc(db, 'politician_profiles', politicianProfileData.id), {
      ...politicianProfileData,
      userId: adminUid, // Vincula ao admin
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
    console.log('Perfil do político populado com sucesso.');

    // 4. Salvar Demandas
    for (const demand of demandsData) {
      await addDoc(collection(db, 'demands'), {
        ...demand,
        masterId: adminUid, // Adicione o masterId
        deadline: demand.deadline ? Timestamp.fromDate(new Date(demand.deadline)) : null,
        createdBy: creatorId,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
    }
    console.log(`${demandsData.length} demandas populadas com sucesso.`);

    // 5. Salvar Cidadãos
    for (const citizen of citizensData) {
      await addDoc(collection(db, 'citizens'), {
        ...citizen,
        masterId: adminUid, // Adicione o masterId
        birthDate: citizen.birthDate ? Timestamp.fromDate(new Date(citizen.birthDate)) : null,
        createdBy: creatorId,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
    }
    console.log(`${citizensData.length} cidadãos populados com sucesso.`);

    // 6. Salvar Eventos da Agenda
    for (const event of getAgendaEvents) {
      await addDoc(collection(db, 'agendaEvents'), {
        ...event,
        masterId: adminUid, // Adicione o masterId
        start: Timestamp.fromDate(new Date(event.start)),
        end: Timestamp.fromDate(new Date(event.end)),
        createdBy: creatorId,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
    }
    console.log(`${getAgendaEvents.length} eventos da agenda populados com sucesso.`);

    // 7. Salvar Documentos (Metadados)
    for (const document of documentsData) {
      await addDoc(collection(db, 'documents'), {
        ...document,
        masterId: adminUid, // Adicione o masterId
        uploadedBy: creatorId, //  Mudado de 'uploadedBy' para 'createdBy' se for o caso, ou manter uploadedBy.
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp() // Adicionando updatedAt se faltar
      });
    }
    console.log(`${documentsData.length} metadados de documentos populados com sucesso.`);

    // 8. Salvar Configurações do Local de Trabalho (Workspace)
    await setDoc(doc(db, 'workspaces', workspaceSettingsData.id), {
        ...workspaceSettingsData,
        updatedAt: serverTimestamp(),
        createdAt: serverTimestamp() // Adicionando createdAt
    });
    console.log('Configurações do Local de Trabalho (Workspace) populadas com sucesso.');

    // 9. Salvar Membros da Equipe
    for (const member of teamMembersData) {
      await addDoc(collection(db, 'team_members'), {
        ...member,
        masterId: adminUid, // Adicione o masterId
        createdBy: creatorId,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      });
    }
    console.log(`${teamMembersData.length} membros da equipe populados com sucesso.`);

    console.log('\nBanco de dados populado com sucesso para apresentação!');
    process.exit(0);

  } catch (error) {
    console.error('\nErro detalhado ao popular o banco de dados:', error);
    if (error.code === 'permission-denied') {
      console.error('\n--- ERRO DE PERMISSÃO NO FIRESTORE ---');
      console.error('Verifique suas Regras de Segurança do Firestore.');
      console.error('O usuário autenticado pelo script (admin@example.<NAME_EMAIL>) precisa de permissão para escrever nas coleções.');
      console.error('Exemplo de regra para permitir escrita por admin (verifique o campo "role" no doc do usuário):');
      console.error("match /collectionName/{docId} { allow write: if get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin'; }");
    }
    process.exit(1);
  }
}

// Executa o seed
seedDatabase();
