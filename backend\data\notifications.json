[{"id": "86b58ff6-0505-4533-9d5a-ba39025a0a08", "userId": "57e408f1-096a-4c33-ae0f-34c6f9cd199f", "title": "Teste de Notificação", "message": "Esta é uma notificação de teste do sistema", "type": "info", "category": "general", "read": false, "actionUrl": null, "actionLabel": null, "metadata": {}, "createdAt": "2025-06-12T00:27:36.828Z", "readAt": null, "expiresAt": null}, {"id": "c291a9fc-e276-4f89-a213-6a6aeaebc9ac", "userId": "57e408f1-096a-4c33-ae0f-34c6f9cd199f", "title": "Nova demanda recebida", "message": "<PERSON> enviou uma solicitação sobre iluminação pública", "type": "info", "category": "demand", "read": false, "actionUrl": "/demands/1", "actionLabel": "<PERSON>er <PERSON>a", "metadata": {}, "createdAt": "2025-06-12T00:28:22.021Z", "readAt": null, "expiresAt": null}, {"id": "7c108e21-3985-43e5-9c61-a654e885b33e", "userId": "57e408f1-096a-4c33-ae0f-34c6f9cd199f", "title": "Evento próximo", "message": "Reunião pública agendada para amanhã às 14h", "type": "warning", "category": "event", "read": false, "actionUrl": "/agenda", "actionLabel": "Ver agenda", "metadata": {}, "createdAt": "2025-06-12T00:28:22.027Z", "readAt": null, "expiresAt": null}, {"id": "529ef933-6a85-4ef0-a191-857d9ee67822", "userId": "57e408f1-096a-4c33-ae0f-34c6f9cd199f", "title": "Demanda resolvida", "message": "A solicitação #123 foi marcada como concluída", "type": "success", "category": "demand", "read": false, "actionUrl": "/demands/123", "actionLabel": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "metadata": {}, "createdAt": "2025-06-12T00:28:22.030Z", "readAt": null, "expiresAt": null}, {"id": "21c57257-cdb4-4b94-bc60-09d7254dcede", "userId": "cb683970-3492-4ff7-a337-633543481a77", "title": "Nova demanda recebida", "message": "<PERSON> enviou uma solicitação sobre iluminação pública", "type": "info", "category": "demand", "read": false, "actionUrl": "/demands/1", "actionLabel": "<PERSON>er <PERSON>a", "metadata": {}, "createdAt": "2025-06-12T00:28:22.032Z", "readAt": null, "expiresAt": null}, {"id": "f69b17e3-377e-47a5-a733-92b475df84ed", "userId": "cb683970-3492-4ff7-a337-633543481a77", "title": "Evento próximo", "message": "Reunião pública agendada para amanhã às 14h", "type": "warning", "category": "event", "read": false, "actionUrl": "/agenda", "actionLabel": "Ver agenda", "metadata": {}, "createdAt": "2025-06-12T00:28:22.034Z", "readAt": null, "expiresAt": null}, {"id": "61317e2f-1ce8-4a69-b9c6-9618360b073f", "userId": "cb683970-3492-4ff7-a337-633543481a77", "title": "Demanda resolvida", "message": "A solicitação #123 foi marcada como concluída", "type": "success", "category": "demand", "read": false, "actionUrl": "/demands/123", "actionLabel": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "metadata": {}, "createdAt": "2025-06-12T00:28:22.035Z", "readAt": null, "expiresAt": null}, {"id": "a4d4a0b6-c664-449e-8247-92c9b406238d", "userId": "267a1f9e-1410-4d4f-a8c2-dc00b544b4bd", "title": "Nova demanda recebida", "message": "<PERSON> enviou uma solicitação sobre iluminação pública", "type": "info", "category": "demand", "read": false, "actionUrl": "/demands/1", "actionLabel": "<PERSON>er <PERSON>a", "metadata": {}, "createdAt": "2025-06-12T00:28:22.037Z", "readAt": null, "expiresAt": null}, {"id": "d0afe1d9-0d69-46f9-945d-32833f22d54d", "userId": "267a1f9e-1410-4d4f-a8c2-dc00b544b4bd", "title": "Evento próximo", "message": "Reunião pública agendada para amanhã às 14h", "type": "warning", "category": "event", "read": false, "actionUrl": "/agenda", "actionLabel": "Ver agenda", "metadata": {}, "createdAt": "2025-06-12T00:28:22.039Z", "readAt": null, "expiresAt": null}, {"id": "77912eba-8df4-4063-a1a2-c872ab6d1ffa", "userId": "267a1f9e-1410-4d4f-a8c2-dc00b544b4bd", "title": "Demanda resolvida", "message": "A solicitação #123 foi marcada como concluída", "type": "success", "category": "demand", "read": false, "actionUrl": "/demands/123", "actionLabel": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "metadata": {}, "createdAt": "2025-06-12T00:28:22.041Z", "readAt": null, "expiresAt": null}, {"id": "39d071d1-24bc-48f6-98f7-b211e9f615de", "userId": "24ae461e-1f1f-4f29-a3d9-e336af9935be", "title": "Nova demanda recebida", "message": "<PERSON> enviou uma solicitação sobre iluminação pública", "type": "info", "category": "demand", "read": false, "actionUrl": "/demands/1", "actionLabel": "<PERSON>er <PERSON>a", "metadata": {}, "createdAt": "2025-06-12T00:28:22.046Z", "readAt": null, "expiresAt": null}, {"id": "8a1c6941-9a6a-40d1-908e-20755bf0ee6a", "userId": "24ae461e-1f1f-4f29-a3d9-e336af9935be", "title": "Evento próximo", "message": "Reunião pública agendada para amanhã às 14h", "type": "warning", "category": "event", "read": false, "actionUrl": "/agenda", "actionLabel": "Ver agenda", "metadata": {}, "createdAt": "2025-06-12T00:28:22.047Z", "readAt": null, "expiresAt": null}, {"id": "e61f6265-3f74-4d4e-abf3-377125bf09a9", "userId": "24ae461e-1f1f-4f29-a3d9-e336af9935be", "title": "Demanda resolvida", "message": "A solicitação #123 foi marcada como concluída", "type": "success", "category": "demand", "read": false, "actionUrl": "/demands/123", "actionLabel": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "metadata": {}, "createdAt": "2025-06-12T00:28:22.049Z", "readAt": null, "expiresAt": null}, {"id": "0cfaf32d-d6e6-4168-8b87-649ec88fa1c4", "userId": "52b3ab3e-7f5d-49b7-a07e-81757cf3d633", "title": "Nova demanda recebida", "message": "<PERSON> enviou uma solicitação sobre iluminação pública", "type": "info", "category": "demand", "read": false, "actionUrl": "/demands/1", "actionLabel": "<PERSON>er <PERSON>a", "metadata": {}, "createdAt": "2025-06-12T00:28:22.050Z", "readAt": null, "expiresAt": null}, {"id": "30722f9b-ef13-4508-9a6a-e6b0ff506d7d", "userId": "52b3ab3e-7f5d-49b7-a07e-81757cf3d633", "title": "Evento próximo", "message": "Reunião pública agendada para amanhã às 14h", "type": "warning", "category": "event", "read": false, "actionUrl": "/agenda", "actionLabel": "Ver agenda", "metadata": {}, "createdAt": "2025-06-12T00:28:22.052Z", "readAt": null, "expiresAt": null}, {"id": "b2b556e5-9db3-487c-b0d9-0594d7ff5eb8", "userId": "52b3ab3e-7f5d-49b7-a07e-81757cf3d633", "title": "Demanda resolvida", "message": "A solicitação #123 foi marcada como concluída", "type": "success", "category": "demand", "read": false, "actionUrl": "/demands/123", "actionLabel": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "metadata": {}, "createdAt": "2025-06-12T00:28:22.054Z", "readAt": null, "expiresAt": null}, {"id": "103c0020-721a-463a-8518-fb28aa0c962e", "userId": "7a904904-f84a-425c-beed-820ea822fa01", "title": "Nova demanda recebida", "message": "<PERSON> enviou uma solicitação sobre iluminação pública", "type": "info", "category": "demand", "read": false, "actionUrl": "/demands/1", "actionLabel": "<PERSON>er <PERSON>a", "metadata": {}, "createdAt": "2025-06-12T00:28:22.056Z", "readAt": null, "expiresAt": null}, {"id": "27d4fb93-a29f-4cd7-8a9d-5f41289698bd", "userId": "7a904904-f84a-425c-beed-820ea822fa01", "title": "Evento próximo", "message": "Reunião pública agendada para amanhã às 14h", "type": "warning", "category": "event", "read": false, "actionUrl": "/agenda", "actionLabel": "Ver agenda", "metadata": {}, "createdAt": "2025-06-12T00:28:22.061Z", "readAt": null, "expiresAt": null}, {"id": "270efea0-b3f5-409c-9b49-f6c3ca9fefe7", "userId": "7a904904-f84a-425c-beed-820ea822fa01", "title": "Demanda resolvida", "message": "A solicitação #123 foi marcada como concluída", "type": "success", "category": "demand", "read": false, "actionUrl": "/demands/123", "actionLabel": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "metadata": {}, "createdAt": "2025-06-12T00:28:22.063Z", "readAt": null, "expiresAt": null}, {"id": "066d5e50-c5ec-4cf3-b3f1-0da04c1bd153", "userId": "a2e35df8-8097-47b1-99a0-383ba7034aa1", "title": "Nova demanda recebida", "message": "<PERSON> enviou uma solicitação sobre iluminação pública", "type": "info", "category": "demand", "read": false, "actionUrl": "/demands/1", "actionLabel": "<PERSON>er <PERSON>a", "metadata": {}, "createdAt": "2025-06-12T00:28:22.064Z", "readAt": null, "expiresAt": null}, {"id": "e231d4ea-9e28-4055-8918-e4e39347c7ed", "userId": "a2e35df8-8097-47b1-99a0-383ba7034aa1", "title": "Evento próximo", "message": "Reunião pública agendada para amanhã às 14h", "type": "warning", "category": "event", "read": false, "actionUrl": "/agenda", "actionLabel": "Ver agenda", "metadata": {}, "createdAt": "2025-06-12T00:28:22.066Z", "readAt": null, "expiresAt": null}, {"id": "68a9f401-8c6c-4051-9e2b-5731626a490e", "userId": "a2e35df8-8097-47b1-99a0-383ba7034aa1", "title": "Demanda resolvida", "message": "A solicitação #123 foi marcada como concluída", "type": "success", "category": "demand", "read": false, "actionUrl": "/demands/123", "actionLabel": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "metadata": {}, "createdAt": "2025-06-12T00:28:22.067Z", "readAt": null, "expiresAt": null}, {"id": "77a82d00-496a-45d5-9217-4b76d0ec8e54", "userId": "57e408f1-096a-4c33-ae0f-34c6f9cd199f", "title": "Nova demanda URGENTE recebida", "message": "Cidadão reportou problema grave de saneamento na Rua Principal, 123", "type": "error", "category": "demand", "read": false, "actionUrl": "/demands/urgent-001", "actionLabel": "Ver demanda urgente", "metadata": {"priority": "urgent", "category": "saneamento", "location": "<PERSON><PERSON>, 123"}, "createdAt": "2025-06-12T00:57:41.461Z", "readAt": null, "expiresAt": null}, {"id": "e546c410-49bf-4328-8120-9f6bc9e5b9db", "userId": "57e408f1-096a-4c33-ae0f-34c6f9cd199f", "title": "Sistema atualizado com sucesso", "message": "Dashboard administrativo foi atualizado para versão 2.1.0 com novas funcionalidades", "type": "success", "category": "system", "read": false, "actionUrl": "/changelog", "actionLabel": "Ver novidades", "metadata": {"version": "2.1.0", "features": ["notificações", "relatórios", "dashboard"]}, "createdAt": "2025-06-12T00:57:41.465Z", "readAt": null, "expiresAt": null}, {"id": "ab178191-00a6-4aba-b003-da6d55e2adbd", "userId": "57e408f1-096a-4c33-ae0f-34c6f9cd199f", "title": "Reunião de equipe em 1 hora", "message": "Reunião semanal da equipe administrativa agendada para 14:00", "type": "warning", "category": "event", "read": false, "actionUrl": "/calendar/meeting-001", "actionLabel": "Ver agenda", "metadata": {"eventTime": "14:00", "participants": ["Admin", "G<PERSON><PERSON>"], "location": "Sala de reuniões"}, "createdAt": "2025-06-12T00:57:41.468Z", "readAt": null, "expiresAt": null}, {"id": "aea3536c-943c-49fa-bea6-d0a58b7e412b", "userId": "57e408f1-096a-4c33-ae0f-34c6f9cd199f", "title": "Relatório mensal disponível", "message": "Relatório de demandas do mês de dezembro está pronto para análise", "type": "info", "category": "general", "read": false, "actionUrl": "/reports/monthly/december", "actionLabel": "<PERSON>er relatório", "metadata": {"reportType": "monthly", "period": "dezemb<PERSON>", "totalDemands": 156, "resolved": 142}, "createdAt": "2025-06-12T00:57:41.471Z", "readAt": null, "expiresAt": null}, {"id": "34e14463-94fe-47b8-b22b-7d6c06fed61f", "userId": "57e408f1-096a-4c33-ae0f-34c6f9cd199f", "title": "Backup automático concluído", "message": "Backup diário dos dados foi realizado com sucesso às 02:00", "type": "success", "category": "system", "read": false, "actionUrl": "/admin/backups", "actionLabel": "Ver backups", "metadata": {"backupTime": "02:00", "size": "2.3 GB", "status": "success"}, "createdAt": "2025-06-12T00:57:41.473Z", "readAt": null, "expiresAt": null}, {"id": "06172314-5665-4b35-8173-f74e21043554", "userId": "52b3ab3e-7f5d-49b7-a07e-81757cf3d633", "title": "Nova demanda URGENTE recebida", "message": "Cidadão reportou problema grave de saneamento na Rua Principal, 123", "type": "error", "category": "demand", "read": false, "actionUrl": "/demands/urgent-001", "actionLabel": "Ver demanda urgente", "metadata": {"priority": "urgent", "category": "saneamento", "location": "<PERSON><PERSON>, 123"}, "createdAt": "2025-06-12T00:57:41.476Z", "readAt": null, "expiresAt": null}, {"id": "6db04de3-1b05-4ebb-ab26-1fded7a1a330", "userId": "52b3ab3e-7f5d-49b7-a07e-81757cf3d633", "title": "Sistema atualizado com sucesso", "message": "Dashboard administrativo foi atualizado para versão 2.1.0 com novas funcionalidades", "type": "success", "category": "system", "read": false, "actionUrl": "/changelog", "actionLabel": "Ver novidades", "metadata": {"version": "2.1.0", "features": ["notificações", "relatórios", "dashboard"]}, "createdAt": "2025-06-12T00:57:41.478Z", "readAt": null, "expiresAt": null}, {"id": "c0d5ab39-bbcf-49fc-8d2b-796655c2222a", "userId": "52b3ab3e-7f5d-49b7-a07e-81757cf3d633", "title": "Reunião de equipe em 1 hora", "message": "Reunião semanal da equipe administrativa agendada para 14:00", "type": "warning", "category": "event", "read": false, "actionUrl": "/calendar/meeting-001", "actionLabel": "Ver agenda", "metadata": {"eventTime": "14:00", "participants": ["Admin", "G<PERSON><PERSON>"], "location": "Sala de reuniões"}, "createdAt": "2025-06-12T00:57:41.479Z", "readAt": null, "expiresAt": null}, {"id": "c90309e9-f220-4d9c-97ca-4a19e4c555eb", "userId": "52b3ab3e-7f5d-49b7-a07e-81757cf3d633", "title": "Relatório mensal disponível", "message": "Relatório de demandas do mês de dezembro está pronto para análise", "type": "info", "category": "general", "read": false, "actionUrl": "/reports/monthly/december", "actionLabel": "<PERSON>er relatório", "metadata": {"reportType": "monthly", "period": "dezemb<PERSON>", "totalDemands": 156, "resolved": 142}, "createdAt": "2025-06-12T00:57:41.480Z", "readAt": null, "expiresAt": null}, {"id": "ac5ce6ff-2555-45d7-b0de-d1d8a68fa718", "userId": "52b3ab3e-7f5d-49b7-a07e-81757cf3d633", "title": "Backup automático concluído", "message": "Backup diário dos dados foi realizado com sucesso às 02:00", "type": "success", "category": "system", "read": false, "actionUrl": "/admin/backups", "actionLabel": "Ver backups", "metadata": {"backupTime": "02:00", "size": "2.3 GB", "status": "success"}, "createdAt": "2025-06-12T00:57:41.482Z", "readAt": null, "expiresAt": null}, {"id": "635028dd-7112-4828-9e5e-cc9f1b53e5b4", "userId": "7a904904-f84a-425c-beed-820ea822fa01", "title": "Nova demanda URGENTE recebida", "message": "Cidadão reportou problema grave de saneamento na Rua Principal, 123", "type": "error", "category": "demand", "read": false, "actionUrl": "/demands/urgent-001", "actionLabel": "Ver demanda urgente", "metadata": {"priority": "urgent", "category": "saneamento", "location": "<PERSON><PERSON>, 123"}, "createdAt": "2025-06-12T00:57:41.483Z", "readAt": null, "expiresAt": null}, {"id": "2d472e9f-dc3b-455c-94e0-71af062ad257", "userId": "7a904904-f84a-425c-beed-820ea822fa01", "title": "Sistema atualizado com sucesso", "message": "Dashboard administrativo foi atualizado para versão 2.1.0 com novas funcionalidades", "type": "success", "category": "system", "read": false, "actionUrl": "/changelog", "actionLabel": "Ver novidades", "metadata": {"version": "2.1.0", "features": ["notificações", "relatórios", "dashboard"]}, "createdAt": "2025-06-12T00:57:41.486Z", "readAt": null, "expiresAt": null}, {"id": "c9bc39a5-1d65-4957-9ca0-c21171637f98", "userId": "7a904904-f84a-425c-beed-820ea822fa01", "title": "Reunião de equipe em 1 hora", "message": "Reunião semanal da equipe administrativa agendada para 14:00", "type": "warning", "category": "event", "read": false, "actionUrl": "/calendar/meeting-001", "actionLabel": "Ver agenda", "metadata": {"eventTime": "14:00", "participants": ["Admin", "G<PERSON><PERSON>"], "location": "Sala de reuniões"}, "createdAt": "2025-06-12T00:57:41.489Z", "readAt": null, "expiresAt": null}, {"id": "49431e52-6ac5-4450-babe-53e96408b2c1", "userId": "7a904904-f84a-425c-beed-820ea822fa01", "title": "Relatório mensal disponível", "message": "Relatório de demandas do mês de dezembro está pronto para análise", "type": "info", "category": "general", "read": false, "actionUrl": "/reports/monthly/december", "actionLabel": "<PERSON>er relatório", "metadata": {"reportType": "monthly", "period": "dezemb<PERSON>", "totalDemands": 156, "resolved": 142}, "createdAt": "2025-06-12T00:57:41.491Z", "readAt": null, "expiresAt": null}, {"id": "5ed5bbb8-571c-4da6-abf7-857c0d49ec64", "userId": "7a904904-f84a-425c-beed-820ea822fa01", "title": "Backup automático concluído", "message": "Backup diário dos dados foi realizado com sucesso às 02:00", "type": "success", "category": "system", "read": false, "actionUrl": "/admin/backups", "actionLabel": "Ver backups", "metadata": {"backupTime": "02:00", "size": "2.3 GB", "status": "success"}, "createdAt": "2025-06-12T00:57:41.493Z", "readAt": null, "expiresAt": null}, {"id": "459a3ae2-d052-457f-a586-4c33d7ccbcae", "userId": "a2e35df8-8097-47b1-99a0-383ba7034aa1", "title": "Nova demanda URGENTE recebida", "message": "Cidadão reportou problema grave de saneamento na Rua Principal, 123", "type": "error", "category": "demand", "read": false, "actionUrl": "/demands/urgent-001", "actionLabel": "Ver demanda urgente", "metadata": {"priority": "urgent", "category": "saneamento", "location": "<PERSON><PERSON>, 123"}, "createdAt": "2025-06-12T00:57:41.496Z", "readAt": null, "expiresAt": null}, {"id": "6b034449-9e99-48a7-9aa6-ebca3e18bd8d", "userId": "a2e35df8-8097-47b1-99a0-383ba7034aa1", "title": "Sistema atualizado com sucesso", "message": "Dashboard administrativo foi atualizado para versão 2.1.0 com novas funcionalidades", "type": "success", "category": "system", "read": false, "actionUrl": "/changelog", "actionLabel": "Ver novidades", "metadata": {"version": "2.1.0", "features": ["notificações", "relatórios", "dashboard"]}, "createdAt": "2025-06-12T00:57:41.499Z", "readAt": null, "expiresAt": null}, {"id": "c0125c50-f843-4248-9eae-d3fcccb49d49", "userId": "a2e35df8-8097-47b1-99a0-383ba7034aa1", "title": "Reunião de equipe em 1 hora", "message": "Reunião semanal da equipe administrativa agendada para 14:00", "type": "warning", "category": "event", "read": false, "actionUrl": "/calendar/meeting-001", "actionLabel": "Ver agenda", "metadata": {"eventTime": "14:00", "participants": ["Admin", "G<PERSON><PERSON>"], "location": "Sala de reuniões"}, "createdAt": "2025-06-12T00:57:41.502Z", "readAt": null, "expiresAt": null}, {"id": "1732d50a-ca15-48bb-87fe-528a3682c2e1", "userId": "a2e35df8-8097-47b1-99a0-383ba7034aa1", "title": "Relatório mensal disponível", "message": "Relatório de demandas do mês de dezembro está pronto para análise", "type": "info", "category": "general", "read": false, "actionUrl": "/reports/monthly/december", "actionLabel": "<PERSON>er relatório", "metadata": {"reportType": "monthly", "period": "dezemb<PERSON>", "totalDemands": 156, "resolved": 142}, "createdAt": "2025-06-12T00:57:41.504Z", "readAt": null, "expiresAt": null}, {"id": "b68e6332-8936-42d9-975f-51e4272b68f3", "userId": "a2e35df8-8097-47b1-99a0-383ba7034aa1", "title": "Backup automático concluído", "message": "Backup diário dos dados foi realizado com sucesso às 02:00", "type": "success", "category": "system", "read": false, "actionUrl": "/admin/backups", "actionLabel": "Ver backups", "metadata": {"backupTime": "02:00", "size": "2.3 GB", "status": "success"}, "createdAt": "2025-06-12T00:57:41.506Z", "readAt": null, "expiresAt": null}]