// Simple test to verify UserRole enum values
import { UserRole } from './types.js';

console.log('UserRole.ADMIN:', UserRole.ADMIN);
console.log('UserRole.STAFF:', UserRole.STAFF);
console.log('UserRole.MASTER:', UserRole.MASTER);

// Test comparisons
const testUser = { role: 'master' };
console.log('testUser.role === UserRole.MASTER:', testUser.role === UserRole.MASTER);
console.log('testUser.role === "master":', testUser.role === 'master');