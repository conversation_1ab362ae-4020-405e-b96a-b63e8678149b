import React, { useState, useEffect } from 'react';
import { DashboardWidget, WidgetConfig, MetricWidget, ListWidget, ChartWidget } from './DashboardWidget';
import { Button } from '../ui/Button';
import { Modal } from '../ui/Modal';
import { Input } from '../ui/Input';
import { safeGetItem, safeSetItem } from '../../utils/storage';

// Dashboard Administrativo - Gerenciamento do Sistema Promandato
interface AdminDashboardProps {
  userRole: 'super_admin' | 'admin' | 'manager';
}

interface SystemMetrics {
  totalUsers: number;
  activeUsers: number;
  totalOrganizations: number;
  monthlyRevenue: number;
  conversionRate: number;
  systemHealth: 'healthy' | 'warning' | 'critical';
}

interface LeadData {
  id: string;
  name: string;
  email: string;
  phone?: string;
  organization?: string;
  source: 'landing_page' | 'referral' | 'direct' | 'campaign';
  status: 'new' | 'contacted' | 'qualified' | 'converted' | 'lost';
  createdAt: string;
  lastContact?: string;
  notes?: string;
}

interface DashboardCustomizerProps {
  initialWidgets?: WidgetConfig[];
  onSave?: (widgets: WidgetConfig[]) => void;
}

const WIDGET_TEMPLATES = [
  {
    id: 'users-metric',
    title: 'Total de Usuários',
    type: 'metric' as const,
    size: 'small' as const,
    data: { value: 1247, change: 12, color: 'blue' }
  },
  {
    id: 'organizations-metric',
    title: 'Organizações Ativas',
    type: 'metric' as const,
    size: 'small' as const,
    data: { value: 156, change: 8, color: 'green' }
  },
  {
    id: 'revenue-metric',
    title: 'Receita Mensal',
    type: 'metric' as const,
    size: 'small' as const,
    data: { value: 89750, change: 15, color: 'yellow' }
  },
  {
    id: 'recent-leads',
    title: 'Leads Recentes',
    type: 'list' as const,
    size: 'medium' as const,
    data: {
      items: [
        { id: '1', title: 'Maria Silva - Prefeitura SP', subtitle: 'Interesse: Profissional', status: 'Novo' },
        { id: '2', title: 'João Santos - Câmara RJ', subtitle: 'Interesse: Padrão', status: 'Contatado' }
      ]
    }
  },
  {
    id: 'plans-chart',
    title: 'Distribuição por Planos',
    type: 'chart' as const,
    size: 'medium' as const,
    data: {
      chartData: [
        { label: 'Básico', value: 45, color: 'bg-gray-500' },
        { label: 'Padrão', value: 67, color: 'bg-blue-500' },
        { label: 'Profissional', value: 44, color: 'bg-purple-500' }
      ]
    }
  }
];

export const DashboardCustomizer: React.FC<DashboardCustomizerProps> = ({
  initialWidgets = [],
  onSave
}) => {
  const [widgets, setWidgets] = useState<WidgetConfig[]>(initialWidgets);
  const [isEditing, setIsEditing] = useState(false);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [editingWidget, setEditingWidget] = useState<WidgetConfig | null>(null);

  // Carregar widgets salvos do localStorage
  useEffect(() => {
    const defaultWidgets = WIDGET_TEMPLATES.map((template, index) => ({
      ...template,
      position: { x: (index % 3) * 300, y: Math.floor(index / 3) * 200 }
    }));

    const savedWidgets = safeGetItem<WidgetConfig[]>('dashboard-widgets', []);

    if (savedWidgets.length > 0) {
      setWidgets(savedWidgets);
    } else if (initialWidgets.length === 0) {
      // Se não há widgets salvos nem iniciais, usar templates padrão
      setWidgets(defaultWidgets);
    }
  }, [initialWidgets]);

  const saveWidgets = (newWidgets: WidgetConfig[]) => {
    setWidgets(newWidgets);
    safeSetItem('dashboard-widgets', newWidgets);
    onSave?.(newWidgets);
  };

  const handleAddWidget = (template: typeof WIDGET_TEMPLATES[0]) => {
    const newWidget: WidgetConfig = {
      ...template,
      id: `${template.id}-${Date.now()}`,
      position: { x: 0, y: 0 }
    };
    
    saveWidgets([...widgets, newWidget]);
    setIsAddModalOpen(false);
  };

  const handleRemoveWidget = (id: string) => {
    saveWidgets(widgets.filter(w => w.id !== id));
  };

  const handleEditWidget = (widget: WidgetConfig) => {
    setEditingWidget(widget);
  };

  const handleSaveEdit = (updatedWidget: WidgetConfig) => {
    saveWidgets(widgets.map(w => w.id === updatedWidget.id ? updatedWidget : w));
    setEditingWidget(null);
  };

  const handleMoveWidget = (id: string, position: { x: number; y: number }) => {
    saveWidgets(widgets.map(w => w.id === id ? { ...w, position } : w));
  };

  const renderWidget = (config: WidgetConfig) => {
    let content;

    switch (config.type) {
      case 'metric':
        content = (
          <MetricWidget
            title={config.title}
            value={config.data?.value || 0}
            change={config.data?.change}
            color={config.data?.color}
            icon={config.data?.icon}
          />
        );
        break;

      case 'list':
        content = (
          <ListWidget
            items={config.data?.items || []}
            emptyMessage="Nenhum item para exibir"
          />
        );
        break;

      case 'chart':
        content = (
          <ChartWidget
            data={config.data?.chartData || []}
            type={config.data?.chartType || 'bar'}
          />
        );
        break;

      default:
        content = (
          <div className="text-center py-8 text-gray-500 dark:text-neutral-medium">
            Widget tipo "{config.type}" não implementado
          </div>
        );
    }

    return (
      <DashboardWidget
        key={config.id}
        config={config}
        isEditing={isEditing}
        onEdit={handleEditWidget}
        onRemove={handleRemoveWidget}
        onMove={handleMoveWidget}
      >
        {content}
      </DashboardWidget>
    );
  };

  return (
    <div className="space-y-6">
      {/* Toolbar */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-neutral-light">
          Dashboard
        </h2>
        
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsAddModalOpen(true)}
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Adicionar Widget
          </Button>
          
          <Button
            variant={isEditing ? "primary" : "outline"}
            size="sm"
            onClick={() => setIsEditing(!isEditing)}
          >
            {isEditing ? 'Finalizar Edição' : 'Editar Dashboard'}
          </Button>
        </div>
      </div>

      {/* Grid de Widgets */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 auto-rows-fr">
        {widgets.map(renderWidget)}
      </div>

      {/* Modal para Adicionar Widget */}
      <Modal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        title="Adicionar Widget"
        size="lg"
      >
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {WIDGET_TEMPLATES.map((template) => (
            <div
              key={template.id}
              className="p-4 border border-gray-200 dark:border-neutral-medium rounded-lg hover:border-primary cursor-pointer transition-colors"
              onClick={() => handleAddWidget(template)}
            >
              <h3 className="font-semibold text-gray-900 dark:text-neutral-light mb-2">
                {template.title}
              </h3>
              <p className="text-sm text-gray-600 dark:text-neutral-DEFAULT mb-3">
                Widget do tipo {template.type} - Tamanho {template.size}
              </p>
              <div className="text-xs text-gray-500 dark:text-neutral-medium">
                Clique para adicionar ao dashboard
              </div>
            </div>
          ))}
        </div>
      </Modal>

      {/* Modal para Editar Widget */}
      {editingWidget && (
        <WidgetEditModal
          widget={editingWidget}
          onSave={handleSaveEdit}
          onClose={() => setEditingWidget(null)}
        />
      )}
    </div>
  );
};

// Modal para editar widget
const WidgetEditModal: React.FC<{
  widget: WidgetConfig;
  onSave: (widget: WidgetConfig) => void;
  onClose: () => void;
}> = ({ widget, onSave, onClose }) => {
  const [title, setTitle] = useState(widget.title);
  const [size, setSize] = useState(widget.size);

  const handleSave = () => {
    onSave({
      ...widget,
      title,
      size
    });
  };

  return (
    <Modal
      isOpen={true}
      onClose={onClose}
      title="Editar Widget"
    >
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-neutral-DEFAULT mb-2">
            Título
          </label>
          <Input
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Digite o título do widget"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-neutral-DEFAULT mb-2">
            Tamanho
          </label>
          <select
            value={size}
            onChange={(e) => setSize(e.target.value as 'small' | 'medium' | 'large')}
            className="w-full px-3 py-2 border border-gray-300 dark:border-neutral-medium rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-neutral-dark dark:text-neutral-light"
            title="Selecionar tamanho do widget"
          >
            <option value="small">Pequeno (1x1)</option>
            <option value="medium">Médio (2x1)</option>
            <option value="large">Grande (2x2)</option>
          </select>
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <Button variant="outline" onClick={onClose}>
            Cancelar
          </Button>
          <Button variant="primary" onClick={handleSave}>
            Salvar
          </Button>
        </div>
      </div>
    </Modal>
  );
};