@echo off
echo ========================================
echo      DEPLOY COMPLETO PROMANDATO
echo ========================================
echo.

echo Este script ira fazer o deploy completo:
echo.
echo 1. Landing Page (Firebase Hosting)
echo 2. Configurar Pagamentos (Stripe)
echo 3. Backend/API (Firebase Functions)
echo 4. Aplicacao Frontend (Firebase Hosting)
echo 5. Database (Firestore)
echo.

set /p confirm="Deseja continuar? (s/n): "
if /i not "%confirm%"=="s" goto end

echo.
echo ========================================
echo        INICIANDO DEPLOY COMPLETO
echo ========================================
echo.

echo [ETAPA 1/5] Deploy Landing Page...
call scripts\1-deploy-landing.bat
if %errorlevel% neq 0 goto error

echo.
echo [ETAPA 2/5] Configurar Pagamentos...
call scripts\2-setup-payments.bat
if %errorlevel% neq 0 goto error

echo.
echo [ETAPA 3/5] Deploy Backend...
call scripts\3-deploy-backend.bat
if %errorlevel% neq 0 goto error

echo.
echo [ETAPA 4/5] Deploy Frontend...
call scripts\4-deploy-frontend.bat
if %errorlevel% neq 0 goto error

echo.
echo [ETAPA 5/5] Configurar Database...
call scripts\5-setup-database.bat
if %errorlevel% neq 0 goto error

echo.
echo ========================================
echo       DEPLOY COMPLETO FINALIZADO!
echo ========================================
echo.
echo URLs finais:
echo - Landing Page: https://promandato-9a4cf.web.app
echo - Aplicacao: https://promandato-9a4cf.web.app (app)
echo - API: https://us-central1-promandato-9a4cf.cloudfunctions.net/api
echo.
echo Proximos passos:
echo 1. Configurar dominios customizados
echo 2. Configurar monitoramento
echo 3. Testar fluxo completo
echo 4. Configurar backup
echo.
goto end

:error
echo.
echo ========================================
echo           ERRO NO DEPLOY!
echo ========================================
echo.
echo O deploy foi interrompido devido a um erro.
echo Verifique os logs acima e tente novamente.
echo.

:end
pause
