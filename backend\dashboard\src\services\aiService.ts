// Interfaces para funcionalidades de IA
interface AIFeature {
  id: string;
  name: string;
  description: string;
  category: 'Análise' | 'Automação' | 'Predição';
  enabled: boolean;
  usageLimit?: number; // Limite de uso por mês
  currentUsage?: number; // Uso atual no mês
}

interface AIUsageMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  lastUsed: Date;
  monthlyUsage: {
    [featureId: string]: number;
  };
}

interface AIConfiguration {
  geminiApiKey?: string;
  maxRetries: number;
  timeout: number;
  cacheEnabled: boolean;
  cacheTTL: number; // Time to live em minutos
}

interface CacheEntry {
  data: any;
  timestamp: number;
  ttl: number;
}

interface TextAnalysisResult {
  sentiment: 'positive' | 'negative' | 'neutral';
  confidence: number;
  keywords: string[];
  emotions: {
    joy: number;
    anger: number;
    fear: number;
    sadness: number;
    surprise: number;
    disgust: number;
    trust: number;
    anticipation: number;
  };
  urgency: 'low' | 'medium' | 'high';
  complexity: 'simple' | 'moderate' | 'complex';
  language: string;
  topics: string[];
  entities: Array<{
    text: string;
    type: 'PERSON' | 'LOCATION' | 'ORGANIZATION' | 'DATE' | 'OTHER';
    confidence: number;
  }>;
}

interface CategorizationResult {
  category: string;
  subcategory?: string;
  confidence: number;
  suggestedTags: string[];
  department?: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  estimatedResolutionTime: number; // em horas
  requiredSkills: string[];
  relatedCategories: Array<{
    category: string;
    similarity: number;
  }>;
}

interface PredictiveAnalysisResult {
  resolutionTime: number; // em horas
  priority: 'low' | 'medium' | 'high' | 'urgent';
  similarCases: Array<{
    id: string;
    similarity: number;
    resolution: string;
    resolutionTime: number;
    outcome: 'resolved' | 'escalated' | 'pending';
  }>;
  trends: {
    increasing: boolean;
    pattern: string;
    seasonality?: {
      detected: boolean;
      period?: string;
      confidence: number;
    };
    forecast: Array<{
      period: string;
      expectedVolume: number;
      confidence: number;
    }>;
  };
  riskFactors: Array<{
    factor: string;
    impact: 'low' | 'medium' | 'high';
    probability: number;
  }>;
  recommendations: Array<{
    action: string;
    priority: number;
    expectedImpact: string;
  }>;
}

interface SmartResponseSuggestion {
  response: string;
  confidence: number;
  tone: 'formal' | 'friendly' | 'empathetic' | 'professional' | 'urgent';
  category: string;
  personalization: {
    citizenName?: string;
    previousInteractions?: number;
    preferredCommunicationStyle?: string;
  };
  templates: Array<{
    id: string;
    name: string;
    content: string;
    variables: string[];
  }>;
  followUpSuggestions?: string[];
  estimatedSatisfaction: number; // 0-1
}

interface AIAnalysisRequest {
  text: string;
  context?: {
    userHistory?: any[];
    category?: string;
    priority?: string;
    citizenId?: string;
    departmentId?: string;
    previousAnalysis?: any;
    previousInteractions?: number;
    preferredCommunicationStyle?: string;
    metadata?: Record<string, any>;
  };
  options?: {
    includeEntities?: boolean;
    includeTopics?: boolean;
    includeSimilarCases?: boolean;
    maxSimilarCases?: number;
    language?: string;
  };
}

// Novas interfaces para funcionalidades avançadas
interface AIInsight {
  type: 'trend' | 'anomaly' | 'recommendation' | 'alert';
  title: string;
  description: string;
  confidence: number;
  impact: 'low' | 'medium' | 'high';
  actionRequired: boolean;
  data: any;
}

interface AIBatchAnalysisRequest {
  items: Array<{
    id: string;
    text: string;
    context?: any;
  }>;
  features: string[]; // quais análises executar
  options?: {
    parallel?: boolean;
    maxConcurrency?: number;
  };
}

interface AIBatchAnalysisResult {
  results: Array<{
    id: string;
    success: boolean;
    data?: any;
    error?: string;
  }>;
  summary: {
    total: number;
    successful: number;
    failed: number;
    processingTime: number;
  };
}

class AIService {
  private baseURL: string;
  private apiKey: string | null = null;
  private cache: Map<string, CacheEntry> = new Map();
  private config: AIConfiguration;
  private metrics: AIUsageMetrics;

  constructor() {
    this.baseURL = import.meta.env.VITE_AI_API_URL || '/api/ai';
    this.apiKey = import.meta.env.VITE_GEMINI_API_KEY || null;
    
    this.config = {
      geminiApiKey: this.apiKey || undefined,
      maxRetries: 3,
      timeout: 30000,
      cacheEnabled: true,
      cacheTTL: 15 // 15 minutos
    };

    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      lastUsed: new Date(),
      monthlyUsage: {}
    };

    // Limpar cache expirado a cada 5 minutos
    setInterval(() => this.cleanExpiredCache(), 5 * 60 * 1000);
  }

  // Configuração e gerenciamento
  updateConfiguration(newConfig: Partial<AIConfiguration>): void {
    this.config = { ...this.config, ...newConfig };
  }

  getConfiguration(): AIConfiguration {
    return { ...this.config };
  }

  getMetrics(): AIUsageMetrics {
    return { ...this.metrics };
  }

  // Sistema de cache
  private getCacheKey(endpoint: string, data: any): string {
    return `${endpoint}_${JSON.stringify(data)}`;
  }

  private getFromCache(key: string): any | null {
    if (!this.config.cacheEnabled) return null;
    
    const entry = this.cache.get(key);
    if (!entry) return null;
    
    const now = Date.now();
    if (now - entry.timestamp > entry.ttl * 60 * 1000) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.data;
  }

  private setCache(key: string, data: any): void {
    if (!this.config.cacheEnabled) return;
    
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: this.config.cacheTTL
    });
  }

  private cleanExpiredCache(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl * 60 * 1000) {
        this.cache.delete(key);
      }
    }
  }

  // Controle de acesso baseado em planos
  private checkFeatureAccess(featureId: string): { allowed: boolean; reason?: string } {
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const aiFeatures = user.plan?.features?.aiFeatures || [];
      
      const feature = aiFeatures.find((f: AIFeature) => f.id === featureId);
      
      if (!feature) {
        return { allowed: false, reason: 'Funcionalidade não disponível no seu plano' };
      }
      
      if (!feature.enabled) {
        return { allowed: false, reason: 'Funcionalidade desabilitada' };
      }
      
      // Verificar limite de uso
      if (feature.usageLimit && feature.currentUsage && feature.currentUsage >= feature.usageLimit) {
        return { allowed: false, reason: 'Limite de uso mensal atingido' };
      }
      
      return { allowed: true };
    } catch {
      return { allowed: false, reason: 'Erro ao verificar permissões' };
    }
  }

  private updateUsageMetrics(featureId: string, success: boolean, responseTime: number): void {
    this.metrics.totalRequests++;
    this.metrics.lastUsed = new Date();
    
    if (success) {
      this.metrics.successfulRequests++;
    } else {
      this.metrics.failedRequests++;
    }
    
    // Atualizar tempo médio de resposta
    const totalTime = this.metrics.averageResponseTime * (this.metrics.totalRequests - 1) + responseTime;
    this.metrics.averageResponseTime = totalTime / this.metrics.totalRequests;
    
    // Atualizar uso mensal da funcionalidade
    this.metrics.monthlyUsage[featureId] = (this.metrics.monthlyUsage[featureId] || 0) + 1;
    
    // Atualizar uso no localStorage para persistência
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      if (user.plan?.features?.aiFeatures) {
        const feature = user.plan.features.aiFeatures.find((f: AIFeature) => f.id === featureId);
        if (feature) {
          feature.currentUsage = (feature.currentUsage || 0) + 1;
          localStorage.setItem('user', JSON.stringify(user));
        }
      }
    } catch (error) {
      console.warn('Erro ao atualizar uso da funcionalidade:', error);
    }
  }

  private async makeRequest(endpoint: string, data: any, featureId?: string): Promise<any> {
    const startTime = Date.now();
    
    // Verificar acesso à funcionalidade
    if (featureId) {
      const accessCheck = this.checkFeatureAccess(featureId);
      if (!accessCheck.allowed) {
        throw new Error(accessCheck.reason);
      }
    }
    
    // Verificar cache
    const cacheKey = this.getCacheKey(endpoint, data);
    const cachedResult = this.getFromCache(cacheKey);
    if (cachedResult) {
      return cachedResult;
    }
    
    const token = localStorage.getItem('accessToken');
    let attempt = 0;
    
    while (attempt < this.config.maxRetries) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);
        
        const response = await fetch(`${this.baseURL}${endpoint}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
            ...(this.apiKey && { 'X-AI-API-Key': this.apiKey })
          },
          body: JSON.stringify(data),
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`Erro na API: ${response.status}`);
        }

        const result = await response.json();
        
        // Salvar no cache
        this.setCache(cacheKey, result);
        
        // Atualizar métricas
        const responseTime = Date.now() - startTime;
        if (featureId) {
          this.updateUsageMetrics(featureId, true, responseTime);
        }
        
        return result;
      } catch (error) {
        attempt++;
        
        if (attempt >= this.config.maxRetries) {
          const responseTime = Date.now() - startTime;
          if (featureId) {
            this.updateUsageMetrics(featureId, false, responseTime);
          }
          console.error('Erro na requisição AI após tentativas:', error);
          throw error;
        }
        
        // Aguardar antes de tentar novamente (backoff exponencial)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }
  }

  // Integração direta com Gemini API
  private async callGeminiAPI(prompt: string, options: any = {}): Promise<any> {
    if (!this.apiKey) {
      throw new Error('Chave da API Gemini não configurada');
    }

    try {
      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }],
          generationConfig: {
            temperature: options.temperature || 0.7,
            topK: options.topK || 40,
            topP: options.topP || 0.95,
            maxOutputTokens: options.maxTokens || 1024,
          }
        })
      });

      if (!response.ok) {
        throw new Error(`Erro na API Gemini: ${response.status}`);
      }

      const result = await response.json();
      return result.candidates[0]?.content?.parts[0]?.text || '';
    } catch (error) {
      console.error('Erro ao chamar Gemini API:', error);
      throw error;
    }
  }

  // Análise de Texto com IA
  async analyzeText(request: AIAnalysisRequest): Promise<TextAnalysisResult> {
    try {
      const response = await this.makeRequest('/analyze-text', request, 'text-analysis');
      return response.data;
    } catch (error) {
      // Fallback para análise com Gemini
      if (this.apiKey) {
        try {
          return await this.analyzeTextWithGemini(request);
        } catch (geminiError) {
          console.warn('Erro na análise com Gemini:', geminiError);
        }
      }
      
      // Fallback para análise local básica
      return this.fallbackTextAnalysis(request.text);
    }
  }

  private async analyzeTextWithGemini(request: AIAnalysisRequest): Promise<TextAnalysisResult> {
    const prompt = `
Analise o seguinte texto e retorne um JSON com a análise completa:

Texto: "${request.text}"

Retorne um JSON com a seguinte estrutura:
{
  "sentiment": "positive|negative|neutral",
  "confidence": 0.0-1.0,
  "keywords": ["palavra1", "palavra2"],
  "emotions": {
    "joy": 0.0-1.0,
    "anger": 0.0-1.0,
    "fear": 0.0-1.0,
    "sadness": 0.0-1.0,
    "surprise": 0.0-1.0,
    "disgust": 0.0-1.0,
    "trust": 0.0-1.0,
    "anticipation": 0.0-1.0
  },
  "urgency": "low|medium|high",
  "complexity": "simple|moderate|complex",
  "language": "pt-BR",
  "topics": ["tópico1", "tópico2"],
  "entities": [
    {
      "text": "entidade",
      "type": "PERSON|LOCATION|ORGANIZATION|DATE|OTHER",
      "confidence": 0.0-1.0
    }
  ]
}

Analise considerando o contexto brasileiro e demandas públicas.
`;

    const result = await this.callGeminiAPI(prompt);
    
    try {
      return JSON.parse(result);
    } catch (parseError) {
      console.warn('Erro ao parsear resposta do Gemini:', parseError);
      return this.fallbackTextAnalysis(request.text);
    }
  }

  // Categorização Automática
  async categorizeDemand(request: AIAnalysisRequest): Promise<CategorizationResult> {
    try {
      const response = await this.makeRequest('/categorize', request, 'auto-categorization');
      return response.data;
    } catch (error) {
      if (this.apiKey) {
        try {
          return await this.categorizeDemandWithGemini(request);
        } catch (geminiError) {
          console.warn('Erro na categorização com Gemini:', geminiError);
        }
      }
      
      return this.fallbackCategorization(request.text);
    }
  }

  private async categorizeDemandWithGemini(request: AIAnalysisRequest): Promise<CategorizationResult> {
    const prompt = `
Categorize a seguinte demanda pública e retorne um JSON:

Texto: "${request.text}"

Categorias disponíveis:
- Infraestrutura (subcategorias: Pavimentação, Iluminação, Sinalização, Drenagem)
- Saúde (subcategorias: Atendimento, Medicamentos, Equipamentos, Estrutura)
- Educação (subcategorias: Ensino, Infraestrutura, Transporte, Merenda)
- Segurança (subcategorias: Policiamento, Iluminação, Equipamentos)
- Meio Ambiente (subcategorias: Limpeza, Arborização, Poluição, Reciclagem)
- Transporte (subcategorias: Ônibus, Ciclovia, Trânsito, Acessibilidade)
- Assistência Social (subcategorias: Programas, Atendimento, Documentação)

Retorne JSON:
{
  "category": "categoria principal",
  "subcategory": "subcategoria específica",
  "confidence": 0.0-1.0,
  "suggestedTags": ["tag1", "tag2"],
  "department": "departamento responsável",
  "priority": "low|medium|high|urgent",
  "estimatedResolutionTime": horas_estimadas,
  "requiredSkills": ["habilidade1", "habilidade2"],
  "relatedCategories": [
    {
      "category": "categoria relacionada",
      "similarity": 0.0-1.0
    }
  ]
}
`;

    const result = await this.callGeminiAPI(prompt);
    
    try {
      return JSON.parse(result);
    } catch (parseError) {
      console.warn('Erro ao parsear categorização do Gemini:', parseError);
      return this.fallbackCategorization(request.text);
    }
  }

  // Análise Preditiva
  async predictiveAnalysis(request: AIAnalysisRequest): Promise<PredictiveAnalysisResult> {
    try {
      const response = await this.makeRequest('/predict', request, 'predictive-analytics');
      return response.data;
    } catch (error) {
      if (this.apiKey) {
        try {
          return await this.predictiveAnalysisWithGemini(request);
        } catch (geminiError) {
          console.warn('Erro na análise preditiva com Gemini:', geminiError);
        }
      }
      
      return this.fallbackPredictiveAnalysis(request.text);
    }
  }

  private async predictiveAnalysisWithGemini(request: AIAnalysisRequest): Promise<PredictiveAnalysisResult> {
    const prompt = `
Faça uma análise preditiva da seguinte demanda pública:

Texto: "${request.text}"
Contexto: ${JSON.stringify(request.context || {})}

Baseado em padrões típicos de demandas públicas brasileiras, retorne um JSON:
{
  "resolutionTime": horas_estimadas,
  "priority": "low|medium|high|urgent",
  "similarCases": [
    {
      "id": "caso_similar_id",
      "similarity": 0.0-1.0,
      "resolution": "descrição da resolução",
      "resolutionTime": horas,
      "outcome": "resolved|escalated|pending"
    }
  ],
  "trends": {
    "increasing": true/false,
    "pattern": "descrição do padrão",
    "seasonality": {
      "detected": true/false,
      "period": "período se detectado",
      "confidence": 0.0-1.0
    },
    "forecast": [
      {
        "period": "próximo período",
        "expectedVolume": número_estimado,
        "confidence": 0.0-1.0
      }
    ]
  },
  "riskFactors": [
    {
      "factor": "fator de risco",
      "impact": "low|medium|high",
      "probability": 0.0-1.0
    }
  ],
  "recommendations": [
    {
      "action": "ação recomendada",
      "priority": 1-10,
      "expectedImpact": "impacto esperado"
    }
  ]
}
`;

    const result = await this.callGeminiAPI(prompt);
    
    try {
      return JSON.parse(result);
    } catch (parseError) {
      console.warn('Erro ao parsear análise preditiva do Gemini:', parseError);
      return this.fallbackPredictiveAnalysis(request.text);
    }
  }

  // Sugestões de Respostas Inteligentes
  async generateSmartResponse(request: AIAnalysisRequest): Promise<SmartResponseSuggestion[]> {
    try {
      const response = await this.makeRequest('/smart-response', request, 'smart-responses');
      return response.data;
    } catch (error) {
      if (this.apiKey) {
        try {
          return await this.generateSmartResponseWithGemini(request);
        } catch (geminiError) {
          console.warn('Erro na geração de respostas com Gemini:', geminiError);
        }
      }
      
      return this.fallbackSmartResponse(request.text);
    }
  }

  private async generateSmartResponseWithGemini(request: AIAnalysisRequest): Promise<SmartResponseSuggestion[]> {
    const prompt = `
Gere respostas inteligentes para a seguinte demanda pública:

Texto: "${request.text}"
Contexto: ${JSON.stringify(request.context || {})}

Gere 3 opções de resposta com tons diferentes. Retorne um JSON array:
[
  {
    "response": "texto da resposta",
    "confidence": 0.0-1.0,
    "tone": "formal|friendly|empathetic|professional|urgent",
    "category": "categoria da resposta",
    "personalization": {
      "citizenName": "nome se disponível",
      "previousInteractions": número,
      "preferredCommunicationStyle": "estilo preferido"
    },
    "templates": [
      {
        "id": "template_id",
        "name": "nome do template",
        "content": "conteúdo do template",
        "variables": ["variável1", "variável2"]
      }
    ],
    "followUpSuggestions": ["sugestão1", "sugestão2"],
    "estimatedSatisfaction": 0.0-1.0
  }
]

As respostas devem ser adequadas para comunicação oficial de órgãos públicos brasileiros.
`;

    const result = await this.callGeminiAPI(prompt);
    
    try {
      return JSON.parse(result);
    } catch (parseError) {
      console.warn('Erro ao parsear respostas do Gemini:', parseError);
      return this.fallbackSmartResponse(request.text);
    }
  }

  // Análise completa (combina todas as funcionalidades)
  async completeAnalysis(request: AIAnalysisRequest): Promise<{
    textAnalysis: TextAnalysisResult;
    categorization: CategorizationResult;
    prediction: PredictiveAnalysisResult;
    smartResponses: SmartResponseSuggestion[];
  }> {
    try {
      const [textAnalysis, categorization, prediction, smartResponses] = await Promise.all([
        this.analyzeText(request),
        this.categorizeDemand(request),
        this.predictiveAnalysis(request),
        this.generateSmartResponse(request)
      ]);

      return {
        textAnalysis,
        categorization,
        prediction,
        smartResponses
      };
    } catch (error) {
      console.error('Erro na análise completa:', error);
      throw error;
    }
  }

  // Nova funcionalidade: Análise em lote
  async batchAnalysis(request: AIBatchAnalysisRequest): Promise<AIBatchAnalysisResult> {
    const startTime = Date.now();
    const results: AIBatchAnalysisResult['results'] = [];
    
    try {
      if (request.options?.parallel) {
        // Processamento paralelo com controle de concorrência
        const maxConcurrency = request.options.maxConcurrency || 5;
        const chunks = this.chunkArray(request.items, maxConcurrency);
        
        for (const chunk of chunks) {
          const chunkPromises = chunk.map(async (item) => {
            try {
              const analysisRequest: AIAnalysisRequest = {
                text: item.text,
                context: item.context
              };
              
              const data = await this.completeAnalysis(analysisRequest);
              return { id: item.id, success: true, data };
            } catch (error) {
              return { 
                id: item.id, 
                success: false, 
                error: error instanceof Error ? error.message : 'Erro desconhecido' 
              };
            }
          });
          
          const chunkResults = await Promise.all(chunkPromises);
          results.push(...chunkResults);
        }
      } else {
        // Processamento sequencial
        for (const item of request.items) {
          try {
            const analysisRequest: AIAnalysisRequest = {
              text: item.text,
              context: item.context
            };
            
            const data = await this.completeAnalysis(analysisRequest);
            results.push({ id: item.id, success: true, data });
          } catch (error) {
            results.push({ 
              id: item.id, 
              success: false, 
              error: error instanceof Error ? error.message : 'Erro desconhecido' 
            });
          }
        }
      }
      
      const processingTime = Date.now() - startTime;
      const successful = results.filter(r => r.success).length;
      const failed = results.filter(r => !r.success).length;
      
      return {
        results,
        summary: {
          total: request.items.length,
          successful,
          failed,
          processingTime
        }
      };
    } catch (error) {
      console.error('Erro na análise em lote:', error);
      throw error;
    }
  }

  // Nova funcionalidade: Insights automáticos
  async generateInsights(data: {
    demands: any[];
    timeRange: { start: Date; end: Date };
    departments?: string[];
  }): Promise<AIInsight[]> {
    try {
      const response = await this.makeRequest('/insights', data, 'predictive-analytics');
      return response.data;
    } catch (error) {
      return this.fallbackGenerateInsights(data);
    }
  }

  // Nova funcionalidade: Detecção de anomalias
  async detectAnomalies(data: {
    metrics: Array<{ timestamp: Date; value: number; category?: string }>;
    sensitivity?: 'low' | 'medium' | 'high';
  }): Promise<Array<{
    timestamp: Date;
    value: number;
    anomalyScore: number;
    type: 'spike' | 'drop' | 'trend_change';
    description: string;
  }>> {
    try {
      const response = await this.makeRequest('/anomalies', data, 'predictive-analytics');
      return response.data;
    } catch (error) {
      return this.fallbackDetectAnomalies(data);
    }
  }

  // Nova funcionalidade: Recomendações personalizadas
  async getPersonalizedRecommendations(userId: string): Promise<Array<{
    type: 'workflow' | 'response_template' | 'priority_adjustment' | 'training';
    title: string;
    description: string;
    impact: 'low' | 'medium' | 'high';
    effort: 'low' | 'medium' | 'high';
    data: any;
  }>> {
    try {
      const response = await this.makeRequest('/recommendations', { userId }, 'predictive-analytics');
      return response.data;
    } catch (error) {
      return this.fallbackPersonalizedRecommendations(userId);
    }
  }

  // Utilitário para dividir array em chunks
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  // Fallbacks para quando a API não estiver disponível
  private fallbackTextAnalysis(text: string): TextAnalysisResult {
    const words = text.toLowerCase().split(' ');
    
    // Análise básica de sentimento
    const positiveWords = ['bom', 'ótimo', 'excelente', 'satisfeito', 'obrigado', 'parabéns', 'agradecimento', 'feliz', 'contente'];
    const negativeWords = ['ruim', 'péssimo', 'problema', 'reclamação', 'insatisfeito', 'urgente', 'crítico', 'grave', 'inaceitável'];
    
    const positiveCount = words.filter(word => positiveWords.includes(word)).length;
    const negativeCount = words.filter(word => negativeWords.includes(word)).length;
    
    let sentiment: 'positive' | 'negative' | 'neutral' = 'neutral';
    if (positiveCount > negativeCount) sentiment = 'positive';
    else if (negativeCount > positiveCount) sentiment = 'negative';
    
    // Análise de urgência
    const urgentWords = ['urgente', 'emergência', 'imediato', 'crítico', 'grave', 'risco'];
    const hasUrgentWords = words.some(word => urgentWords.includes(word));
    
    // Análise de complexidade
    const complexWords = ['complexo', 'múltiplo', 'diversos', 'vários', 'complicado'];
    const hasComplexWords = words.some(word => complexWords.includes(word));
    
    // Extração de entidades básica
    const entities = this.extractBasicEntities(text);
    
    return {
      sentiment,
      confidence: 0.6,
      keywords: words.filter(word => word.length > 4).slice(0, 5),
      emotions: {
        joy: sentiment === 'positive' ? 0.7 : 0.2,
        anger: sentiment === 'negative' ? 0.6 : 0.1,
        fear: hasUrgentWords ? 0.5 : 0.1,
        sadness: sentiment === 'negative' ? 0.4 : 0.1,
        surprise: 0.2,
        disgust: sentiment === 'negative' ? 0.3 : 0.1,
        trust: sentiment === 'positive' ? 0.6 : 0.3,
        anticipation: hasUrgentWords ? 0.7 : 0.3
      },
      urgency: hasUrgentWords ? 'high' : sentiment === 'negative' ? 'medium' : 'low',
      complexity: hasComplexWords || text.length > 500 ? 'complex' : text.length > 200 ? 'moderate' : 'simple',
      language: 'pt-BR',
      topics: this.extractTopics(text),
      entities
    };
  }

  private extractBasicEntities(text: string): Array<{
    text: string;
    type: 'PERSON' | 'LOCATION' | 'ORGANIZATION' | 'DATE' | 'OTHER';
    confidence: number;
  }> {
    const entities: Array<{ text: string; type: 'PERSON' | 'LOCATION' | 'ORGANIZATION' | 'DATE' | 'OTHER'; confidence: number }> = [];
    
    // Padrões básicos para extração de entidades
    const datePattern = /\d{1,2}\/\d{1,2}\/\d{4}|\d{1,2} de \w+ de \d{4}/g;
    const dates = text.match(datePattern) || [];
    dates.forEach(date => {
      entities.push({ text: date, type: 'DATE' as const, confidence: 0.8 });
    });
    
    // Locais comuns
    const locationWords = ['rua', 'avenida', 'praça', 'bairro', 'cidade', 'estado'];
    const words = text.toLowerCase().split(' ');
    words.forEach((word, index) => {
      if (locationWords.includes(word) && index < words.length - 1) {
        entities.push({ 
          text: `${word} ${words[index + 1]}`, 
          type: 'LOCATION' as const, 
          confidence: 0.6 
        });
      }
    });
    
    return entities;
  }

  private extractTopics(text: string): string[] {
    const topicKeywords = {
      'infraestrutura': ['rua', 'asfalto', 'buraco', 'iluminação', 'semáforo', 'calçada'],
      'saúde': ['hospital', 'posto', 'médico', 'remédio', 'consulta', 'atendimento'],
      'educação': ['escola', 'professor', 'ensino', 'creche', 'educação', 'aluno'],
      'segurança': ['polícia', 'roubo', 'violência', 'segurança', 'crime', 'patrulhamento'],
      'meio_ambiente': ['lixo', 'poluição', 'árvore', 'parque', 'ambiente', 'reciclagem'],
      'transporte': ['ônibus', 'transporte', 'trânsito', 'ciclovia', 'mobilidade']
    };
    
    const words = text.toLowerCase().split(' ');
    const topics = [];
    
    for (const [topic, keywords] of Object.entries(topicKeywords)) {
      const matches = words.filter(word => keywords.includes(word)).length;
      if (matches > 0) {
        topics.push(topic);
      }
    }
    
    return topics;
  }

  private fallbackCategorization(text: string): CategorizationResult {
    const categories = {
      'Infraestrutura': {
        keywords: ['rua', 'asfalto', 'buraco', 'iluminação', 'semáforo', 'calçada', 'pavimentação'],
        department: 'Secretaria de Obras',
        estimatedTime: 72,
        subcategories: ['Pavimentação', 'Iluminação', 'Sinalização', 'Drenagem']
      },
      'Saúde': {
        keywords: ['hospital', 'posto', 'médico', 'remédio', 'consulta', 'atendimento', 'saúde'],
        department: 'Secretaria de Saúde',
        estimatedTime: 48,
        subcategories: ['Atendimento', 'Medicamentos', 'Equipamentos', 'Estrutura']
      },
      'Educação': {
        keywords: ['escola', 'professor', 'ensino', 'creche', 'educação', 'aluno', 'merenda'],
        department: 'Secretaria de Educação',
        estimatedTime: 96,
        subcategories: ['Ensino', 'Infraestrutura', 'Transporte', 'Merenda']
      },
      'Segurança': {
        keywords: ['polícia', 'roubo', 'violência', 'segurança', 'crime', 'patrulhamento'],
        department: 'Secretaria de Segurança',
        estimatedTime: 24,
        subcategories: ['Policiamento', 'Iluminação', 'Equipamentos']
      },
      'Meio Ambiente': {
        keywords: ['lixo', 'poluição', 'árvore', 'parque', 'ambiente', 'reciclagem', 'limpeza'],
        department: 'Secretaria de Meio Ambiente',
        estimatedTime: 48,
        subcategories: ['Limpeza', 'Arborização', 'Poluição', 'Reciclagem']
      }
    };

    const words = text.toLowerCase().split(' ');
    let bestCategory = 'Geral';
    let maxMatches = 0;
    let categoryData = null;

    for (const [category, data] of Object.entries(categories)) {
      const matches = words.filter(word => data.keywords.includes(word)).length;
      if (matches > maxMatches) {
        maxMatches = matches;
        bestCategory = category;
        categoryData = data;
      }
    }

    const urgentWords = ['urgente', 'emergência', 'imediato', 'crítico'];
    const hasUrgentWords = words.some(word => urgentWords.includes(word));

    return {
      category: bestCategory,
      subcategory: categoryData?.subcategories[0],
      confidence: maxMatches > 0 ? 0.7 : 0.3,
      suggestedTags: words.filter(word => word.length > 4).slice(0, 3),
      department: categoryData?.department || 'Secretaria Geral',
      priority: hasUrgentWords ? 'urgent' : maxMatches > 2 ? 'high' : 'medium',
      estimatedResolutionTime: categoryData?.estimatedTime || 72,
      requiredSkills: this.getRequiredSkills(bestCategory),
      relatedCategories: this.getRelatedCategories(bestCategory)
    };
  }

  private getRequiredSkills(category: string): string[] {
    const skillsMap: Record<string, string[]> = {
      'Infraestrutura': ['Engenharia Civil', 'Operação de Máquinas', 'Soldagem'],
      'Saúde': ['Medicina', 'Enfermagem', 'Administração Hospitalar'],
      'Educação': ['Pedagogia', 'Administração Escolar', 'Psicologia'],
      'Segurança': ['Segurança Pública', 'Investigação', 'Patrulhamento'],
      'Meio Ambiente': ['Biologia', 'Engenharia Ambiental', 'Gestão de Resíduos']
    };
    
    return skillsMap[category] || ['Administração Geral'];
  }

  private getRelatedCategories(category: string): Array<{ category: string; similarity: number }> {
    const relationsMap: Record<string, Array<{ category: string; similarity: number }>> = {
      'Infraestrutura': [
        { category: 'Transporte', similarity: 0.7 },
        { category: 'Segurança', similarity: 0.5 }
      ],
      'Saúde': [
        { category: 'Assistência Social', similarity: 0.6 },
        { category: 'Educação', similarity: 0.4 }
      ],
      'Educação': [
        { category: 'Assistência Social', similarity: 0.7 },
        { category: 'Transporte', similarity: 0.5 }
      ],
      'Segurança': [
        { category: 'Infraestrutura', similarity: 0.6 },
        { category: 'Meio Ambiente', similarity: 0.4 }
      ],
      'Meio Ambiente': [
        { category: 'Saúde', similarity: 0.5 },
        { category: 'Infraestrutura', similarity: 0.4 }
      ]
    };
    
    return relationsMap[category] || [];
  }

  private fallbackPredictiveAnalysis(text: string): PredictiveAnalysisResult {
    const urgentWords = ['urgente', 'emergência', 'imediato', 'crítico'];
    const hasUrgentWords = text.toLowerCase().split(' ').some(word => urgentWords.includes(word));
    
    const complexity = text.length > 500 ? 'complex' : text.length > 200 ? 'moderate' : 'simple';
    const baseTime = hasUrgentWords ? 24 : 72;
    const complexityMultiplier = complexity === 'complex' ? 1.5 : complexity === 'moderate' ? 1.2 : 1;
    
    return {
      resolutionTime: Math.round(baseTime * complexityMultiplier),
      priority: hasUrgentWords ? 'urgent' : 'medium',
      similarCases: this.generateMockSimilarCases(),
      trends: {
        increasing: Math.random() > 0.5,
        pattern: 'stable',
        seasonality: {
          detected: false,
          confidence: 0.3
        },
        forecast: [
          {
            period: 'próxima semana',
            expectedVolume: Math.floor(Math.random() * 20) + 5,
            confidence: 0.6
          }
        ]
      },
      riskFactors: [
        {
          factor: 'Complexidade da demanda',
          impact: complexity === 'complex' ? 'high' : 'medium',
          probability: 0.7
        }
      ],
      recommendations: [
        {
          action: 'Priorizar atendimento se urgente',
          priority: hasUrgentWords ? 10 : 5,
          expectedImpact: 'Melhoria na satisfação do cidadão'
        }
      ]
    };
  }

  private generateMockSimilarCases(): Array<{
    id: string;
    similarity: number;
    resolution: string;
    resolutionTime: number;
    outcome: 'resolved' | 'escalated' | 'pending';
  }> {
    return [
      {
        id: 'case_001',
        similarity: 0.8,
        resolution: 'Encaminhado para departamento responsável',
        resolutionTime: 48,
        outcome: 'resolved'
      },
      {
        id: 'case_002',
        similarity: 0.6,
        resolution: 'Solicitação de informações adicionais',
        resolutionTime: 72,
        outcome: 'pending'
      }
    ];
  }

  private fallbackSmartResponse(text: string): SmartResponseSuggestion[] {
    const sentiment = this.fallbackTextAnalysis(text).sentiment;
    
    const responses: SmartResponseSuggestion[] = [
      {
        response: 'Agradecemos seu contato. Sua demanda foi registrada e será analisada pela equipe responsável.',
        confidence: 0.8,
        tone: 'formal' as const,
        category: 'acknowledgment',
        personalization: {},
        templates: [],
        estimatedSatisfaction: 0.7
      },
      {
        response: 'Olá! Recebemos sua solicitação e vamos trabalhar para resolver da melhor forma possível.',
        confidence: 0.7,
        tone: 'friendly' as const,
        category: 'acknowledgment',
        personalization: {},
        templates: [],
        estimatedSatisfaction: 0.8
      }
    ];

    if (sentiment === 'negative') {
      responses.push({
        response: 'Entendemos sua preocupação e lamentamos qualquer inconveniente. Vamos priorizar sua demanda.',
        confidence: 0.9,
        tone: 'empathetic' as const,
        category: 'concern',
        personalization: {},
        templates: [],
        estimatedSatisfaction: 0.9
      });
    }

    return responses;
  }

  private fallbackGenerateInsights(_data: any): AIInsight[] {
    return [
      {
        type: 'trend',
        title: 'Aumento nas demandas de infraestrutura',
        description: 'Observado crescimento de 15% nas demandas relacionadas à infraestrutura urbana',
        confidence: 0.7,
        impact: 'medium',
        actionRequired: false,
        data: { category: 'infraestrutura', growth: 0.15 }
      }
    ];
  }

  private fallbackDetectAnomalies(_data: any): Array<{
    timestamp: Date;
    value: number;
    anomalyScore: number;
    type: 'spike' | 'drop' | 'trend_change';
    description: string;
  }> {
    return [];
  }

  private fallbackPersonalizedRecommendations(_userId: string): Array<{
    type: 'workflow' | 'response_template' | 'priority_adjustment' | 'training';
    title: string;
    description: string;
    impact: 'low' | 'medium' | 'high';
    effort: 'low' | 'medium' | 'high';
    data: any;
  }> {
    return [
      {
        type: 'workflow',
        title: 'Otimizar fluxo de categorização',
        description: 'Implementar categorização automática para reduzir tempo de processamento',
        impact: 'high',
        effort: 'medium',
        data: {}
      }
    ];
  }

  // Verificar se uma funcionalidade de IA está disponível
  isFeatureAvailable(featureId: string): boolean {
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const aiFeatures = user.plan?.features?.aiFeatures || [];
      return aiFeatures.some((feature: AIFeature) => feature.id === featureId && feature.enabled);
    } catch {
      return false;
    }
  }

  // Obter todas as funcionalidades de IA disponíveis para o usuário
  getAvailableFeatures(): AIFeature[] {
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      return user.plan?.features?.aiFeatures || [];
    } catch {
      return [];
    }
  }

  // Verificar limites de uso
  checkUsageLimits(): { [featureId: string]: { current: number; limit: number; percentage: number } } {
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      const aiFeatures = user.plan?.features?.aiFeatures || [];
      const limits: { [featureId: string]: { current: number; limit: number; percentage: number } } = {};
      
      aiFeatures.forEach((feature: AIFeature) => {
        if (feature.usageLimit) {
          const current = feature.currentUsage || 0;
          const limit = feature.usageLimit;
          const percentage = (current / limit) * 100;
          
          limits[feature.id] = { current, limit, percentage };
        }
      });
      
      return limits;
    } catch {
      return {};
    }
  }

  // Resetar uso mensal (deve ser chamado no início de cada mês)
  resetMonthlyUsage(): void {
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      if (user.plan?.features?.aiFeatures) {
        user.plan.features.aiFeatures.forEach((feature: AIFeature) => {
          feature.currentUsage = 0;
        });
        localStorage.setItem('user', JSON.stringify(user));
      }
      
      this.metrics.monthlyUsage = {};
    } catch (error) {
      console.warn('Erro ao resetar uso mensal:', error);
    }
  }

  // Obter estatísticas de uso
  getUsageStatistics(): {
    totalRequests: number;
    successRate: number;
    averageResponseTime: number;
    mostUsedFeature: string;
    monthlyUsage: { [featureId: string]: number };
  } {
    const successRate = this.metrics.totalRequests > 0 
      ? (this.metrics.successfulRequests / this.metrics.totalRequests) * 100 
      : 0;
    
    const mostUsedFeature = Object.entries(this.metrics.monthlyUsage)
      .sort(([,a], [,b]) => b - a)[0]?.[0] || 'none';
    
    return {
      totalRequests: this.metrics.totalRequests,
      successRate: Math.round(successRate * 100) / 100,
      averageResponseTime: Math.round(this.metrics.averageResponseTime),
      mostUsedFeature,
      monthlyUsage: { ...this.metrics.monthlyUsage }
    };
  }

  // Limpar cache manualmente
  clearCache(): void {
    this.cache.clear();
  }

  // Verificar status da API
  async checkAPIStatus(): Promise<{
    external: boolean;
    gemini: boolean;
    features: { [key: string]: boolean };
  }> {
    const status = {
      external: false,
      gemini: !!this.apiKey,
      features: {} as { [key: string]: boolean }
    };

    // Testar API externa
    try {
      await fetch(`${this.baseURL}/health`, { method: 'GET' });
      status.external = true;
    } catch {
      status.external = false;
    }

    // Verificar funcionalidades disponíveis
    const features = this.getAvailableFeatures();
    features.forEach(feature => {
      status.features[feature.id] = feature.enabled;
    });

    return status;
  }
}

// Instância singleton
const aiService = new AIService();

export default aiService;
export type { 
  AIFeature, 
  TextAnalysisResult, 
  CategorizationResult, 
  PredictiveAnalysisResult, 
  SmartResponseSuggestion,
  AIAnalysisRequest,
  AIUsageMetrics,
  AIConfiguration,
  AIInsight,
  AIBatchAnalysisRequest,
  AIBatchAnalysisResult
};