import React, { useState, useEffect } from 'react';
import { 
  Building2, 
  Users, 
  DollarSign, 
  Calendar,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Mail,
  Phone,
  Globe,
  Search,
  Filter,
  Plus,
  Eye,
  Edit,
  Trash2,
  Download,
  MoreVertical,
  TrendingUp,
  TrendingDown,
  Minus
} from 'lucide-react';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';

interface Organization {
  id: string;
  name: string;
  plan: 'basic' | 'standard' | 'professional';
  status: 'active' | 'trial' | 'suspended' | 'cancelled';
  users: number;
  maxUsers: number;
  monthlyRevenue: number;
  createdAt: string;
  lastActivity: string;
  contactEmail: string;
  contactPhone?: string;
  website?: string;
  address?: string;
  city: string;
  state: string;
  country: string;
  billingCycle: 'monthly' | 'yearly';
  nextBillingDate: string;
  totalRevenue: number;
  usageMetrics: {
    activeUsers: number;
    storageUsed: number; // GB
    apiCalls: number;
    monthlyLogins: number;
  };
  features: string[];
  notes?: string;
}

interface OrganizationsManagementProps {
  onOrganizationUpdate?: (organizations: Organization[]) => void;
}

const OrganizationsManagement: React.FC<OrganizationsManagementProps> = ({ onOrganizationUpdate }) => {
  const [organizations, setOrganizations] = useState<Organization[]>([
    {
      id: '1',
      name: 'Prefeitura de Belo Horizonte',
      plan: 'professional',
      status: 'active',
      users: 25,
      maxUsers: -1, // ilimitado
      monthlyRevenue: 449,
      createdAt: '2023-12-01T00:00:00Z',
      lastActivity: '2024-01-15T14:30:00Z',
      contactEmail: '<EMAIL>',
      contactPhone: '(31) 3277-9000',
      website: 'https://prefeitura.pbh.gov.br',
      address: 'Av. Afonso Pena, 1212',
      city: 'Belo Horizonte',
      state: 'MG',
      country: 'Brasil',
      billingCycle: 'yearly',
      nextBillingDate: '2024-12-01T00:00:00Z',
      totalRevenue: 4490,
      usageMetrics: {
        activeUsers: 23,
        storageUsed: 45.2,
        apiCalls: 15420,
        monthlyLogins: 1250
      },
      features: ['ai-analysis', 'bulk-messages', 'advanced-reports', 'api-access', 'webhooks'],
      notes: 'Cliente premium com excelente engajamento'
    },
    {
      id: '2',
      name: 'Câmara Municipal de Curitiba',
      plan: 'standard',
      status: 'trial',
      users: 12,
      maxUsers: 15,
      monthlyRevenue: 0,
      createdAt: '2024-01-10T00:00:00Z',
      lastActivity: '2024-01-15T11:20:00Z',
      contactEmail: '<EMAIL>',
      contactPhone: '(41) 3350-4000',
      city: 'Curitiba',
      state: 'PR',
      country: 'Brasil',
      billingCycle: 'monthly',
      nextBillingDate: '2024-01-24T00:00:00Z',
      totalRevenue: 0,
      usageMetrics: {
        activeUsers: 8,
        storageUsed: 2.1,
        apiCalls: 234,
        monthlyLogins: 89
      },
      features: ['ai-analysis', 'bulk-messages', 'advanced-reports'],
      notes: 'Trial ativo - acompanhar conversão'
    },
    {
      id: '3',
      name: 'Assembleia Legislativa do RS',
      plan: 'professional',
      status: 'active',
      users: 45,
      maxUsers: -1,
      monthlyRevenue: 449,
      createdAt: '2023-08-15T00:00:00Z',
      lastActivity: '2024-01-15T16:45:00Z',
      contactEmail: '<EMAIL>',
      contactPhone: '(51) 3210-1234',
      website: 'https://www.al.rs.gov.br',
      city: 'Porto Alegre',
      state: 'RS',
      country: 'Brasil',
      billingCycle: 'monthly',
      nextBillingDate: '2024-02-15T00:00:00Z',
      totalRevenue: 2245,
      usageMetrics: {
        activeUsers: 42,
        storageUsed: 89.7,
        apiCalls: 45230,
        monthlyLogins: 2890
      },
      features: ['ai-analysis', 'bulk-messages', 'advanced-reports', 'api-access', 'webhooks'],
      notes: 'Cliente de longo prazo com alto volume de uso'
    }
  ]);

  const [filteredOrganizations, setFilteredOrganizations] = useState<Organization[]>(organizations);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [planFilter, setPlanFilter] = useState<string>('');
  const [selectedOrganization, setSelectedOrganization] = useState<Organization | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  // Filtrar organizações
  useEffect(() => {
    let filtered = organizations;

    if (searchTerm) {
      filtered = filtered.filter(org => 
        org.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        org.contactEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
        org.city.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (statusFilter) {
      filtered = filtered.filter(org => org.status === statusFilter);
    }

    if (planFilter) {
      filtered = filtered.filter(org => org.plan === planFilter);
    }

    setFilteredOrganizations(filtered);
  }, [organizations, searchTerm, statusFilter, planFilter]);

  // Funções de utilidade
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const getStatusColor = (status: string) => {
    const colors = {
      active: 'text-green-600 bg-green-100',
      trial: 'text-blue-600 bg-blue-100',
      suspended: 'text-yellow-600 bg-yellow-100',
      cancelled: 'text-red-600 bg-red-100'
    };
    return colors[status as keyof typeof colors] || 'text-gray-600 bg-gray-100';
  };

  const getPlanColor = (plan: string) => {
    const colors = {
      basic: 'text-gray-600 bg-gray-100',
      standard: 'text-blue-600 bg-blue-100',
      professional: 'text-purple-600 bg-purple-100'
    };
    return colors[plan as keyof typeof colors] || 'text-gray-600 bg-gray-100';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'trial':
        return <Clock className="h-4 w-4 text-blue-600" />;
      case 'suspended':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'cancelled':
        return <Minus className="h-4 w-4 text-red-600" />;
      default:
        return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  // Métricas resumidas
  const totalRevenue = organizations.reduce((sum, org) => sum + org.totalRevenue, 0);
  const activeOrganizations = organizations.filter(org => org.status === 'active').length;
  const trialOrganizations = organizations.filter(org => org.status === 'trial').length;
  const totalUsers = organizations.reduce((sum, org) => sum + org.users, 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <Building2 className="h-6 w-6 mr-2 text-blue-600" />
            Gerenciamento de Organizações
          </h2>
          <p className="text-gray-600">
            {organizations.length} organizações cadastradas
          </p>
        </div>
        <div className="flex space-x-3">
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Nova Organização
          </Button>
        </div>
      </div>

      {/* Métricas Resumidas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Organizações Ativas</p>
              <p className="text-2xl font-bold text-gray-900">{activeOrganizations}</p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Em Trial</p>
              <p className="text-2xl font-bold text-gray-900">{trialOrganizations}</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <Clock className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Usuários Totais</p>
              <p className="text-2xl font-bold text-gray-900">{totalUsers}</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-full">
              <Users className="h-6 w-6 text-purple-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Receita Total</p>
              <p className="text-2xl font-bold text-gray-900">{formatCurrency(totalRevenue)}</p>
            </div>
            <div className="p-3 bg-yellow-100 rounded-full">
              <DollarSign className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
        </Card>
      </div>

      {/* Filtros */}
      <Card className="p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
              <input
                type="text"
                placeholder="Buscar organizações..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          <select 
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Todos os status</option>
            <option value="active">Ativo</option>
            <option value="trial">Trial</option>
            <option value="suspended">Suspenso</option>
            <option value="cancelled">Cancelado</option>
          </select>
          <select 
            value={planFilter}
            onChange={(e) => setPlanFilter(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">Todos os planos</option>
            <option value="basic">Básico</option>
            <option value="standard">Padrão</option>
            <option value="professional">Profissional</option>
          </select>
        </div>
      </Card>

      {/* Tabela de Organizações */}
      <Card>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Organização
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Plano
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Usuários
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Receita Mensal
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Última Atividade
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ações
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredOrganizations.map((org) => (
                <tr key={org.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{org.name}</div>
                      <div className="text-sm text-gray-500 flex items-center">
                        <Mail className="h-3 w-3 mr-1" />
                        {org.contactEmail}
                      </div>
                      <div className="text-sm text-gray-500">
                        {org.city}, {org.state}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPlanColor(org.plan)}`}>
                      {org.plan}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      {getStatusIcon(org.status)}
                      <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(org.status)}`}>
                        {org.status}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {org.users}{org.maxUsers > 0 ? `/${org.maxUsers}` : ''}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatCurrency(org.monthlyRevenue)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(org.lastActivity)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button 
                        onClick={() => {
                          setSelectedOrganization(org);
                          setShowDetails(true);
                        }}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      <button className="text-green-600 hover:text-green-900">
                        <Edit className="h-4 w-4" />
                      </button>
                      <button className="text-gray-600 hover:text-gray-900">
                        <MoreVertical className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>

      {/* Modal de Detalhes */}
      {showDetails && selectedOrganization && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-xl font-semibold text-gray-900">
                Detalhes da Organização
              </h3>
              <button 
                onClick={() => setShowDetails(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Informações Básicas */}
              <Card className="p-4">
                <h4 className="font-semibold text-gray-900 mb-3">Informações Básicas</h4>
                <div className="space-y-2 text-sm">
                  <div><strong>Nome:</strong> {selectedOrganization.name}</div>
                  <div><strong>Email:</strong> {selectedOrganization.contactEmail}</div>
                  {selectedOrganization.contactPhone && (
                    <div><strong>Telefone:</strong> {selectedOrganization.contactPhone}</div>
                  )}
                  {selectedOrganization.website && (
                    <div><strong>Website:</strong> {selectedOrganization.website}</div>
                  )}
                  <div><strong>Endereço:</strong> {selectedOrganization.address}</div>
                  <div><strong>Cidade:</strong> {selectedOrganization.city}, {selectedOrganization.state}</div>
                </div>
              </Card>

              {/* Informações do Plano */}
              <Card className="p-4">
                <h4 className="font-semibold text-gray-900 mb-3">Plano e Faturamento</h4>
                <div className="space-y-2 text-sm">
                  <div><strong>Plano:</strong> {selectedOrganization.plan}</div>
                  <div><strong>Status:</strong> {selectedOrganization.status}</div>
                  <div><strong>Ciclo:</strong> {selectedOrganization.billingCycle}</div>
                  <div><strong>Próximo Faturamento:</strong> {formatDate(selectedOrganization.nextBillingDate)}</div>
                  <div><strong>Receita Mensal:</strong> {formatCurrency(selectedOrganization.monthlyRevenue)}</div>
                  <div><strong>Receita Total:</strong> {formatCurrency(selectedOrganization.totalRevenue)}</div>
                </div>
              </Card>

              {/* Métricas de Uso */}
              <Card className="p-4">
                <h4 className="font-semibold text-gray-900 mb-3">Métricas de Uso</h4>
                <div className="space-y-2 text-sm">
                  <div><strong>Usuários Totais:</strong> {selectedOrganization.users}</div>
                  <div><strong>Usuários Ativos:</strong> {selectedOrganization.usageMetrics.activeUsers}</div>
                  <div><strong>Logins Mensais:</strong> {selectedOrganization.usageMetrics.monthlyLogins.toLocaleString()}</div>
                  <div><strong>Armazenamento:</strong> {selectedOrganization.usageMetrics.storageUsed} GB</div>
                  <div><strong>Chamadas API:</strong> {selectedOrganization.usageMetrics.apiCalls.toLocaleString()}</div>
                </div>
              </Card>

              {/* Funcionalidades */}
              <Card className="p-4">
                <h4 className="font-semibold text-gray-900 mb-3">Funcionalidades Ativas</h4>
                <div className="flex flex-wrap gap-2">
                  {selectedOrganization.features.map((feature, index) => (
                    <span 
                      key={index}
                      className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      {feature}
                    </span>
                  ))}
                </div>
              </Card>
            </div>

            {selectedOrganization.notes && (
              <Card className="p-4 mt-6">
                <h4 className="font-semibold text-gray-900 mb-3">Observações</h4>
                <p className="text-sm text-gray-600">{selectedOrganization.notes}</p>
              </Card>
            )}

            <div className="flex justify-end space-x-3 mt-6">
              <Button variant="outline" onClick={() => setShowDetails(false)}>
                Fechar
              </Button>
              <Button>
                Editar Organização
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrganizationsManagement;
