import NotificationService from '../services/notificationService.js';

/**
 * Middleware para interceptar eventos e gerar notificações automáticas
 */

class NotificationMiddleware {
  
  /**
   * Middleware para interceptar criação de demandas
   */
  static async onDemandCreated(req, res, next) {
    // Executar após a resposta ser enviada
    res.on('finish', async () => {
      if (res.statusCode >= 200 && res.statusCode < 300) {
        try {
          const demandData = req.body;
          const assignedUserId = demandData.assignedTo || null;
          
          await NotificationService.notifyNewDemand(demandData, assignedUserId);
          console.log('📢 Notificação de nova demanda enviada');
        } catch (error) {
          console.error('Erro ao enviar notificação de nova demanda:', error);
        }
      }
    });
    
    next();
  }

  /**
   * Middleware para interceptar resolução de demandas
   */
  static async onDemandResolved(req, res, next) {
    // Executar após a resposta ser enviada
    res.on('finish', async () => {
      if (res.statusCode >= 200 && res.statusCode < 300) {
        try {
          const demandData = req.body;
          const resolvedByUserId = req.user?.id;
          
          if (demandData.status === 'resolved' || demandData.status === 'completed') {
            await NotificationService.notifyDemandResolved(demandData, resolvedByUserId);
            console.log('✅ Notificação de demanda resolvida enviada');
          }
        } catch (error) {
          console.error('Erro ao enviar notificação de demanda resolvida:', error);
        }
      }
    });
    
    next();
  }

  /**
   * Middleware para interceptar criação de usuários
   */
  static async onUserCreated(req, res, next) {
    // Executar após a resposta ser enviada
    res.on('finish', async () => {
      if (res.statusCode >= 200 && res.statusCode < 300) {
        try {
          const userData = req.body;
          
          // Enviar notificação de boas-vindas
          await NotificationService.notifyUser(
            userData.id || res.locals.createdUserId,
            'Bem-vindo ao ProMandato',
            'Sua conta foi criada com sucesso. Explore as funcionalidades disponíveis.',
            {
              type: 'success',
              category: 'general',
              actionUrl: '/dashboard',
              actionLabel: 'Ir para dashboard',
              metadata: {
                welcomeMessage: true,
                userRole: userData.role
              }
            }
          );
          
          console.log('👋 Notificação de boas-vindas enviada');
        } catch (error) {
          console.error('Erro ao enviar notificação de boas-vindas:', error);
        }
      }
    });
    
    next();
  }

  /**
   * Middleware para interceptar criação de eventos
   */
  static async onEventCreated(req, res, next) {
    // Executar após a resposta ser enviada
    res.on('finish', async () => {
      if (res.statusCode >= 200 && res.statusCode < 300) {
        try {
          const eventData = req.body;
          
          // Calcular horas até o evento
          const eventDate = new Date(eventData.date);
          const now = new Date();
          const hoursUntilEvent = Math.floor((eventDate.getTime() - now.getTime()) / (1000 * 60 * 60));
          
          // Notificar apenas se o evento for nas próximas 48 horas
          if (hoursUntilEvent <= 48 && hoursUntilEvent > 0) {
            await NotificationService.notifyUpcomingEvent(eventData, hoursUntilEvent);
            console.log('📅 Notificação de evento próximo enviada');
          }
        } catch (error) {
          console.error('Erro ao enviar notificação de evento:', error);
        }
      }
    });
    
    next();
  }

  /**
   * Middleware para interceptar atualizações do sistema
   */
  static async onSystemUpdate(updateData) {
    try {
      await NotificationService.notifySystemUpdate(updateData);
      console.log('🔄 Notificação de atualização do sistema enviada');
    } catch (error) {
      console.error('Erro ao enviar notificação de atualização:', error);
    }
  }

  /**
   * Middleware genérico para logs de ações importantes
   */
  static async logImportantAction(req, res, next) {
    // Executar após a resposta ser enviada
    res.on('finish', async () => {
      if (res.statusCode >= 200 && res.statusCode < 300) {
        try {
          const action = req.method;
          const resource = req.route?.path || req.path;
          const userId = req.user?.id;
          
          // Log apenas para ações importantes
          const importantActions = ['POST', 'PUT', 'DELETE'];
          const importantResources = ['/demands', '/events', '/users'];
          
          if (importantActions.includes(action) && 
              importantResources.some(r => resource.includes(r))) {
            
            console.log(`📝 Ação importante registrada: ${action} ${resource} por usuário ${userId}`);
            
            // Aqui você pode adicionar lógica adicional para notificações específicas
            // baseadas na ação realizada
          }
        } catch (error) {
          console.error('Erro ao registrar ação importante:', error);
        }
      }
    });
    
    next();
  }

  /**
   * Verificar prazos e enviar notificações automáticas
   * Esta função deve ser chamada periodicamente (ex: via cron job)
   */
  static async checkDeadlinesAndNotify() {
    try {
      console.log('⏰ Verificando prazos...');
      
      // Aqui você implementaria a lógica para verificar prazos
      // Por exemplo, buscar demandas com deadline próximo
      
      // Exemplo de implementação:
      // const upcomingDeadlines = await Demand.findUpcomingDeadlines();
      // 
      // for (const demand of upcomingDeadlines) {
      //   const daysRemaining = calculateDaysRemaining(demand.deadline);
      //   await NotificationService.notifyDeadlineApproaching(demand, daysRemaining);
      // }
      
      console.log('✅ Verificação de prazos concluída');
    } catch (error) {
      console.error('Erro ao verificar prazos:', error);
    }
  }

  /**
   * Limpeza automática de notificações antigas
   * Esta função deve ser chamada periodicamente (ex: via cron job)
   */
  static async cleanupOldNotifications() {
    try {
      console.log('🧹 Limpando notificações antigas...');
      
      const deletedCount = await NotificationService.cleanupOldNotifications();
      
      if (deletedCount > 0) {
        console.log(`🗑️ ${deletedCount} notificações antigas removidas`);
      } else {
        console.log('✨ Nenhuma notificação antiga para remover');
      }
    } catch (error) {
      console.error('Erro na limpeza de notificações:', error);
    }
  }

  /**
   * Configurar tarefas automáticas (cron jobs)
   */
  static setupAutomaticTasks() {
    // Verificar prazos a cada hora
    setInterval(() => {
      this.checkDeadlinesAndNotify();
    }, 60 * 60 * 1000); // 1 hora

    // Limpeza de notificações antigas a cada 24 horas
    setInterval(() => {
      this.cleanupOldNotifications();
    }, 24 * 60 * 60 * 1000); // 24 horas

    console.log('⚙️ Tarefas automáticas de notificação configuradas');
  }
}

export default NotificationMiddleware;
