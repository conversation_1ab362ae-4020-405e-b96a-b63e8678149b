@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo    CONFIGURAR DOMÍNIO CUSTOMIZADO
echo    ProMandato - www.promandato.com.br
echo ========================================
echo.

echo [1/6] Criando estrutura de diretórios...

:: Criar diretório public se não existir
if not exist "public" mkdir public
if not exist "public\landingpage" mkdir public\landingpage
if not exist "public\app" mkdir public\app

echo ✅ Diretórios criados

echo.
echo [2/6] Copiando arquivos da landing page...

:: Copiar todos os arquivos da landingpage para public/landingpage
xcopy "landingpage\*" "public\landingpage\" /E /Y /Q

if %errorlevel% equ 0 (
    echo ✅ Landing page copiada para public/landingpage/
) else (
    echo ❌ Erro ao copiar landing page
    pause
    exit /b 1
)

echo.
echo [3/6] Fazendo build do frontend...

cd frontend

:: Verificar se package.json existe
if not exist "package.json" (
    echo ❌ package.json não encontrado no diretório frontend
    pause
    exit /b 1
)

:: Instalar dependências e fazer build
call npm ci
call npm run build

if %errorlevel% neq 0 (
    echo ❌ Erro no build do frontend
    pause
    exit /b 1
)

echo ✅ Build do frontend concluído

echo.
echo [4/6] Copiando build do frontend...

cd ..

:: Copiar build do frontend para public/app
xcopy "frontend\dist\*" "public\app\" /E /Y /Q

if %errorlevel% equ 0 (
    echo ✅ Frontend copiado para public/app/
) else (
    echo ❌ Erro ao copiar frontend
    pause
    exit /b 1
)

echo.
echo [5/6] Atualizando configurações...

:: Atualizar URLs na landing page para usar o novo domínio
powershell -Command "(Get-Content 'public\landingpage\config.js') -replace 'https://promandato-9a4cf.web.app', 'https://www.promandato.com.br' | Set-Content 'public\landingpage\config.js'"

:: Atualizar URL da aplicação na landing page
powershell -Command "(Get-Content 'public\landingpage\script.js') -replace 'https://promandato-9a4cf.web.app', 'https://www.promandato.com.br/app' | Set-Content 'public\landingpage\script.js'"

echo ✅ Configurações atualizadas

echo.
echo [6/6] Testando estrutura localmente...

echo Iniciando servidor local...
echo.
echo 📍 URLs disponíveis:
echo   - Página inicial: http://localhost:5000/
echo   - Landing page: http://localhost:5000/landingpage/
echo   - Aplicação: http://localhost:5000/app/
echo.
echo Pressione Ctrl+C para parar o servidor
echo.

firebase serve --only hosting

echo.
echo ========================================
echo     CONFIGURAÇÃO CONCLUÍDA!
echo ========================================
echo.
echo 📋 PRÓXIMOS PASSOS:
echo.
echo 1. Testar localmente:
echo    - http://localhost:5000/landingpage/
echo    - http://localhost:5000/app/
echo.
echo 2. Fazer deploy:
echo    firebase deploy --only hosting
echo.
echo 3. Configurar domínio no Firebase Console:
echo    - Acesse: https://console.firebase.google.com/
echo    - Vá para Hosting
echo    - Clique em "Adicionar domínio personalizado"
echo    - Digite: www.promandato.com.br
echo    - Siga as instruções para configurar DNS
echo.
echo 4. Aguardar propagação DNS (até 24h)
echo.

pause
