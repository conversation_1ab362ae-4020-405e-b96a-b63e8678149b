import React, { useState, useEffect, useCallback } from 'react';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Input, Textarea } from '../components/ui/Input';
import { Modal } from '../components/ui/Modal';
import { ICONS } from '../constants';
import { Citizen, UserRole } from '../types';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';
import { useAuth } from '../hooks/useAuth';
import { getCitizens, addCitizen, updateCitizen, deleteCitizen } from '../services/firebaseService';
import { toast } from 'react-toastify';

const CitizenRow: React.FC<{ citizen: Citizen; onEdit: (citizen: Citizen) => void; onDelete: (id: string) => void }> = ({ citizen, onEdit, onDelete }) => {
  return (
    <tr className="hover:bg-gray-50 dark:hover:bg-neutral-dark">
      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-neutral-dark dark:text-neutral-light">{citizen.fullName}</td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-neutral-DEFAULT">{citizen.email || '-'}</td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-neutral-DEFAULT">{citizen.phone || '-'}</td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-neutral-DEFAULT">
        {citizen.tags?.map(tag => (
          <span key={tag} className="px-2 mr-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-700 dark:text-blue-100">
            {tag}
          </span>
        )) || '-'}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <Button variant="ghost" size="sm" onClick={() => onEdit(citizen)} className="text-primary hover:text-primary-dark dark:text-primary-light dark:hover:text-primary mr-2">
          {React.cloneElement(ICONS.PENCIL, { className: "w-5 h-5"})}
        </Button>
        <Button variant="ghost" size="sm" onClick={() => onDelete(citizen.id)} className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-500">
          {React.cloneElement(ICONS.TRASH, { className: "w-5 h-5"})}
        </Button>
      </td>
    </tr>
  );
};

const CitizensPage: React.FC = () => {
  const [citizens, setCitizens] = useState<Citizen[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentCitizen, setCurrentCitizen] = useState<Partial<Citizen> | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [tagFilter, setTagFilter] = useState('');
  
  // Add the missing state variables for import functionality
  const [isImportModalOpen, setIsImportModalOpen] = useState(false);
  const [importedContacts, setImportedContacts] = useState<Partial<Citizen>[]>([]);
  const [selectedContacts, setSelectedContacts] = useState<Set<number>>(new Set());
  const [isImporting, setIsImporting] = useState(false);
  const [editedContacts, setEditedContacts] = useState<Partial<Citizen>[]>([]);

  const { currentUser } = useAuth();

  // Add the import contact functions
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    setError(null);
    const file = event.target.files?.[0];
    if (!file) return;
    
    // Verificar se é um arquivo CSV
    if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
      setError("Por favor, selecione um arquivo CSV válido.");
      return;
    }
    
    setIsImporting(true);
    
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const csvContent = e.target?.result as string;
        const lines = csvContent.split('\n');
        const headers = lines[0].split(',').map(header => header.trim());
        
        const contacts: Partial<Citizen>[] = [];
        
        // Processar cada linha do CSV (exceto o cabeçalho)
        for (let i = 1; i < lines.length; i++) {
          if (!lines[i].trim()) continue; // Pular linhas vazias
          
          const values = lines[i].split(',').map(value => value.trim());
          const contact: Partial<Citizen> = {};
          
          // Mapear valores para campos do cidadão
          headers.forEach((header, index) => {
            const value = values[index] || '';
            switch(header.toLowerCase()) {
              case 'nome':
              case 'nome completo':
              case 'fullname':
                contact.fullName = value;
                break;
              case 'email':
                contact.email = value;
                break;
              case 'telefone':
              case 'phone':
                contact.phone = value;
                break;
              case 'endereço':
              case 'address':
                contact.address = value;
                break;
              // Adicione mais mapeamentos conforme necessário
            }
          });
          
          // Adicionar apenas contatos com pelo menos nome
          if (contact.fullName) {
            contacts.push(contact);
          }
        }
        
        setImportedContacts(contacts);
        setEditedContacts([...contacts]);
      } catch (err) {
        console.error("Erro ao processar CSV:", err);
        setError("Falha ao processar o arquivo CSV. Verifique o formato.");
      } finally {
        setIsImporting(false);
      }
    };
    
    reader.onerror = () => {
      setError("Erro ao ler o arquivo. Tente novamente.");
      setIsImporting(false);
    };
    
    reader.readAsText(file);
  };

  const fetchCitizens = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const fetchedCitizens = await getCitizens();
      setCitizens(fetchedCitizens);
    } catch (err) {
      console.error("Failed to fetch citizens:", err);
      setError("Falha ao carregar cidadãos. Tente novamente.");
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchCitizens();
  }, [fetchCitizens]);

  const handleOpenModal = (citizen?: Citizen) => {
    const initialVoterRegistration = citizen?.voterRegistration || { number: '', zone: '', section: '' };
    setCurrentCitizen(citizen ? 
        { ...citizen, voterRegistration: initialVoterRegistration } 
        : 
        { fullName: '', tags: [], voterRegistration: initialVoterRegistration }
    );
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setCurrentCitizen(null);
    setError(null); 
  };

  // Add the missing import modal functions
  const handleOpenImportModal = () => {
    setImportedContacts([]);
    setSelectedContacts(new Set());
    setIsImportModalOpen(true);
    setError(null);
  };

  const handleCloseImportModal = () => {
    setIsImportModalOpen(false);
    setError(null);
  };

  const toggleContactSelection = (index: number) => {
    const newSelection = new Set(selectedContacts);
    if (newSelection.has(index)) {
      newSelection.delete(index);
    } else {
      newSelection.add(index);
    }
    setSelectedContacts(newSelection);
  };

  const handleContactFieldChange = (index: number, field: keyof Citizen, value: string) => {
    const updatedContacts = [...editedContacts];
    updatedContacts[index] = { ...updatedContacts[index], [field]: value };
    setEditedContacts(updatedContacts);
  };

  const handleImportGoogleContacts = () => {
    setIsImporting(true);
    setError(null);

    // Simulação de importação do Google
    setTimeout(() => {
      const mockContacts: Partial<Citizen>[] = [
        { fullName: 'João Silva', email: '<EMAIL>', phone: '(11) 98765-4321' },
        { fullName: 'Maria Oliveira', email: '<EMAIL>', phone: '(11) 91234-5678' },
        { fullName: 'Pedro Santos', email: '<EMAIL>', phone: '(11) 99876-5432' }
      ];

      setImportedContacts(mockContacts);
      setEditedContacts([...mockContacts]);
      setIsImporting(false);
    }, 1500);
  };

  const handleImportAppleContacts = () => {
    setIsImporting(true);
    setError(null);

    // Simulação de importação da Apple
    setTimeout(() => {
      const mockContacts: Partial<Citizen>[] = [
        { fullName: 'Ana Pereira', email: '<EMAIL>', phone: '(11) 97777-8888' },
        { fullName: 'Carlos Ferreira', email: '<EMAIL>', phone: '(11) 96666-9999' },
        { fullName: 'Lucia Mendes', email: '<EMAIL>', phone: '(11) 95555-4444' }
      ];

      setImportedContacts(mockContacts);
      setEditedContacts([...mockContacts]);
      setIsImporting(false);
    }, 1500);
  };

  // Adicionar função para obter o masterId correto
  const getMasterId = (): string => {
    if (!currentUser) throw new Error("Usuário não autenticado");
    
    // Se o usuário for master, usa o próprio ID
    if (currentUser.role === UserRole.MASTER) {
      return currentUser.id;
    }
    
    // Se for staff, usa o masterId associado
    if (currentUser.masterId) {
      return currentUser.masterId;
    }
    
    // Fallback para o ID do usuário atual (não deveria acontecer)
    console.warn("MasterId não encontrado, usando ID do usuário atual como fallback");
    return currentUser.id;
  };

  const handleImportSelectedContacts = async () => {
    if (!currentUser) return;
    
    setIsSaving(true);
    setError(null);
    try {
      const masterId = getMasterId();
      const contactsToImport = Array.from(selectedContacts).map(index => {
        // Garantir que cada contato tenha o masterId correto
        return {
          ...editedContacts[index],
          masterId: masterId
        };
      });
      
      for (const contact of contactsToImport) {
        await addCitizen(contact as Omit<Citizen, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>, currentUser.id);
      }
      
      await fetchCitizens();
      handleCloseImportModal();
      // Substituindo toast.success por uma mensagem de sucesso no estado
      setError(null); // Limpar qualquer erro anterior
      // Opcionalmente, podemos mostrar uma mensagem de sucesso temporária
      const successMessage = `${contactsToImport.length} contatos importados com sucesso!`;
      alert(successMessage); // Solução temporária usando alert
    } catch (err) {
      console.error("Failed to save imported contacts:", err);
      setError("Falha ao salvar contatos importados. Tente novamente.");
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveCitizen = async () => {
    if (!currentCitizen || !currentUser) {
      setError("Dados do cidadão ou usuário inválidos.");
      return;
    }
    if (!currentCitizen.fullName) {
        setError("Nome completo é obrigatório.");
        return;
    }

    setIsSaving(true);
    setError(null);
    try {
      const citizenDataToSave = { ...currentCitizen };
      
      (Object.keys(citizenDataToSave) as Array<keyof typeof citizenDataToSave>).forEach(key => {
        if (typeof citizenDataToSave[key] === 'string' && citizenDataToSave[key] === '') {
          delete citizenDataToSave[key];
        }
      });
      if (citizenDataToSave.socialMedia) {
        (Object.keys(citizenDataToSave.socialMedia) as Array<keyof NonNullable<typeof citizenDataToSave.socialMedia>>).forEach(key => {
            if (citizenDataToSave.socialMedia![key] === '') {
                delete citizenDataToSave.socialMedia![key];
            }
        });
        if (Object.keys(citizenDataToSave.socialMedia).length === 0) {
            delete citizenDataToSave.socialMedia;
        }
      }
      if (citizenDataToSave.voterRegistration) {
        (Object.keys(citizenDataToSave.voterRegistration) as Array<keyof NonNullable<typeof citizenDataToSave.voterRegistration>>).forEach(key => {
            if (citizenDataToSave.voterRegistration![key] === '') {
                delete citizenDataToSave.voterRegistration![key];
            }
        });
        if (Object.keys(citizenDataToSave.voterRegistration).length === 0) {
            delete citizenDataToSave.voterRegistration;
        }
      }


      if (currentCitizen.id) { 
        const { id, createdBy, createdAt, ...updateData } = citizenDataToSave;
        await updateCitizen(id!, updateData as Omit<Citizen, 'id' | 'createdAt' | 'createdBy'>);
      } else { 
        const newCitizenData = {
            fullName: citizenDataToSave.fullName!,
            email: citizenDataToSave.email,
            phone: citizenDataToSave.phone,
            address: citizenDataToSave.address,
            birthDate: citizenDataToSave.birthDate,
            voterRegistration: citizenDataToSave.voterRegistration,
            neighborhood: citizenDataToSave.neighborhood,
            workplace: citizenDataToSave.workplace,
            profession: citizenDataToSave.profession,
            socialMedia: citizenDataToSave.socialMedia,
            whatsapp: citizenDataToSave.whatsapp,
            notes: citizenDataToSave.notes,
            tags: citizenDataToSave.tags || [],
        };
        await addCitizen(newCitizenData as Omit<Citizen, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>, currentUser.id);
      }
      await fetchCitizens(); 
      handleCloseModal();
    } catch (err) {
      console.error("Failed to save citizen:", err);
      setError("Falha ao salvar cidadão. Tente novamente.");
    } finally {
      setIsSaving(false);
    }
  };
  
  const handleDeleteCitizen = async (id: string) => {
     if (window.confirm("Tem certeza que deseja excluir este cidadão?")) {
        setIsLoading(true);
        setError(null);
        try {
            await deleteCitizen(id);
            await fetchCitizens();
        } catch (err) {
            console.error("Failed to delete citizen:", err);
            setError("Falha ao excluir cidadão. Tente novamente.");
        } finally {
            setIsLoading(false);
        }
    }
  };

  const allTags = Array.from(new Set(citizens.flatMap(c => c.tags || []))).sort();

  const filteredCitizens = citizens
    .filter(c => 
        (c.fullName || '').toLowerCase().includes(searchTerm.toLowerCase()) || 
        (c.email || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        (c.phone || '').includes(searchTerm) ||
        (c.neighborhood || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
        (c.profession || '').toLowerCase().includes(searchTerm.toLowerCase())
    )
    .filter(c => tagFilter ? (c.tags && c.tags.includes(tagFilter)) : true);


  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-neutral-dark dark:text-neutral-light">Cadastro de Cidadãos</h1>
        <div className="flex space-x-2">
          <Button onClick={handleOpenImportModal} leftIcon={ICONS.PLUS} variant="outline">
            Importar Contatos
          </Button>
          <Button onClick={() => handleOpenModal()} leftIcon={ICONS.PLUS}>
            Novo Cidadão
          </Button>
        </div>
      </div>

      {error && !isModalOpen && <p className="text-red-500 dark:text-red-400 bg-red-100 dark:bg-red-900 dark:bg-opacity-30 p-3 rounded-md">{error}</p>}

      <Card>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4 p-4 bg-gray-50 dark:bg-neutral-dark rounded-md">
          <div className="flex flex-col">
            <label htmlFor="search" className="block text-sm font-medium text-neutral-dark dark:text-neutral-light mb-1">
              Buscar Cidadãos
            </label>
            <Input 
              id="search"
              name="search"
              placeholder="Buscar por nome, email, tel, bairro..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex flex-col">
            <label htmlFor="tagFilter" className="block text-sm font-medium text-neutral-dark dark:text-neutral-light mb-1">
              Filtrar por Tag
            </label>
            <select 
              id="tagFilter"
              name="tagFilter"
              className="block w-full px-3 py-2 border border-gray-300 dark:border-neutral-medium rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm bg-white dark:bg-neutral-dark dark:text-neutral-light"
              value={tagFilter}
              onChange={(e) => setTagFilter(e.target.value)}
            >
              <option value="">Todas as Tags</option>
              {allTags.map(tag => (
                <option key={tag} value={tag}>{tag}</option>
              ))}
            </select>
          </div>
        </div>
        
        {isLoading && !citizens.length ? (
          <div className="flex justify-center items-center py-10">
            <LoadingSpinner size="lg" />
          </div>
        ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-neutral-dark">
            <thead className="bg-gray-50 dark:bg-neutral-darker">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-neutral-DEFAULT uppercase tracking-wider">Nome Completo</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-neutral-DEFAULT uppercase tracking-wider">Email</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-neutral-DEFAULT uppercase tracking-wider">Telefone</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-neutral-DEFAULT uppercase tracking-wider">Tags</th>
                <th scope="col" className="relative px-6 py-3"><span className="sr-only">Ações</span></th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-neutral-darker divide-y divide-gray-200 dark:divide-neutral-dark">
              {filteredCitizens.length > 0 ? (
                filteredCitizens.map((citizen) => (
                  <CitizenRow key={citizen.id} citizen={citizen} onEdit={handleOpenModal} onDelete={handleDeleteCitizen} />
                ))
              ) : (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-neutral-DEFAULT">
                    {searchTerm || tagFilter ? 'Nenhum cidadão encontrado com os filtros aplicados.' : 'Nenhum cidadão cadastrado.'}
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        )}
      </Card>

      <Modal isOpen={isModalOpen} onClose={handleCloseModal} title={currentCitizen?.id ? "Editar Cidadão" : "Novo Cidadão"} size="xl">
        <div className="space-y-4 max-h-[75vh] overflow-y-auto p-1">
          {error && <p className="text-red-500 dark:text-red-400 bg-red-100 dark:bg-red-900 dark:bg-opacity-30 p-2 rounded-md text-sm mb-3">{error}</p>}
          
          <fieldset className="border dark:border-neutral-medium p-4 rounded-md">
            <legend className="text-md font-semibold text-neutral-dark dark:text-neutral-light px-2">Informações Pessoais</legend>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-3 mt-2">
              <Input
                id="fullName"
                name="fullName"
                label="Nome Completo *"
                value={currentCitizen?.fullName || ''}
                onChange={(e) => setCurrentCitizen(prev => ({ ...prev, fullName: e.target.value }))}
                required
                disabled={isSaving}
                className="md:col-span-2"
              />
              <Input
                id="email"
                name="email"
                label="Email"
                type="email"
                value={currentCitizen?.email || ''}
                onChange={(e) => setCurrentCitizen(prev => ({ ...prev, email: e.target.value }))}
                disabled={isSaving}
              />
              <Input
                id="phone"
                name="phone"
                label="Telefone"
                type="tel"
                value={currentCitizen?.phone || ''}
                onChange={(e) => setCurrentCitizen(prev => ({ ...prev, phone: e.target.value }))}
                placeholder="(XX) XXXXX-XXXX"
                disabled={isSaving}
              />
              <Input
                id="whatsapp"
                name="whatsapp"
                label="WhatsApp"
                type="tel"
                value={currentCitizen?.whatsapp || ''}
                onChange={(e) => setCurrentCitizen(prev => ({ ...prev, whatsapp: e.target.value }))}
                placeholder="(XX) XXXXX-XXXX"
                disabled={isSaving}
              />
              <Input
                id="birthDate"
                name="birthDate"
                label="Data de Nascimento"
                type="date"
                value={currentCitizen?.birthDate || ''}
                onChange={(e) => setCurrentCitizen(prev => ({ ...prev, birthDate: e.target.value }))}
                disabled={isSaving}
              />
              <Input
                id="address"
                name="address"
                label="Endereço Completo"
                value={currentCitizen?.address || ''}
                onChange={(e) => setCurrentCitizen(prev => ({ ...prev, address: e.target.value }))}
                disabled={isSaving}
                className="md:col-span-2"
              />
              <Input
                id="neighborhood"
                name="neighborhood"
                label="Bairro"
                value={currentCitizen?.neighborhood || ''}
                onChange={(e) => setCurrentCitizen(prev => ({ ...prev, neighborhood: e.target.value }))}
                disabled={isSaving}
              />
            </div>
          </fieldset>

          <fieldset className="border dark:border-neutral-medium p-4 rounded-md">
            <legend className="text-md font-semibold text-neutral-dark dark:text-neutral-light px-2">Informações Eleitorais</legend>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-x-4 gap-y-3 mt-2">
              <Input
                id="voterNumber"
                name="voterNumber"
                label="Nº Título de Eleitor"
                value={currentCitizen?.voterRegistration?.number || ''}
                onChange={(e) => setCurrentCitizen(prev => ({ ...prev, voterRegistration: { ...(prev?.voterRegistration || {}), number: e.target.value } }))}
                disabled={isSaving}
              />
              <Input
                id="voterZone"
                name="voterZone"
                label="Zona Eleitoral"
                value={currentCitizen?.voterRegistration?.zone || ''}
                onChange={(e) => setCurrentCitizen(prev => ({ ...prev, voterRegistration: { ...(prev?.voterRegistration || {}), zone: e.target.value } }))}
                disabled={isSaving}
              />
              <Input
                id="voterSection"
                name="voterSection"
                label="Seção Eleitoral"
                value={currentCitizen?.voterRegistration?.section || ''}
                onChange={(e) => setCurrentCitizen(prev => ({ ...prev, voterRegistration: { ...(prev?.voterRegistration || {}), section: e.target.value } }))}
                disabled={isSaving}
              />
            </div>
          </fieldset>
          
          <fieldset className="border dark:border-neutral-medium p-4 rounded-md">
            <legend className="text-md font-semibold text-neutral-dark dark:text-neutral-light px-2">Informações Profissionais</legend>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-3 mt-2">
              <Input
                id="profession"
                name="profession"
                label="Profissão"
                value={currentCitizen?.profession || ''}
                onChange={(e) => setCurrentCitizen(prev => ({ ...prev, profession: e.target.value }))}
                disabled={isSaving}
              />
              <Input
                id="workplace"
                name="workplace"
                label="Local de Trabalho"
                value={currentCitizen?.workplace || ''}
                onChange={(e) => setCurrentCitizen(prev => ({ ...prev, workplace: e.target.value }))}
                disabled={isSaving}
              />
            </div>
          </fieldset>

          <fieldset className="border dark:border-neutral-medium p-4 rounded-md">
            <legend className="text-md font-semibold text-neutral-dark dark:text-neutral-light px-2">Redes Sociais</legend>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-3 mt-2">
                <Input
                id="facebook"
                name="facebook"
                label="Facebook (URL ou usuário)"
                value={currentCitizen?.socialMedia?.facebook || ''}
                onChange={(e) => setCurrentCitizen(prev => ({ ...prev, socialMedia: { ...(prev?.socialMedia || {}), facebook: e.target.value } }))}
                disabled={isSaving}
                />
                <Input
                id="instagram"
                name="instagram"
                label="Instagram (@usuário)"
                value={currentCitizen?.socialMedia?.instagram || ''}
                onChange={(e) => setCurrentCitizen(prev => ({ ...prev, socialMedia: { ...(prev?.socialMedia || {}), instagram: e.target.value } }))}
                disabled={isSaving}
                />
                <Input
                id="twitter"
                name="twitter"
                label="Twitter/X (@usuário)"
                value={currentCitizen?.socialMedia?.twitter || ''}
                onChange={(e) => setCurrentCitizen(prev => ({ ...prev, socialMedia: { ...(prev?.socialMedia || {}), twitter: e.target.value } }))}
                disabled={isSaving}
                />
                <Input
                id="linkedin"
                name="linkedin"
                label="LinkedIn (URL do perfil)"
                value={currentCitizen?.socialMedia?.linkedin || ''}
                onChange={(e) => setCurrentCitizen(prev => ({ ...prev, socialMedia: { ...(prev?.socialMedia || {}), linkedin: e.target.value } }))}
                disabled={isSaving}
                />
                <Input
                id="tiktok"
                name="tiktok"
                label="TikTok (@usuário)"
                value={currentCitizen?.socialMedia?.tiktok || ''}
                onChange={(e) => setCurrentCitizen(prev => ({ ...prev, socialMedia: { ...(prev?.socialMedia || {}), tiktok: e.target.value } }))}
                disabled={isSaving}
                className="md:col-span-2"
                />
            </div>
          </fieldset>
          
          <fieldset className="border dark:border-neutral-medium p-4 rounded-md">
            <legend className="text-md font-semibold text-neutral-dark dark:text-neutral-light px-2">Outras Informações</legend>
            <div className="mt-2">
                <Input
                    id="tags"
                    name="tags"
                    label="Tags (separadas por vírgula)"
                    value={currentCitizen?.tags?.join(', ') || ''}
                    onChange={(e) => setCurrentCitizen(prev => ({ ...prev, tags: e.target.value.split(',').map(tag => tag.trim()).filter(Boolean) }))}
                    placeholder="Ex: apoiador, zona_norte, demanda_saude"
                    disabled={isSaving}
                />
                <Textarea
                    id="notes"
                    name="notes"
                    label="Observações"
                    value={currentCitizen?.notes || ''}
                    onChange={(e) => setCurrentCitizen(prev => ({ ...prev, notes: e.target.value }))}
                    rows={3}
                    disabled={isSaving}
                    className="mt-3"
                />
            </div>
          </fieldset>

          <div className="flex justify-end space-x-2 pt-4 sticky bottom-0 bg-white dark:bg-neutral-darker py-3 -mx-1 px-1 border-t dark:border-neutral-medium">
            <Button variant="outline" onClick={handleCloseModal} disabled={isSaving}>Cancelar</Button>
            <Button onClick={handleSaveCitizen} isLoading={isSaving} disabled={isSaving}>
              {currentCitizen?.id ? "Salvar Alterações" : "Criar Cidadão"}
            </Button>
          </div>
        </div>
      </Modal>

      <Modal isOpen={isImportModalOpen} onClose={handleCloseImportModal} title="Importar Contatos" size="lg">
        <div className="space-y-4 max-h-[75vh] overflow-y-auto p-1">
          {error && <p className="text-red-500 dark:text-red-400 bg-red-100 dark:bg-red-900 dark:bg-opacity-30 p-2 rounded-md text-sm mb-3">{error}</p>}
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Importar contatos via CSV
            </label>
            <div className="flex flex-col space-y-2">
              <input
                type="file"
                accept=".csv"
                onChange={handleFileUpload}
                className="block w-full text-sm text-gray-500 dark:text-gray-400
                  file:mr-4 file:py-2 file:px-4
                  file:rounded-md file:border-0
                  file:text-sm file:font-medium
                  file:bg-primary file:text-white
                  hover:file:bg-primary-dark"
                disabled={isImporting}
                aria-label="Selecionar arquivo CSV para importação"
              />
              <p className="text-xs text-gray-500 dark:text-gray-400">
                O arquivo CSV deve conter colunas para Nome, Email e Telefone.
                <a href="#" className="text-primary hover:underline ml-1">Baixar modelo</a>
              </p>
            </div>
          </div>
          
          <div className="flex items-center my-4">
            <div className="flex-grow border-t border-gray-300 dark:border-gray-700"></div>
            <span className="flex-shrink mx-4 text-gray-500 dark:text-gray-400 text-sm">ou</span>
            <div className="flex-grow border-t border-gray-300 dark:border-gray-700"></div>
          </div>
          
          <div className="flex space-x-2 mb-4">
            <Button
              onClick={handleImportGoogleContacts}
              leftIcon={ICONS.USER}
              disabled={isImporting}
              className="flex-1"
            >
              Importar do Google
            </Button>
            <Button
              onClick={handleImportAppleContacts}
              leftIcon={ICONS.USER_CIRCLE}
              disabled={isImporting}
              className="flex-1"
            >
              Importar da Apple
            </Button>
          </div>
          
          {isImporting ? (
            <div className="flex justify-center items-center py-10">
              <LoadingSpinner size="lg" />
            </div>
          ) : importedContacts.length > 0 ? (
            <div>
              <div className="mb-2 flex justify-between items-center">
                <h3 className="font-medium">Contatos Encontrados ({importedContacts.length})</h3>
                <span className="text-sm text-gray-500">Selecionados: {selectedContacts.size}</span>
              </div>
              
              <div className="border rounded-md overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200 dark:divide-neutral-dark">
                  <thead className="bg-gray-50 dark:bg-neutral-darker">
                    <tr>
                      <th scope="col" className="px-3 py-2 w-10"></th>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-neutral-DEFAULT uppercase tracking-wider">Nome</th>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-neutral-DEFAULT uppercase tracking-wider">Email</th>
                      <th scope="col" className="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-neutral-DEFAULT uppercase tracking-wider">Telefone</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-neutral-darker divide-y divide-gray-200 dark:divide-neutral-dark">
                    {importedContacts.map((contact: Partial<Citizen>, index: number) => (
                      <tr key={index} className="hover:bg-gray-50 dark:hover:bg-neutral-dark">
                        <td className="px-3 py-2">
                          <input 
                            type="checkbox" 
                            checked={selectedContacts.has(index)}
                            onChange={() => toggleContactSelection(index)}
                            className="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary"
                            aria-label={`Selecionar ${contact.fullName}`}
                          />
                        </td>
                        <td className="px-3 py-2 text-sm">{contact.fullName}</td>
                        <td className="px-3 py-2 text-sm">
                          <Input
                            value={editedContacts[index]?.email || ''}
                            onChange={(e) => handleContactFieldChange(index, 'email', e.target.value)}
                            className="py-1 px-2 h-8 text-sm"
                            disabled={!selectedContacts.has(index)}
                          />
                        </td>
                        <td className="px-3 py-2 text-sm">
                          <Input
                            value={editedContacts[index]?.phone || ''}
                            onChange={(e) => handleContactFieldChange(index, 'phone', e.target.value)}
                            className="py-1 px-2 h-8 text-sm"
                            disabled={!selectedContacts.has(index)}
                          />
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500 dark:text-neutral-DEFAULT">
              Selecione uma fonte para importar contatos
            </div>
          )}
          
          <div className="flex justify-end space-x-2 pt-4 sticky bottom-0 bg-white dark:bg-neutral-darker py-3 -mx-1 px-1 border-t dark:border-neutral-medium">
            <Button variant="outline" onClick={handleCloseImportModal} disabled={isImporting || isSaving}>Cancelar</Button>
            <Button 
              onClick={handleImportSelectedContacts} 
              isLoading={isSaving} 
              disabled={isImporting || isSaving || selectedContacts.size === 0}
            >
              Importar {selectedContacts.size} Contato{selectedContacts.size !== 1 ? 's' : ''}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};



export default CitizensPage;
