import express from 'express';
import { authenticateToken } from '../middleware/auth.js';
import { checkAIFeature, incrementUsage } from '../middleware/planLimits.js';

const router = express.Router();

/**
 * Análise de Sentimento - Disponível nos planos Padrão e Profissional
 */
router.post('/sentiment-analysis', 
  authenticateToken,
  checkAIFeature('sentiment-analysis'),
  incrementUsage('apiCalls'),
  async (req, res) => {
    try {
      const { text, source = 'manual' } = req.body;

      if (!text || text.trim().length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Texto é obrigatório para análise'
        });
      }

      // Simulação de análise de sentimento (em produção, usar API real de IA)
      const sentiment = analyzeSentiment(text);
      
      const result = {
        text: text.substring(0, 200) + (text.length > 200 ? '...' : ''),
        sentiment: sentiment.label,
        confidence: sentiment.confidence,
        scores: sentiment.scores,
        source,
        analyzedAt: new Date().toISOString(),
        organizationId: req.user.organizationId
      };

      res.json({
        success: true,
        data: result
      });

    } catch (error) {
      console.error('Erro na análise de sentimento:', error);
      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      });
    }
  }
);

/**
 * Categorização Automática - Disponível apenas no plano Profissional
 */
router.post('/auto-categorization',
  authenticateToken,
  checkAIFeature('auto-categorization'),
  incrementUsage('apiCalls'),
  async (req, res) => {
    try {
      const { text, type = 'demand' } = req.body;

      if (!text || text.trim().length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Texto é obrigatório para categorização'
        });
      }

      // Simulação de categorização automática
      const category = categorizeText(text, type);

      const result = {
        text: text.substring(0, 200) + (text.length > 200 ? '...' : ''),
        category: category.category,
        subcategory: category.subcategory,
        confidence: category.confidence,
        suggestedTags: category.tags,
        priority: category.priority,
        type,
        analyzedAt: new Date().toISOString(),
        organizationId: req.user.organizationId
      };

      res.json({
        success: true,
        data: result
      });

    } catch (error) {
      console.error('Erro na categorização automática:', error);
      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      });
    }
  }
);

/**
 * Análise Preditiva - Disponível apenas no plano Profissional
 */
router.post('/predictive-analysis',
  authenticateToken,
  checkAIFeature('predictive-analysis'),
  incrementUsage('apiCalls'),
  async (req, res) => {
    try {
      const { data, type = 'demand_volume', period = '30d' } = req.body;

      if (!data || !Array.isArray(data)) {
        return res.status(400).json({
          success: false,
          message: 'Dados históricos são obrigatórios para análise preditiva'
        });
      }

      // Simulação de análise preditiva
      const prediction = predictTrends(data, type, period);

      const result = {
        type,
        period,
        prediction: prediction.forecast,
        confidence: prediction.confidence,
        trends: prediction.trends,
        insights: prediction.insights,
        recommendations: prediction.recommendations,
        analyzedAt: new Date().toISOString(),
        organizationId: req.user.organizationId
      };

      res.json({
        success: true,
        data: result
      });

    } catch (error) {
      console.error('Erro na análise preditiva:', error);
      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      });
    }
  }
);

/**
 * Respostas Inteligentes - Disponível apenas no plano Profissional
 */
router.post('/smart-responses',
  authenticateToken,
  checkAIFeature('smart-responses'),
  incrementUsage('apiCalls'),
  async (req, res) => {
    try {
      const { demandText, category, context = {} } = req.body;

      if (!demandText || demandText.trim().length === 0) {
        return res.status(400).json({
          success: false,
          message: 'Texto da demanda é obrigatório'
        });
      }

      // Simulação de geração de respostas inteligentes
      const responses = generateSmartResponses(demandText, category, context);

      const result = {
        demandText: demandText.substring(0, 200) + (demandText.length > 200 ? '...' : ''),
        category,
        suggestedResponses: responses.suggestions,
        tone: responses.tone,
        urgency: responses.urgency,
        followUpActions: responses.followUpActions,
        generatedAt: new Date().toISOString(),
        organizationId: req.user.organizationId
      };

      res.json({
        success: true,
        data: result
      });

    } catch (error) {
      console.error('Erro na geração de respostas inteligentes:', error);
      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      });
    }
  }
);

/**
 * Obter funcionalidades de IA disponíveis para o plano atual
 */
router.get('/features',
  authenticateToken,
  async (req, res) => {
    try {
      const features = {
        basic: {
          available: [],
          message: 'Funcionalidades de IA não estão disponíveis no plano Básico. Faça upgrade para o plano Padrão ou Profissional.'
        },
        standard: {
          available: [
            {
              id: 'sentiment-analysis',
              name: 'Análise de Sentimento',
              description: 'Análise automática de sentimentos em textos',
              endpoint: '/api/ai/sentiment-analysis'
            }
          ],
          message: 'Plano Padrão inclui análise de sentimento. Upgrade para o Profissional para funcionalidades completas de IA.'
        },
        professional: {
          available: [
            {
              id: 'sentiment-analysis',
              name: 'Análise de Sentimento',
              description: 'Análise automática de sentimentos em textos',
              endpoint: '/api/ai/sentiment-analysis'
            },
            {
              id: 'auto-categorization',
              name: 'Categorização Automática',
              description: 'Categorização automática de demandas e conteúdo',
              endpoint: '/api/ai/auto-categorization'
            },
            {
              id: 'predictive-analysis',
              name: 'Análise Preditiva',
              description: 'Previsões baseadas em dados históricos',
              endpoint: '/api/ai/predictive-analysis'
            },
            {
              id: 'smart-responses',
              name: 'Respostas Inteligentes',
              description: 'Sugestões automáticas de respostas',
              endpoint: '/api/ai/smart-responses'
            }
          ],
          message: 'Plano Profissional com IA completa ativada.'
        }
      };

      const planId = req.planInfo?.planId || 'basic';
      
      res.json({
        success: true,
        data: {
          currentPlan: planId,
          ...features[planId]
        }
      });

    } catch (error) {
      console.error('Erro ao obter funcionalidades de IA:', error);
      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      });
    }
  }
);

// Funções auxiliares para simulação de IA (em produção, usar APIs reais)

function analyzeSentiment(text) {
  // Simulação simples de análise de sentimento
  const positiveWords = ['bom', 'ótimo', 'excelente', 'maravilhoso', 'perfeito', 'adorei', 'parabéns'];
  const negativeWords = ['ruim', 'péssimo', 'horrível', 'terrível', 'odeio', 'problema', 'reclamação'];
  
  const words = text.toLowerCase().split(/\s+/);
  let positiveScore = 0;
  let negativeScore = 0;
  
  words.forEach(word => {
    if (positiveWords.some(pw => word.includes(pw))) positiveScore++;
    if (negativeWords.some(nw => word.includes(nw))) negativeScore++;
  });
  
  const total = positiveScore + negativeScore;
  if (total === 0) {
    return { label: 'neutral', confidence: 0.5, scores: { positive: 0.33, negative: 0.33, neutral: 0.34 } };
  }
  
  if (positiveScore > negativeScore) {
    return { 
      label: 'positive', 
      confidence: positiveScore / total,
      scores: { positive: 0.7, negative: 0.1, neutral: 0.2 }
    };
  } else if (negativeScore > positiveScore) {
    return { 
      label: 'negative', 
      confidence: negativeScore / total,
      scores: { positive: 0.1, negative: 0.7, neutral: 0.2 }
    };
  } else {
    return { label: 'neutral', confidence: 0.5, scores: { positive: 0.3, negative: 0.3, neutral: 0.4 } };
  }
}

function categorizeText(text, type) {
  const categories = {
    demand: {
      'infraestrutura': ['buraco', 'asfalto', 'rua', 'calçada', 'ponte', 'estrada'],
      'saude': ['posto', 'hospital', 'médico', 'remédio', 'consulta', 'saúde'],
      'educacao': ['escola', 'professor', 'ensino', 'educação', 'creche'],
      'seguranca': ['policia', 'segurança', 'roubo', 'violência', 'iluminação'],
      'meio-ambiente': ['lixo', 'poluição', 'árvore', 'parque', 'meio ambiente']
    }
  };
  
  const words = text.toLowerCase().split(/\s+/);
  const scores = {};
  
  Object.keys(categories[type] || {}).forEach(category => {
    scores[category] = 0;
    categories[type][category].forEach(keyword => {
      words.forEach(word => {
        if (word.includes(keyword)) scores[category]++;
      });
    });
  });
  
  const bestCategory = Object.keys(scores).reduce((a, b) => scores[a] > scores[b] ? a : b);
  const confidence = scores[bestCategory] / words.length;
  
  return {
    category: bestCategory,
    subcategory: null,
    confidence: Math.min(confidence * 2, 1),
    tags: categories[type][bestCategory] || [],
    priority: confidence > 0.3 ? 'high' : 'medium'
  };
}

function predictTrends(data, type, period) {
  // Simulação simples de análise preditiva
  const trend = data.length > 1 ? (data[data.length - 1] - data[0]) / data.length : 0;
  
  return {
    forecast: data.map((_, i) => data[data.length - 1] + (trend * (i + 1))),
    confidence: 0.75,
    trends: trend > 0 ? 'increasing' : trend < 0 ? 'decreasing' : 'stable',
    insights: [
      `Tendência ${trend > 0 ? 'crescente' : trend < 0 ? 'decrescente' : 'estável'} identificada`,
      `Variação média de ${Math.abs(trend).toFixed(2)} por período`
    ],
    recommendations: [
      'Monitorar tendência nos próximos períodos',
      'Considerar ações preventivas se necessário'
    ]
  };
}

function generateSmartResponses(demandText, category, context) {
  const responses = {
    infraestrutura: [
      'Agradecemos sua solicitação. Nossa equipe de infraestrutura irá avaliar a situação.',
      'Recebemos sua demanda e já encaminhamos para o setor responsável.',
      'Sua solicitação foi registrada e será analisada pela equipe técnica.'
    ],
    saude: [
      'Sua demanda de saúde foi recebida e encaminhada para a Secretaria de Saúde.',
      'Agradecemos o contato. A equipe de saúde irá analisar sua solicitação.',
      'Recebemos sua solicitação e ela será avaliada pelos profissionais competentes.'
    ]
  };
  
  const defaultResponses = [
    'Agradecemos seu contato. Sua demanda foi recebida e será analisada.',
    'Recebemos sua solicitação e ela será encaminhada para o setor responsável.',
    'Sua demanda foi registrada e receberá a devida atenção.'
  ];
  
  return {
    suggestions: responses[category] || defaultResponses,
    tone: 'formal',
    urgency: 'normal',
    followUpActions: [
      'Acompanhar status da demanda',
      'Notificar cidadão sobre andamento',
      'Definir prazo para resolução'
    ]
  };
}

export default router;
