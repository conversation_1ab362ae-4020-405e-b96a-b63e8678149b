const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value);
};

const formatPercentage = (value: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'percent',
    minimumFractionDigits: 1,
    maximumFractionDigits: 1
  }).format(value / 100);
};

const formatNumber = (value: number): string => {
  return new Intl.NumberFormat('pt-BR').format(value);
};

const formatDate = (isoDate: string): string => {
  return new Date(isoDate).toLocaleDateString('pt-BR', {
    day: '2-digit',
    month: 'short',
    year: 'numeric'
  });
};

const formatDateRange = (start: string, end: string): string => {
  return `${formatDate(start)} - ${formatDate(end)}`;
};

const calculatePerformanceMetrics = (
  completed: number,
  total: number,
  overdue: number
) => {
  const completionRate = total > 0 ? (completed / total) * 100 : 0;
  const overdueRate = total > 0 ? (overdue / total) * 100 : 0;
  
  return {
    completionRate: formatPercentage(completionRate),
    overdueRate: formatPercentage(overdueRate),
    efficiency: total > 0 ? formatPercentage(Math.max(0, 100 - overdueRate)) : '0%'
  };
};

const calculateEngagementMetrics = (
  followers: number,
  engagement: number,
  reach: number
) => {
  return {
    followersFormatted: formatNumber(followers),
    engagementFormatted: formatPercentage(engagement),
    reachFormatted: formatNumber(reach),
    engagementPerFollower: formatNumber(Math.round((engagement * reach) / (followers || 1)))
  };
};

export {
  formatCurrency,
  formatPercentage,
  formatNumber,
  formatDate,
  formatDateRange,
  calculatePerformanceMetrics,
  calculateEngagementMetrics
};
