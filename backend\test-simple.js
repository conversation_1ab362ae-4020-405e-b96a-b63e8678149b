import User from './models/User.js';
import Notification from './models/Notification.js';
import NotificationService from './services/notificationService.js';

async function testSimple() {
  try {
    console.log('🧪 Teste simples do sistema de notificações...');
    
    // 1. Testar carregamento de usuários
    console.log('1. Testando carregamento de usuários...');
    const users = await User.findAll();
    console.log(`✅ ${users.length} usuários encontrados`);
    
    if (users.length === 0) {
      console.log('❌ Nenhum usuário encontrado');
      return;
    }
    
    const firstUser = users[0];
    console.log(`📋 Primeiro usuário: ${firstUser.name} (${firstUser.email})`);
    
    // 2. Testar criação de notificação
    console.log('\n2. Testando criação de notificação...');
    const notification = await NotificationService.notifyUser(
      firstUser.id,
      'Teste de Notificação',
      'Esta é uma notificação de teste do sistema',
      {
        type: 'info',
        category: 'general'
      }
    );
    console.log(`✅ Notificação criada: ${notification.id}`);
    
    // 3. Testar busca de notificações
    console.log('\n3. Testando busca de notificações...');
    const userNotifications = await Notification.findByUserId(firstUser.id);
    console.log(`✅ ${userNotifications.length} notificações encontradas para o usuário`);
    
    // 4. Testar contagem de não lidas
    console.log('\n4. Testando contagem de não lidas...');
    const unreadCount = await Notification.getUnreadCountForUser(firstUser.id);
    console.log(`✅ ${unreadCount} notificações não lidas`);
    
    console.log('\n🎉 Teste simples concluído com sucesso!');
    
  } catch (error) {
    console.error('❌ Erro no teste:', error);
  }
}

testSimple();
