@echo off
echo ========================================
echo     ETAPA 2: CONFIGURAR PAGAMENTOS
echo ========================================
echo.

echo [1/4] Verificando integracao Stripe...
if not exist "landingpage\stripe-integration.js" (
    echo ERRO: Integracao Stripe nao encontrada!
    pause
    exit /b 1
)

echo Integracao Stripe encontrada!
echo.

echo [2/4] Configuracoes necessarias:
echo.
echo 1. STRIPE DASHBOARD:
echo    - Acesse: https://dashboard.stripe.com/
echo    - Copie as chaves de PRODUCAO
echo    - Configure webhooks
echo.
echo 2. ATUALIZAR CHAVES:
echo    - Edite: landingpage/config.js
echo    - Troque chaves de teste por producao
echo.
echo 3. CONFIGURAR WEBHOOKS:
echo    - URL: https://api.promandato.com.br/webhook/stripe
echo    - Eventos: checkout.session.completed
echo.

echo [3/4] Testando configuracao atual...
echo Verificando arquivo de configuracao...

if exist "landingpage\config.js" (
    echo Config encontrado!
) else (
    echo AVISO: Config nao encontrado!
)

echo.
echo [4/4] Proximos passos manuais:
echo.
echo 1. Configure as chaves Stripe em producao
echo 2. Configure webhooks no Stripe Dashboard  
echo 3. Teste um pagamento
echo 4. Execute: scripts\3-deploy-backend.bat
echo.

pause
