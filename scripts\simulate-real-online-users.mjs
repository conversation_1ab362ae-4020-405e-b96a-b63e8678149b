import { initializeApp } from 'firebase/app';
import { getFirestore, collection, doc, setDoc, getDocs, deleteDoc } from 'firebase/firestore';

// Configuração do Firebase
const FIREBASE_CONFIG = {
  apiKey: "AIzaSyB04qDOcU5jdZAYy-yaYQRUrrYQ_l49vcY",
  authDomain: "promandato-9a4cf.firebaseapp.com",
  projectId: "promandato-9a4cf",
  storageBucket: "promandato-9a4cf.firebasestorage.app",
  messagingSenderId: "517140455601",
  appId: "1:517140455601:web:fa2eb0ec2f88b506594290",
  measurementId: "G-MXYM2GKJS5"
};

// Inicializa o Firebase
const app = initializeApp(FIREBASE_CONFIG);
const db = getFirestore(app);

// Função para obter usuários reais do Firestore
async function getRealUsers() {
  try {
    const usersCollection = collection(db, 'users');
    const usersSnapshot = await getDocs(usersCollection);
    
    const users = [];
    usersSnapshot.forEach((doc) => {
      const userData = doc.data();
      users.push({
        id: doc.id,
        name: userData.name,
        email: userData.email,
        role: userData.role,
        planId: userData.planId,
        department: userData.department,
        position: userData.position
      });
    });
    
    return users;
  } catch (error) {
    console.error('❌ Erro ao buscar usuários reais:', error);
    return [];
  }
}

// Funções auxiliares para gerar dados aleatórios
function getRandomStatus() {
  const statuses = ['online', 'away', 'busy'];
  return statuses[Math.floor(Math.random() * statuses.length)];
}

function getRandomDevice() {
  const devices = ['Desktop', 'Mobile', 'Tablet'];
  return devices[Math.floor(Math.random() * devices.length)];
}

function getRandomBrowser() {
  const browsers = ['Chrome', 'Firefox', 'Safari', 'Edge'];
  return browsers[Math.floor(Math.random() * browsers.length)];
}

function getRandomLocation() {
  const locations = [
    'São Paulo, SP',
    'Rio de Janeiro, RJ',
    'Belo Horizonte, MG',
    'Brasília, DF',
    'Salvador, BA',
    'Fortaleza, CE',
    'Recife, PE',
    'Porto Alegre, RS'
  ];
  return locations[Math.floor(Math.random() * locations.length)];
}

function getRandomPage() {
  const pages = [
    '/dashboard',
    '/demands',
    '/citizens',
    '/agenda',
    '/documents',
    '/reports',
    '/profile',
    '/settings'
  ];
  return pages[Math.floor(Math.random() * pages.length)];
}

function generateRandomIP() {
  return `192.168.1.${Math.floor(Math.random() * 255)}`;
}

// Função para simular usuários online baseados nos usuários reais
async function simulateRealOnlineUsers() {
  console.log('🚀 Iniciando simulação de usuários online baseados em dados reais...\n');

  try {
    // Buscar usuários reais
    const realUsers = await getRealUsers();
    console.log(`👥 Encontrados ${realUsers.length} usuários reais no Firestore`);

    if (realUsers.length === 0) {
      console.log('⚠️ Nenhum usuário real encontrado no Firestore.');
      console.log('💡 Execute primeiro: node sync-users-to-firestore.mjs');
      return;
    }

    // Limpar dados anteriores de usuários online
    console.log('🧹 Limpando dados anteriores de usuários online...');
    await clearOnlineUsers();

    // Simular alguns usuários como online (não todos)
    const onlineCount = Math.min(realUsers.length, Math.floor(realUsers.length * 0.7) + 1); // 70% dos usuários online
    const onlineUsers = realUsers.slice(0, onlineCount);

    console.log(`🔄 Simulando ${onlineUsers.length} usuários como online...`);

    for (const user of onlineUsers) {
      try {
        const now = new Date().toISOString();
        const sessionStartTime = new Date(Date.now() - Math.random() * 3600000).toISOString(); // Sessão iniciada há até 1 hora

        const onlineUserData = {
          id: user.id,
          name: user.name || 'Nome não informado',
          email: user.email || 'Email não informado',
          role: user.role || 'staff',
          status: getRandomStatus(),
          lastActivity: now,
          sessionStart: sessionStartTime,
          sessionDuration: 0, // Será calculado dinamicamente
          device: getRandomDevice(),
          browser: getRandomBrowser(),
          location: getRandomLocation(),
          currentPage: getRandomPage(),
          actionsToday: Math.floor(Math.random() * 50),
          ipAddress: generateRandomIP(),
          planId: user.planId || 'basic',
          department: user.department || 'Não informado',
          position: user.position || 'Não informado'
        };

        // Salvar no Firestore
        await setDoc(doc(db, 'online_users', user.id), onlineUserData);
        
        console.log(`✅ ${user.name} simulado como ${onlineUserData.status} (${onlineUserData.device})`);

      } catch (error) {
        console.error(`❌ Erro ao simular usuário ${user.name}:`, error.message);
      }
    }

    console.log('\n🎉 Simulação concluída!');
    console.log(`📊 Resumo:`);
    console.log(`   • Total de usuários reais: ${realUsers.length}`);
    console.log(`   • Usuários simulados como online: ${onlineUsers.length}`);
    console.log(`   • Taxa de usuários online: ${Math.round((onlineUsers.length / realUsers.length) * 100)}%`);

    // Mostrar detalhes dos usuários online
    console.log('\n👥 Usuários online simulados:');
    const onlineSnapshot = await getDocs(collection(db, 'online_users'));
    onlineSnapshot.forEach((doc) => {
      const userData = doc.data();
      console.log(`   • ${userData.name} (${userData.email}) - ${userData.status} - ${userData.device}`);
    });

    console.log('\n💡 Agora você pode testar a página "Clientes Online" com dados reais!');
    console.log('🔗 Acesse o dashboard backend: http://localhost:3000/#/online-clients');

  } catch (error) {
    console.error('❌ Erro na simulação:', error);
  }
}

// Função para limpar usuários online
async function clearOnlineUsers() {
  try {
    const onlineUsersSnapshot = await getDocs(collection(db, 'online_users'));
    const sessionsSnapshot = await getDocs(collection(db, 'user_sessions'));
    
    // Deletar usuários online
    const deletePromises = [];
    onlineUsersSnapshot.forEach((doc) => {
      deletePromises.push(deleteDoc(doc.ref));
    });
    
    // Deletar sessões
    sessionsSnapshot.forEach((doc) => {
      deletePromises.push(deleteDoc(doc.ref));
    });
    
    await Promise.all(deletePromises);
    console.log('✅ Dados anteriores de usuários online limpos');
  } catch (error) {
    console.error('❌ Erro ao limpar dados:', error);
  }
}

// Função para limpar todos os dados online (útil para reset)
async function clearAllOnlineData() {
  console.log('🧹 Limpando todos os dados de usuários online...');
  await clearOnlineUsers();
  console.log('✅ Todos os dados online limpos!');
}

// Verificar argumentos da linha de comando
const args = process.argv.slice(2);

if (args.includes('--clear')) {
  clearAllOnlineData().then(() => process.exit(0));
} else {
  simulateRealOnlineUsers().then(() => process.exit(0));
}
