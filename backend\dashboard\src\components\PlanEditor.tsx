import React, { useState, useEffect } from 'react';
import { Plan } from '../types/plans';

interface PlanEditorProps {
  plan: Plan;
  onSave: (plan: Plan) => void;
  onCancel: () => void;
}

const PlanEditor: React.FC<PlanEditorProps> = ({ plan, onSave, onCancel }) => {
  const [editedPlan, setEditedPlan] = useState<Plan>({ ...plan });
  const [activeTab, setActiveTab] = useState<'basic' | 'pricing' | 'features' | 'ai'>('basic');

  useEffect(() => {
    setEditedPlan({ ...plan });
  }, [plan]);

  const handleInputChange = (field: string, value: any) => {
    setEditedPlan(prev => ({
      ...prev,
      [field]: value,
      updatedAt: new Date().toISOString()
    }));
  };

  const handlePriceChange = (type: 'monthly' | 'yearly', value: number) => {
    setEditedPlan(prev => ({
      ...prev,
      price: {
        ...prev.price,
        [type]: value
      },
      updatedAt: new Date().toISOString()
    }));
  };

  const handleFeatureChange = (feature: string, value: any) => {
    setEditedPlan(prev => ({
      ...prev,
      features: {
        ...prev.features,
        [feature]: value
      },
      updatedAt: new Date().toISOString()
    }));
  };

  const handleAIFeatureToggle = (featureId: string, enabled: boolean) => {
    setEditedPlan(prev => ({
      ...prev,
      features: {
        ...prev.features,
        aiFeatures: prev.features.aiFeatures.map(feature =>
          feature.id === featureId ? { ...feature, enabled } : feature
        )
      },
      updatedAt: new Date().toISOString()
    }));
  };

  const handleSave = () => {
    onSave(editedPlan);
  };

  const tabs = [
    { id: 'basic', label: 'Básico', icon: '📝' },
    { id: 'pricing', label: 'Preços', icon: '💰' },
    { id: 'features', label: 'Recursos', icon: '⚙️' },
    { id: 'ai', label: 'IA', icon: '🤖' }
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold text-gray-900">
              Editar Plano: {editedPlan.name}
            </h2>
            <button
              type="button"
              onClick={onCancel}
              className="text-gray-400 hover:text-gray-600"
              aria-label="Fechar editor"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                type="button"
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.icon} {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {/* Basic Tab */}
          {activeTab === 'basic' && (
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nome do Plano
                </label>
                <input
                  type="text"
                  value={editedPlan.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  title="Nome do plano"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Descrição
                </label>
                <textarea
                  value={editedPlan.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  rows={3}
                  title="Descrição do plano"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Badge (opcional)
                </label>
                <input
                  type="text"
                  value={editedPlan.badge || ''}
                  onChange={(e) => handleInputChange('badge', e.target.value)}
                  placeholder="Ex: IA Incluída"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="flex items-center space-x-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={editedPlan.popular || false}
                    onChange={(e) => handleInputChange('popular', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Plano Popular</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={editedPlan.enabled}
                    onChange={(e) => handleInputChange('enabled', e.target.checked)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-sm text-gray-700">Plano Ativo</span>
                </label>
              </div>
            </div>
          )}

          {/* Pricing Tab */}
          {activeTab === 'pricing' && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Preço Mensal (R$)
                  </label>
                  <input
                    type="number"
                    value={editedPlan.price.monthly}
                    onChange={(e) => handlePriceChange('monthly', parseFloat(e.target.value))}
                    min="0"
                    step="0.01"
                    title="Preço mensal em reais"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Preço Anual (R$)
                  </label>
                  <input
                    type="number"
                    value={editedPlan.price.yearly}
                    onChange={(e) => handlePriceChange('yearly', parseFloat(e.target.value))}
                    min="0"
                    step="0.01"
                    title="Preço anual em reais"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div className="bg-blue-50 p-4 rounded-md">
                <h4 className="text-sm font-medium text-blue-900 mb-2">Cálculo de Desconto</h4>
                <p className="text-sm text-blue-700">
                  Preço mensal total: R$ {(editedPlan.price.monthly * 12).toFixed(2)}
                </p>
                <p className="text-sm text-blue-700">
                  Preço anual: R$ {editedPlan.price.yearly.toFixed(2)}
                </p>
                <p className="text-sm text-blue-700 font-medium">
                  Desconto anual: {Math.round(((editedPlan.price.monthly * 12 - editedPlan.price.yearly) / (editedPlan.price.monthly * 12)) * 100)}%
                </p>
              </div>
            </div>
          )}

          {/* Features Tab */}
          {activeTab === 'features' && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Máximo de Usuários
                  </label>
                  <input
                    type="number"
                    value={editedPlan.features.maxUsers === -1 ? '' : editedPlan.features.maxUsers}
                    onChange={(e) => handleFeatureChange('maxUsers', e.target.value === '' ? -1 : parseInt(e.target.value))}
                    placeholder="Deixe vazio para ilimitado"
                    title="Número máximo de usuários permitidos"
                    min="1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Máximo de Demandas
                  </label>
                  <input
                    type="number"
                    value={editedPlan.features.maxDemands === -1 ? '' : editedPlan.features.maxDemands}
                    onChange={(e) => handleFeatureChange('maxDemands', e.target.value === '' ? -1 : parseInt(e.target.value))}
                    placeholder="Deixe vazio para ilimitado"
                    min="1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Máximo de Cidadãos
                  </label>
                  <input
                    type="number"
                    value={editedPlan.features.maxCitizens === -1 ? '' : editedPlan.features.maxCitizens}
                    onChange={(e) => handleFeatureChange('maxCitizens', e.target.value === '' ? -1 : parseInt(e.target.value))}
                    placeholder="Deixe vazio para ilimitado"
                    min="1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Armazenamento (GB)
                  </label>
                  <input
                    type="number"
                    value={editedPlan.features.storageGB}
                    onChange={(e) => handleFeatureChange('storageGB', parseInt(e.target.value))}
                    title="Armazenamento em gigabytes"
                    min="1"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="text-sm font-medium text-gray-900">Recursos Disponíveis</h4>
                <div className="grid grid-cols-2 gap-4">
                  {[
                    { key: 'bulkMessages', label: 'Mensagens em Massa' },
                    { key: 'socialMediaIntegration', label: 'Integração Redes Sociais' },
                    { key: 'smsNotifications', label: 'Notificações SMS' },
                    { key: 'advancedReports', label: 'Relatórios Avançados' },
                    { key: 'customReports', label: 'Relatórios Personalizados' },
                    { key: 'dataExport', label: 'Exportação de Dados' },
                    { key: 'apiAccess', label: 'Acesso à API' },
                    { key: 'webhooks', label: 'Webhooks' }
                  ].map((feature) => (
                    <label key={feature.key} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={editedPlan.features[feature.key as keyof typeof editedPlan.features] as boolean}
                        onChange={(e) => handleFeatureChange(feature.key, e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="ml-2 text-sm text-gray-700">{feature.label}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nível de Suporte
                </label>
                <select
                  value={editedPlan.features.supportLevel}
                  onChange={(e) => handleFeatureChange('supportLevel', e.target.value)}
                  title="Nível de suporte oferecido"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="basic">Básico</option>
                  <option value="priority">Prioritário</option>
                  <option value="dedicated">Dedicado</option>
                </select>
              </div>
            </div>
          )}

          {/* AI Tab */}
          {activeTab === 'ai' && (
            <div className="space-y-6">
              <div className="bg-purple-50 p-4 rounded-md">
                <h4 className="text-sm font-medium text-purple-900 mb-2">
                  Recursos de Inteligência Artificial
                </h4>
                <p className="text-sm text-purple-700">
                  Configure quais recursos de IA estarão disponíveis neste plano.
                </p>
              </div>

              <div className="space-y-4">
                {editedPlan.features.aiFeatures.map((aiFeature) => (
                  <div key={aiFeature.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            checked={aiFeature.enabled}
                            onChange={(e) => handleAIFeatureToggle(aiFeature.id, e.target.checked)}
                            title={`Ativar/desativar ${aiFeature.name}`}
                            className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                          />
                          <h5 className="ml-3 text-sm font-medium text-gray-900">
                            {aiFeature.name}
                          </h5>
                        </div>
                        <p className="mt-1 text-sm text-gray-600 ml-6">
                          {aiFeature.description}
                        </p>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800 ml-6 mt-2">
                          {aiFeature.category}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {editedPlan.features.aiFeatures.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <p>Nenhum recurso de IA configurado para este plano.</p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            Cancelar
          </button>
          <button
            type="button"
            onClick={handleSave}
            className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
          >
            Salvar Alterações
          </button>
        </div>
      </div>
    </div>
  );
};

export default PlanEditor;