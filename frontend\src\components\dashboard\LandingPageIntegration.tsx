import React, { useState, useEffect } from 'react';
import { 
  Globe, 
  Users, 
  TrendingUp, 
  MousePointer, 
  Mail, 
  Phone,
  Calendar,
  ExternalLink,
  Download,
  Refresh<PERSON>w,
  Filter,
  Bar<PERSON>hart3,
  PieChart,
  Activity
} from 'lucide-react';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';

interface LandingPageMetrics {
  totalViews: number;
  uniqueVisitors: number;
  conversionRate: number;
  bounceRate: number;
  averageTimeOnPage: number;
  demoRequests: number;
  trialSignups: number;
  contactFormSubmissions: number;
  topTrafficSources: Array<{
    source: string;
    visitors: number;
    percentage: number;
  }>;
  deviceBreakdown: {
    desktop: number;
    mobile: number;
    tablet: number;
  };
  geographicData: Array<{
    country: string;
    visitors: number;
    percentage: number;
  }>;
}

interface LeadFromLanding {
  id: string;
  name: string;
  email: string;
  phone?: string;
  organization?: string;
  planInterest: 'basic' | 'standard' | 'professional';
  source: 'demo_request' | 'trial_signup' | 'contact_form' | 'pricing_click';
  timestamp: string;
  userAgent: string;
  ipAddress: string;
  referrer?: string;
  utmSource?: string;
  utmMedium?: string;
  utmCampaign?: string;
}

interface LandingPageIntegrationProps {
  onLeadUpdate?: (leads: LeadFromLanding[]) => void;
}

const LandingPageIntegration: React.FC<LandingPageIntegrationProps> = ({ onLeadUpdate }) => {
  const [metrics, setMetrics] = useState<LandingPageMetrics>({
    totalViews: 15420,
    uniqueVisitors: 8932,
    conversionRate: 3.2,
    bounceRate: 42.1,
    averageTimeOnPage: 185, // segundos
    demoRequests: 89,
    trialSignups: 34,
    contactFormSubmissions: 156,
    topTrafficSources: [
      { source: 'Google Orgânico', visitors: 4521, percentage: 50.6 },
      { source: 'Google Ads', visitors: 2134, percentage: 23.9 },
      { source: 'LinkedIn', visitors: 1245, percentage: 13.9 },
      { source: 'Direto', visitors: 892, percentage: 10.0 },
      { source: 'Facebook', visitors: 140, percentage: 1.6 }
    ],
    deviceBreakdown: {
      desktop: 65.2,
      mobile: 28.4,
      tablet: 6.4
    },
    geographicData: [
      { country: 'Brasil', visitors: 7845, percentage: 87.8 },
      { country: 'Portugal', visitors: 567, percentage: 6.3 },
      { country: 'Estados Unidos', visitors: 234, percentage: 2.6 },
      { country: 'Outros', visitors: 286, percentage: 3.3 }
    ]
  });

  const [recentLeads, setRecentLeads] = useState<LeadFromLanding[]>([
    {
      id: '1',
      name: 'Ana Costa',
      email: '<EMAIL>',
      phone: '(11) 99999-9999',
      organization: 'Prefeitura de São Paulo',
      planInterest: 'professional',
      source: 'demo_request',
      timestamp: '2024-01-15T14:30:00Z',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      ipAddress: '*************',
      referrer: 'https://google.com',
      utmSource: 'google',
      utmMedium: 'cpc',
      utmCampaign: 'gestao-politica'
    },
    {
      id: '2',
      name: 'Carlos Silva',
      email: '<EMAIL>',
      organization: 'Câmara Municipal do Rio',
      planInterest: 'standard',
      source: 'trial_signup',
      timestamp: '2024-01-15T13:15:00Z',
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      ipAddress: '*************',
      referrer: 'https://linkedin.com'
    }
  ]);

  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(new Date());

  // Função para atualizar métricas
  const refreshMetrics = async () => {
    setIsLoading(true);
    try {
      // Simular chamada à API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Aqui você faria a chamada real para a API
      // const response = await fetch('/api/landing-page/metrics');
      // const data = await response.json();
      // setMetrics(data);
      
      setLastUpdate(new Date());
    } catch (error) {
      console.error('Erro ao atualizar métricas:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Função para formatar tempo
  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  // Função para formatar porcentagem
  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  useEffect(() => {
    // Atualizar leads no componente pai
    if (onLeadUpdate) {
      onLeadUpdate(recentLeads);
    }
  }, [recentLeads, onLeadUpdate]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <Globe className="h-6 w-6 mr-2 text-blue-600" />
            Integração Landing Page
          </h2>
          <p className="text-gray-600">
            Última atualização: {lastUpdate.toLocaleString('pt-BR')}
          </p>
        </div>
        <div className="flex space-x-3">
          <Button 
            variant="outline" 
            onClick={refreshMetrics}
            disabled={isLoading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            Atualizar
          </Button>
          <Button variant="outline">
            <ExternalLink className="h-4 w-4 mr-2" />
            Ver Landing Page
          </Button>
        </div>
      </div>

      {/* Métricas Principais */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Visualizações</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.totalViews.toLocaleString()}</p>
              <p className="text-sm text-green-600">+12% vs mês anterior</p>
            </div>
            <div className="p-3 bg-blue-100 rounded-full">
              <Activity className="h-6 w-6 text-blue-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Visitantes Únicos</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.uniqueVisitors.toLocaleString()}</p>
              <p className="text-sm text-green-600">+8% vs mês anterior</p>
            </div>
            <div className="p-3 bg-green-100 rounded-full">
              <Users className="h-6 w-6 text-green-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Taxa de Conversão</p>
              <p className="text-2xl font-bold text-gray-900">{formatPercentage(metrics.conversionRate)}</p>
              <p className="text-sm text-green-600">+0.5% vs mês anterior</p>
            </div>
            <div className="p-3 bg-yellow-100 rounded-full">
              <TrendingUp className="h-6 w-6 text-yellow-600" />
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Tempo Médio</p>
              <p className="text-2xl font-bold text-gray-900">{formatTime(metrics.averageTimeOnPage)}</p>
              <p className="text-sm text-green-600">+15s vs mês anterior</p>
            </div>
            <div className="p-3 bg-purple-100 rounded-full">
              <MousePointer className="h-6 w-6 text-purple-600" />
            </div>
          </div>
        </Card>
      </div>

      {/* Conversões */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Calendar className="h-5 w-5 mr-2 text-blue-600" />
            Solicitações de Demo
          </h3>
          <div className="text-center">
            <p className="text-3xl font-bold text-blue-600">{metrics.demoRequests}</p>
            <p className="text-sm text-gray-600">Este mês</p>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Users className="h-5 w-5 mr-2 text-green-600" />
            Cadastros Trial
          </h3>
          <div className="text-center">
            <p className="text-3xl font-bold text-green-600">{metrics.trialSignups}</p>
            <p className="text-sm text-gray-600">Este mês</p>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Mail className="h-5 w-5 mr-2 text-purple-600" />
            Formulários de Contato
          </h3>
          <div className="text-center">
            <p className="text-3xl font-bold text-purple-600">{metrics.contactFormSubmissions}</p>
            <p className="text-sm text-gray-600">Este mês</p>
          </div>
        </Card>
      </div>

      {/* Fontes de Tráfego */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Principais Fontes de Tráfego
          </h3>
          <div className="space-y-4">
            {metrics.topTrafficSources.map((source, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm font-medium text-gray-900">{source.source}</span>
                    <span className="text-sm text-gray-600">{source.visitors.toLocaleString()}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${source.percentage}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <PieChart className="h-5 w-5 mr-2" />
            Dispositivos
          </h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-900">Desktop</span>
              <span className="text-sm text-gray-600">{formatPercentage(metrics.deviceBreakdown.desktop)}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full" 
                style={{ width: `${metrics.deviceBreakdown.desktop}%` }}
              ></div>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-900">Mobile</span>
              <span className="text-sm text-gray-600">{formatPercentage(metrics.deviceBreakdown.mobile)}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-green-600 h-2 rounded-full" 
                style={{ width: `${metrics.deviceBreakdown.mobile}%` }}
              ></div>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-900">Tablet</span>
              <span className="text-sm text-gray-600">{formatPercentage(metrics.deviceBreakdown.tablet)}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-yellow-600 h-2 rounded-full" 
                style={{ width: `${metrics.deviceBreakdown.tablet}%` }}
              ></div>
            </div>
          </div>
        </Card>
      </div>

      {/* Leads Recentes */}
      <Card className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <Users className="h-5 w-5 mr-2" />
            Leads Recentes da Landing Page
          </h3>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Lead
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Organização
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Plano de Interesse
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Fonte
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Data/Hora
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  UTM
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {recentLeads.map((lead) => (
                <tr key={lead.id} className="hover:bg-gray-50">
                  <td className="px-4 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{lead.name}</div>
                      <div className="text-sm text-gray-500 flex items-center">
                        <Mail className="h-3 w-3 mr-1" />
                        {lead.email}
                      </div>
                      {lead.phone && (
                        <div className="text-sm text-gray-500 flex items-center">
                          <Phone className="h-3 w-3 mr-1" />
                          {lead.phone}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    {lead.organization || '-'}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      lead.planInterest === 'professional' ? 'bg-purple-100 text-purple-800' :
                      lead.planInterest === 'standard' ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {lead.planInterest}
                    </span>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      {lead.source.replace('_', ' ')}
                    </span>
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(lead.timestamp).toLocaleString('pt-BR')}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                    {lead.utmSource && (
                      <div className="text-xs">
                        <div>Fonte: {lead.utmSource}</div>
                        {lead.utmMedium && <div>Meio: {lead.utmMedium}</div>}
                        {lead.utmCampaign && <div>Campanha: {lead.utmCampaign}</div>}
                      </div>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </Card>
    </div>
  );
};

export default LandingPageIntegration;
