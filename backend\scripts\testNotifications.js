import NotificationService from '../services/notificationService.js';
import User from '../models/User.js';
import Notification from '../models/Notification.js';

/**
 * Script para testar o sistema de notificações
 * Execute com: node scripts/testNotifications.js
 */

async function testNotifications() {
  try {
    console.log('🧪 Iniciando testes do sistema de notificações...\n');

    // 1. Testar criação de usuário de teste
    console.log('1️⃣ Criando usuário de teste...');
    let testUser;
    try {
      testUser = await User.create({
        email: '<EMAIL>',
        password: 'teste123',
        name: '<PERSON><PERSON><PERSON><PERSON> Teste',
        role: 'USER'
      });
      console.log('✅ Usuário de teste criado:', testUser.name);
    } catch (error) {
      // Se o usuário já existe, buscar ele
      testUser = await User.findByEmail('<EMAIL>');
      console.log('ℹ️ Usuário de teste já existe:', testUser.name);
    }

    // 2. Testar notificação simples
    console.log('\n2️⃣ Testando notificação simples...');
    const simpleNotification = await NotificationService.notifyUser(
      testUser.id,
      'Teste de Notificação',
      'Esta é uma notificação de teste do sistema',
      {
        type: 'info',
        category: 'general',
        actionUrl: '/test',
        actionLabel: 'Ver teste'
      }
    );
    console.log('✅ Notificação simples criada:', simpleNotification.id);

    // 3. Testar notificação de nova demanda
    console.log('\n3️⃣ Testando notificação de nova demanda...');
    const demandData = {
      id: 'test-demand-1',
      citizenName: 'João Silva',
      category: 'infraestrutura',
      priority: 'alta',
      description: 'Buraco na rua principal'
    };
    
    const demandNotifications = await NotificationService.notifyNewDemand(demandData, testUser.id);
    console.log('✅ Notificação de demanda criada para usuário específico');

    // 4. Testar notificação de demanda resolvida
    console.log('\n4️⃣ Testando notificação de demanda resolvida...');
    const resolvedDemandData = {
      id: 'test-demand-2',
      category: 'limpeza urbana'
    };
    
    await NotificationService.notifyDemandResolved(resolvedDemandData, testUser.id);
    console.log('✅ Notificação de demanda resolvida criada');

    // 5. Testar notificação de prazo vencendo
    console.log('\n5️⃣ Testando notificação de prazo vencendo...');
    const deadlineData = {
      id: 'test-demand-3',
      deadline: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString()
    };
    
    await NotificationService.notifyDeadlineApproaching(deadlineData, 2);
    console.log('✅ Notificação de prazo vencendo criada');

    // 6. Testar notificação de evento próximo
    console.log('\n6️⃣ Testando notificação de evento próximo...');
    const eventData = {
      id: 'test-event-1',
      title: 'Reunião Pública',
      date: new Date(Date.now() + 12 * 60 * 60 * 1000).toISOString(),
      time: '14:00'
    };
    
    await NotificationService.notifyUpcomingEvent(eventData, 12);
    console.log('✅ Notificação de evento próximo criada');

    // 7. Testar notificação de atualização do sistema
    console.log('\n7️⃣ Testando notificação de atualização do sistema...');
    const updateData = {
      message: 'Sistema atualizado com novas funcionalidades de relatórios',
      version: '2.1.0',
      type: 'feature',
      actionUrl: '/reports',
      actionLabel: 'Ver relatórios'
    };
    
    await NotificationService.notifySystemUpdate(updateData);
    console.log('✅ Notificação de atualização do sistema criada');

    // 8. Testar busca de notificações
    console.log('\n8️⃣ Testando busca de notificações...');
    const userNotifications = await Notification.findByUserId(testUser.id);
    console.log(`✅ Encontradas ${userNotifications.length} notificações para o usuário`);

    // 9. Testar contagem de não lidas
    console.log('\n9️⃣ Testando contagem de não lidas...');
    const unreadCount = await Notification.getUnreadCountForUser(testUser.id);
    console.log(`✅ ${unreadCount} notificações não lidas encontradas`);

    // 10. Testar marcar como lida
    console.log('\n🔟 Testando marcar como lida...');
    if (userNotifications.length > 0) {
      const firstNotification = userNotifications[0];
      await firstNotification.markAsRead();
      console.log('✅ Primeira notificação marcada como lida');
    }

    // 11. Testar marcar todas como lidas
    console.log('\n1️⃣1️⃣ Testando marcar todas como lidas...');
    const updated = await Notification.markAllAsReadForUser(testUser.id);
    console.log(`✅ ${updated ? 'Todas' : 'Nenhuma'} notificações marcadas como lidas`);

    // 12. Testar limpeza de notificações expiradas
    console.log('\n1️⃣2️⃣ Testando limpeza de notificações expiradas...');
    const deletedCount = await Notification.cleanupExpired();
    console.log(`✅ ${deletedCount} notificações expiradas removidas`);

    // 13. Mostrar estatísticas finais
    console.log('\n📊 Estatísticas finais:');
    const finalNotifications = await Notification.findByUserId(testUser.id);
    const finalUnreadCount = await Notification.getUnreadCountForUser(testUser.id);
    
    console.log(`   Total de notificações: ${finalNotifications.length}`);
    console.log(`   Não lidas: ${finalUnreadCount}`);
    console.log(`   Lidas: ${finalNotifications.length - finalUnreadCount}`);

    // 14. Mostrar algumas notificações de exemplo
    console.log('\n📋 Últimas notificações:');
    finalNotifications.slice(0, 3).forEach((notification, index) => {
      console.log(`   ${index + 1}. ${notification.title} (${notification.type})`);
      console.log(`      ${notification.message}`);
      console.log(`      Status: ${notification.read ? 'Lida' : 'Não lida'}`);
      console.log('');
    });

    console.log('🎉 Todos os testes concluídos com sucesso!\n');

  } catch (error) {
    console.error('❌ Erro durante os testes:', error);
    process.exit(1);
  }
}

// Executar apenas se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  testNotifications()
    .then(() => {
      console.log('✨ Testes finalizados');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Erro fatal nos testes:', error);
      process.exit(1);
    });
}

export default testNotifications;
