import { useMemo } from 'react';
import {
  formatCurrency,
  formatPercentage,
  formatNumber,
  formatDate,
  formatDateRange,
  calculatePerformanceMetrics,
  calculateEngagementMetrics
} from './reportHelpers';

export const useReportHelpers = () => {
  return useMemo(() => ({
    formatCurrency,
    formatPercentage,
    formatNumber,
    formatDate,
    formatDateRange,
    calculatePerformanceMetrics,
    calculateEngagementMetrics
  }), []);
};
