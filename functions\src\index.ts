/**
 * Firebase Functions para ProMandato
 * Backend API e webhooks
 */

import {onRequest} from "firebase-functions/v2/https";
import * as logger from "firebase-functions/logger";

// Health check function
export const api = onRequest((request, response) => {
  logger.info("API Health Check", {structuredData: true});

  response.set("Access-Control-Allow-Origin", "*");
  response.set("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
  response.set("Access-Control-Allow-Headers", "Content-Type, Authorization");

  if (request.method === "OPTIONS") {
    response.status(200).send();
    return;
  }

  response.json({
    success: true,
    message: "ProMandato API funcionando",
    timestamp: new Date().toISOString(),
    version: "1.0.0",
  });
});
