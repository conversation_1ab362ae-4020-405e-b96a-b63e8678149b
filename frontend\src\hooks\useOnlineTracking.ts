import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useAuth } from './useAuth';
import { updateUserActivity, incrementUserActions } from '../services/onlineUsersService';

// Hook para rastrear atividade do usuário online
export const useOnlineTracking = () => {
  const { currentUser } = useAuth();
  const location = useLocation();

  // Atualizar página atual quando a rota muda
  useEffect(() => {
    if (currentUser) {
      updateUserActivity(currentUser.id, location.pathname);
    }
  }, [currentUser, location.pathname]);

  // Função para registrar uma ação do usuário
  const trackUserAction = () => {
    if (currentUser) {
      incrementUserActions(currentUser.id);
    }
  };

  // Rastrear cliques e interações
  useEffect(() => {
    if (!currentUser?.id) return;

    // Throttle para evitar muitas chamadas
    let lastActivityUpdate = 0;
    const THROTTLE_DELAY = 5000; // 5 segundos

    const handleUserInteraction = () => {
      const now = Date.now();
      if (now - lastActivityUpdate > THROTTLE_DELAY) {
        updateUserActivity(currentUser.id);
        lastActivityUpdate = now;
      }
    };

    const handleUserAction = () => {
      trackUserAction();
    };

    // Eventos que indicam atividade do usuário
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    
    // Eventos que indicam ações específicas do usuário
    const actionEvents = ['click', 'submit'];

    // Adicionar listeners para atividade
    activityEvents.forEach(event => {
      document.addEventListener(event, handleUserInteraction, { passive: true });
    });

    // Adicionar listeners para ações
    actionEvents.forEach(event => {
      document.addEventListener(event, handleUserAction, { passive: true });
    });

    // Cleanup
    return () => {
      activityEvents.forEach(event => {
        document.removeEventListener(event, handleUserInteraction);
      });
      
      actionEvents.forEach(event => {
        document.removeEventListener(event, handleUserAction);
      });
    };
  }, [currentUser]);

  return {
    trackUserAction
  };
};
