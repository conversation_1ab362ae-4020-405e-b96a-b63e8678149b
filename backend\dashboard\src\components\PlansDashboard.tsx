import React, { useState, useEffect } from 'react';
import { Plan, PlanType } from '../types/plans';
import planService from '../services/planService';
import PlanCard from './PlanCard';
import PlanEditor from './PlanEditor';
import BulkActions from './BulkActions';

const PlansDashboard: React.FC = () => {
  const [plans, setPlans] = useState<Plan[]>([]);
  const [selectedPlan, setSelectedPlan] = useState<Plan | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showEditor, setShowEditor] = useState(false);

  useEffect(() => {
    loadPlans();
  }, []);

  const loadPlans = async () => {
    try {
      setLoading(true);
      setError(null);
      const plansData = await planService.getAllPlans();
      setPlans(plansData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao carregar planos');
    } finally {
      setLoading(false);
    }
  };

  const handlePlanUpdate = async (updatedPlan: Plan) => {
    try {
      const updated = await planService.updatePlan(updatedPlan.id, updatedPlan);
      setPlans(plans.map(p => p.id === updated.id ? updated : p));
      setSelectedPlan(updated);
      setShowEditor(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao atualizar plano');
    }
  };

  const handlePlanToggle = async (planId: PlanType, enabled: boolean) => {
    try {
      const updated = await planService.togglePlanStatus(planId, enabled);
      setPlans(plans.map(p => p.id === updated.id ? updated : p));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao alterar status do plano');
    }
  };

  const handleBulkUpdate = async (updates: any[]) => {
    try {
      const updatedPlans = await planService.bulkUpdatePlans(updates);
      setPlans(updatedPlans);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao atualizar planos em massa');
    }
  };

  const handleResetPlans = async () => {
    if (window.confirm('Tem certeza que deseja resetar todos os planos para os valores padrão?')) {
      try {
        const resetPlans = await planService.resetPlansToDefault();
        setPlans(resetPlans);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Erro ao resetar planos');
      }
    }
  };

  const handleExportConfig = async () => {
    try {
      const config = await planService.exportPlansConfig();
      const blob = new Blob([config], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `plans-config-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro ao exportar configuração');
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(price);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 dark:border-blue-400"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Dashboard de Planos - Promandato
              </h1>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Gerencie os planos e preços da plataforma
              </p>
            </div>
            <div className="flex space-x-3">
              <button
                type="button"
                onClick={handleExportConfig}
                className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
              >
                📥 Exportar Config
              </button>
              <button
                type="button"
                onClick={handleResetPlans}
                className="inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md shadow-sm text-sm font-medium text-red-700 dark:text-red-400 bg-white dark:bg-gray-700 hover:bg-red-50 dark:hover:bg-red-900/20"
              >
                🔄 Resetar Planos
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Error Alert */}
        {error && (
          <div className="mb-6 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400 dark:text-red-300" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800 dark:text-red-200">Erro</h3>
                <div className="mt-2 text-sm text-red-700 dark:text-red-300">
                  <p>{error}</p>
                </div>
                <div className="mt-4">
                  <button
                    type="button"
                    onClick={() => setError(null)}
                    className="text-sm font-medium text-red-800 dark:text-red-200 hover:text-red-600 dark:hover:text-red-100"
                  >
                    Fechar
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Bulk Actions */}
        <BulkActions 
          plans={plans}
          onBulkUpdate={handleBulkUpdate}
          onReload={loadPlans}
        />

        {/* Plans Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {plans.map((plan) => (
            <PlanCard
              key={plan.id}
              plan={plan}
              onEdit={(plan) => {
                setSelectedPlan(plan);
                setShowEditor(true);
              }}
              onToggle={handlePlanToggle}
              formatPrice={formatPrice}
            />
          ))}
        </div>

        {/* Statistics */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Estatísticas dos Planos</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
              <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {plans.filter(p => p.enabled).length}
              </div>
              <div className="text-sm text-blue-600 dark:text-blue-400">Planos Ativos</div>
            </div>
            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
              <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                {formatPrice(Math.min(...plans.map(p => p.price.monthly)))}
              </div>
              <div className="text-sm text-green-600 dark:text-green-400">Menor Preço Mensal</div>
            </div>
            <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
              <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                {formatPrice(Math.max(...plans.map(p => p.price.monthly)))}
              </div>
              <div className="text-sm text-purple-600 dark:text-purple-400">Maior Preço Mensal</div>
            </div>
          </div>
        </div>
      </main>

      {/* Plan Editor Modal */}
      {showEditor && selectedPlan && (
        <PlanEditor
          plan={selectedPlan}
          onSave={handlePlanUpdate}
          onCancel={() => {
            setShowEditor(false);
            setSelectedPlan(null);
          }}
        />
      )}
    </div>
  );
};

export default PlansDashboard;