import fs from 'fs-extra';
import User from './models/User.js';

console.log('🚀 Criando usuários baseados no prices.csv...');

// Dados dos seus planos do CSV
const plansData = [
    {
        priceId: 'price_1RY5DHClUIoqY19kkU0D8pmf',
        productName: 'Basico',
        amount: 169.90,
        planType: 'basic'
    },
    {
        priceId: 'price_1RY5FSClUIoqY19kh9kieCaY', 
        productName: 'Plano Padrão',
        amount: 259.90,
        planType: 'standard'
    },
    {
        priceId: 'price_1RY5GJClUIoqY19k1bCNt4lG',
        productName: 'Plano Profissional', 
        amount: 599.90,
        planType: 'professional'
    }
];

// Função para gerar senha temporária
function generateTempPassword() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let password = '';
    for (let i = 0; i < 12; i++) {
        password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
}

async function createUsersFromPlans() {
    console.log(`📊 Criando usuários para ${plansData.length} planos...\n`);
    
    let created = 0;
    let errors = 0;
    const createdUsers = [];
    
    for (const plan of plansData) {
        try {
            const tempPassword = generateTempPassword();
            const timestamp = Date.now();
            const email = `demo.${plan.planType}@promandato.com.br`;
            
            console.log(`📝 Criando usuário para: ${plan.productName}`);
            console.log(`   Email: ${email}`);
            console.log(`   Plano: ${plan.planType}`);
            console.log(`   Preço: R$ ${plan.amount}`);
            console.log(`   Price ID: ${plan.priceId}`);
            
            // Verificar se usuário já existe
            const existingUser = await User.findByEmail(email);
            if (existingUser) {
                console.log(`⚠️  Usuário já existe: ${email}`);
                continue;
            }
            
            // Criar usuário
            const user = await User.create({
                email: email,
                name: `Demo ${plan.productName}`,
                password: tempPassword,
                role: 'ADMIN',
                planId: plan.planType,
                isActive: true,
                emailVerified: false,
                stripeCustomerId: `cus_demo_${timestamp}`,
                stripePriceId: plan.priceId,
                profile: {
                    phone: '+5511999999999',
                    country: 'BR',
                    company: `Empresa Demo ${plan.productName}`
                }
            });
            
            console.log(`✅ Usuário criado: ${user.email}`);
            console.log(`🔑 Senha temporária: ${tempPassword}\n`);
            
            createdUsers.push({
                email: user.email,
                password: tempPassword,
                plan: plan.planType,
                planName: plan.productName,
                amount: plan.amount
            });
            
            created++;
            
        } catch (error) {
            console.error(`❌ Erro ao criar usuário para ${plan.productName}:`, error.message);
            errors++;
        }
    }
    
    // Salvar relatório
    const reportPath = './data/created-users-report.json';
    const report = {
        timestamp: new Date().toISOString(),
        stats: { created, errors, total: plansData.length },
        users: createdUsers
    };
    
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
    
    console.log('📊 RESUMO DA CRIAÇÃO:');
    console.log(`✅ Usuários criados: ${created}`);
    console.log(`❌ Erros: ${errors}`);
    console.log(`📄 Relatório salvo em: ${reportPath}`);
    
    if (created > 0) {
        console.log('\n🎉 USUÁRIOS CRIADOS COM SUCESSO!');
        console.log('\n👥 CREDENCIAIS DE ACESSO:');
        console.log('=' .repeat(60));
        
        createdUsers.forEach((user, index) => {
            console.log(`\n${index + 1}. ${user.planName} (${user.plan.toUpperCase()})`);
            console.log(`   Email: ${user.email}`);
            console.log(`   Senha: ${user.password}`);
            console.log(`   Valor: R$ ${user.amount}`);
        });
        
        console.log('\n📋 PRÓXIMOS PASSOS:');
        console.log('1. Acesse: http://localhost:5174');
        console.log('2. Faça login com qualquer uma das credenciais acima');
        console.log('3. Altere a senha no primeiro acesso');
        console.log('4. Explore o sistema baseado no plano escolhido');
        
        console.log('\n🌐 URLs IMPORTANTES:');
        console.log('• Sistema Principal: http://localhost:5174');
        console.log('• Landing Page: http://localhost:5174/landingpage/');
        console.log('• Dashboard Admin: http://localhost:3002');
    }
    
    return { created, errors, users: createdUsers };
}

// Executar criação
createUsersFromPlans()
    .then((result) => {
        console.log('\n✅ Processo finalizado!');
        process.exit(0);
    })
    .catch((error) => {
        console.error('\n❌ Erro na criação:', error);
        process.exit(1);
    });
