import React from 'react';
import { usePlan } from '../../contexts/PlanContext';
import { PlanType } from '../../types/plans';

interface PlanLimitWarningProps {
  resource: string;
  action: string;
  className?: string;
}

export const PlanLimitWarning: React.FC<PlanLimitWarningProps> = ({
  resource,
  action,
  className = ''
}) => {
  const { hasReachedLimit, getResourceUsage, canUseFeature, upgradePlan, planType } = usePlan();
  
  const isLimited = hasReachedLimit(resource);
  const usage = getResourceUsage(resource);
  
  if (!isLimited && canUseFeature(resource)) {
    return null;
  }

  const getUpgradeRecommendation = () => {
    if (planType === PlanType.BASIC) {
      return {
        targetPlan: 'Padrão',
        targetPlanType: PlanType.STANDARD,
        benefits: ['Mais usuários', 'Mais demandas', 'Relatórios avançados']
      };
    } else if (planType === PlanType.STANDARD) {
      return {
        targetPlan: 'Profissional',
        targetPlanType: PlanType.PROFESSIONAL,
        benefits: ['Recursos ilimitados', 'IA incluída', 'Suporte dedicado']
      };
    }
    return null;
  };

  const recommendation = getUpgradeRecommendation();

  return (
    <div className={`bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 ${className}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
        </div>
        
        <div className="ml-3 flex-1">
          <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
            {isLimited ? 'Limite Atingido' : 'Recurso Não Disponível'}
          </h3>
          
          <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
            {isLimited ? (
              <p>
                Você atingiu o limite de {usage.limit} {resource} do seu plano atual. 
                Para {action}, faça upgrade para um plano superior.
              </p>
            ) : (
              <p>
                Este recurso não está disponível no seu plano atual. 
                Faça upgrade para acessar {action}.
              </p>
            )}
          </div>

          {usage.limit > 0 && (
            <div className="mt-3">
              <div className="flex justify-between text-xs text-yellow-700 dark:text-yellow-300 mb-1">
                <span>Uso atual</span>
                <span>{usage.used} / {usage.limit}</span>
              </div>
              <div className="w-full bg-yellow-200 dark:bg-yellow-800 rounded-full h-2">
                <div
                  className="bg-yellow-500 h-2 rounded-full"
                  style={{ width: `${Math.min((usage.used / usage.limit) * 100, 100)}%` }}
                />
              </div>
            </div>
          )}

          {recommendation && (
            <div className="mt-4 p-3 bg-white dark:bg-neutral-darker rounded border border-yellow-200 dark:border-yellow-800">
              <h4 className="text-sm font-medium text-neutral-dark dark:text-neutral-light mb-2">
                💡 Recomendação: Upgrade para o Plano {recommendation.targetPlan}
              </h4>
              <ul className="text-xs text-neutral-medium space-y-1 mb-3">
                {recommendation.benefits.map((benefit, index) => (
                  <li key={index} className="flex items-center">
                    <svg className="w-3 h-3 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    {benefit}
                  </li>
                ))}
              </ul>
              <button
                type="button"
                onClick={() => upgradePlan(recommendation.targetPlanType)}
                className="w-full bg-primary text-white py-2 px-4 rounded text-sm font-medium hover:bg-primary-dark transition-colors"
              >
                Fazer Upgrade Agora
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PlanLimitWarning;