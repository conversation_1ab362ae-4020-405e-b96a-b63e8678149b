import React, { useState, useEffect, useCallback } from 'react';
import { useAuth } from '../hooks/useAuth';
import { DashboardSummaryCard } from '../components/dashboard/DashboardSummaryCard';
import { ICONS, ROUTE_PATHS } from '../constants';
import { Card } from '../components/ui/Card';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';
import { getDemands, getCitizens, getAgendaEvents, getDocumentsMetadata } from '../services/firebaseService';
import { Demand, AgendaEvent, Document, UserRole } from '../types';

const formatDate = (dateString?: string, options?: Intl.DateTimeFormatOptions) => {
  if (!dateString) return '-';
  const defaultOptions: Intl.DateTimeFormatOptions = {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    ...(options || {}),
  };
  try {
    return new Date(dateString).toLocaleDateString('pt-BR', defaultOptions);
  } catch {
    return "Data inválida";
  }
};

const PRIORITY_COLORS = {
  high: 'text-red-600 dark:text-red-400',
  medium: 'text-yellow-600 dark:text-yellow-400',
  low: 'text-blue-600 dark:text-blue-400'
};

const STATUS_COLORS = {
  pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
  in_progress: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
  completed: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
  cancelled: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200'
};

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + " " + sizes[i];
};

const DashboardPage: React.FC = () => {
  const { currentUser, loading: authLoading } = useAuth();


  const [pendingDemandsCount, setPendingDemandsCount] = useState<number | null>(null);
  const [citizensCount, setCitizensCount] = useState<number | null>(null);
  const [upcomingEventsCount, setUpcomingEventsCount] = useState<number | null>(null);
  const [documentsCount, setDocumentsCount] = useState<number | null>(null);
  
  const [recentDemands, setRecentDemands] = useState<Demand[]>([]);
  const [nextAgendaEvents, setNextAgendaEvents] = useState<AgendaEvent[]>([]);
  const [recentDocuments, setRecentDocuments] = useState<Document[]>([]);

  const [isLoadingSummary, setIsLoadingSummary] = useState(true);
  const [isLoadingActivities, setIsLoadingActivities] = useState(true);
  const [isLoadingEvents, setIsLoadingEvents] = useState(true);
  const [isLoadingDocuments, setIsLoadingDocuments] = useState(true);
  const [error, setError] = useState<string | null>(null);

  console.log('DashboardPage - Estado inicial:', {
    authLoading,
    currentUser: currentUser ? 'Autenticado' : 'Não autenticado',
    pendingDemandsCount,
    citizensCount,
    upcomingEventsCount
  });

  const loadDashboardData = useCallback(async () => {
    if (!currentUser) {
      console.log('DashboardPage - Usuário não autenticado');
      return;
    }

    setIsLoadingSummary(true);
    setIsLoadingActivities(true);
    setIsLoadingEvents(true);
    setIsLoadingDocuments(true);
    setError(null);

    try {
      // Garantir que temos o masterId correto para as queries
      const masterId = currentUser.role === UserRole.MASTER ? currentUser.id : currentUser.masterId || null;
      
      console.log('DashboardPage - Usando masterId:', masterId);
      console.log('DashboardPage - Dados do usuário:', currentUser);

      // Carregue cada tipo de dado separadamente para identificar qual está falhando
      try {
        const demandsData = await getDemands();
        console.log('DashboardPage - Demandas carregadas:', demandsData?.length || 0);
        
        if (demandsData) {
          const pendingDemands = demandsData.filter(d => d.status === 'pending');
          console.log('DashboardPage - Demandas pendentes:', pendingDemands.length);
          setPendingDemandsCount(pendingDemands.length);
          
          // Ordenar demandas por data de criação (mais recentes primeiro)
          const sortedDemands = [...demandsData].sort((a, b) => 
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          ).slice(0, 5);
          setRecentDemands(sortedDemands);
        }
        setIsLoadingActivities(false);
      } catch (err) {
        console.error('Erro ao carregar demandas:', err);
        setIsLoadingActivities(false);
      }

      try {
        const citizensData = await getCitizens();
        console.log('DashboardPage - Cidadãos carregados:', citizensData?.length || 0);
        
        if (citizensData) {
          setCitizensCount(citizensData.length);
        }
      } catch (err) {
        console.error('Erro ao carregar cidadãos:', err);
      }

      try {
        const eventsData = await getAgendaEvents();
        console.log('DashboardPage - Eventos carregados:', eventsData?.length || 0);
        
        if (eventsData) {
          // Filtrar eventos futuros
          const now = new Date();
          const upcomingEvents = eventsData.filter(e => new Date(e.start) > now);
          setUpcomingEventsCount(upcomingEvents.length);
          
          // Ordenar eventos por data (mais próximos primeiro)
          const sortedEvents = [...upcomingEvents].sort((a, b) => 
            new Date(a.start).getTime() - new Date(b.start).getTime()
          ).slice(0, 5);
          setNextAgendaEvents(sortedEvents);
        }
        setIsLoadingEvents(false);
      } catch (err) {
        console.error('Erro ao carregar eventos:', err);
        setIsLoadingEvents(false);
      }

      try {
        const documentsData = await getDocumentsMetadata();
        console.log('DashboardPage - Documentos carregados:', documentsData?.length || 0);
        
        if (documentsData) {
          setDocumentsCount(documentsData.length);
          
          // Ordenar documentos por data de criação (mais recentes primeiro)
          const sortedDocuments = [...documentsData].sort((a, b) => 
            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
          ).slice(0, 5);
          setRecentDocuments(sortedDocuments);
        }
        setIsLoadingDocuments(false);
      } catch (err) {
        console.error('Erro ao carregar documentos:', err);
        setIsLoadingDocuments(false);
      }

      setIsLoadingSummary(false);
    } catch (error) {
      console.error('DashboardPage - Erro ao carregar dados:', error);
      setError('Erro ao carregar dados do dashboard');
      setIsLoadingSummary(false);
      setIsLoadingActivities(false);
      setIsLoadingEvents(false);
      setIsLoadingDocuments(false);
    }
  }, [currentUser]);

  useEffect(() => {
    if (!authLoading) {
      console.log('DashboardPage - Auth loading completado, iniciando carregamento');
      loadDashboardData();
    }
  }, [loadDashboardData, authLoading]);

  const summaryData = [
    {
      title: 'Demandas Ativas',
      value: isLoadingSummary ? <LoadingSpinner size="sm" /> : (pendingDemandsCount ?? '-'),
      icon: ICONS.DEMANDS,
      colorClass: 'bg-blue-600',
      actionLink: {text: "Gerenciar", href: `#${ROUTE_PATHS.DEMANDS}`}
    },
    {
      title: 'Cidadãos Atendidos',
      value: isLoadingSummary ? <LoadingSpinner size="sm" /> : (citizensCount ?? '-'),
      icon: ICONS.CITIZENS,
      colorClass: 'bg-emerald-600',
      actionLink: {text: "Ver Cidadãos", href: `#${ROUTE_PATHS.CITIZENS}`}
    },
    {
      title: 'Próximos Compromissos',
      value: isLoadingSummary ? <LoadingSpinner size="sm" /> : (upcomingEventsCount ?? '-'),
      icon: ICONS.AGENDA,
      colorClass: 'bg-indigo-600',
      actionLink: {text: "Ver Agenda", href: `#${ROUTE_PATHS.AGENDA}`}
    },
    {
      title: 'Documentos Públicos',
      value: isLoadingSummary ? <LoadingSpinner size="sm" /> : (documentsCount ?? '-'),
      icon: ICONS.DOCUMENTS,
      colorClass: 'bg-slate-600',
      actionLink: {text: "Acessar", href: `#${ROUTE_PATHS.DOCUMENTS}`}
    },
  ];
  
  if (authLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <LoadingSpinner size="lg" />
        <p className="ml-4 text-xl dark:text-neutral-light">Verificando autenticação...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-neutral-dark dark:text-neutral-light">
          Bem-vindo(a) de volta, {currentUser?.name}!
        </h1>
        <p className="text-sm text-gray-500 dark:text-neutral-DEFAULT">
          {new Date().toLocaleDateString('pt-BR', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
        </p>
      </div>

      {error && (
        <Card>
          <p className="text-red-600 dark:text-red-400 p-4 text-center bg-red-50 dark:bg-red-900/30 rounded-lg">
            {error}
          </p>
        </Card>
      )}
      
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        {summaryData.map((item) => (
          <DashboardSummaryCard
            key={item.title}
            title={item.title}
            value={item.value}
            icon={item.icon}
            colorClass={item.colorClass}
            actionLink={item.actionLink}
          />
        ))}
      </div>



      {/* Métricas de Gestão Política */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-emerald-100 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Demandas Resolvidas</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                {isLoadingSummary ? <LoadingSpinner size="sm" /> : '89'}
              </p>
              <p className="text-xs text-emerald-600">+12% este mês</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Engajamento Cidadão</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                {isLoadingSummary ? <LoadingSpinner size="sm" /> : '94%'}
              </p>
              <p className="text-xs text-blue-600">+5% esta semana</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-indigo-100 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Aprovação Popular</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                {isLoadingSummary ? <LoadingSpinner size="sm" /> : '87%'}
              </p>
              <p className="text-xs text-indigo-600">+3% este mês</p>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-slate-100 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Tempo Médio Resposta</p>
              <p className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                {isLoadingSummary ? <LoadingSpinner size="sm" /> : '2.1 dias'}
              </p>
              <p className="text-xs text-slate-600">-0.5 dias</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Alertas Prioritários */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Alertas Prioritários</h2>
          <span className="bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 text-xs font-medium px-2.5 py-0.5 rounded-full border border-red-200 dark:border-red-700">
            2 urgentes
          </span>
        </div>
        <div className="space-y-3">
          <div className="flex items-start space-x-3 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-red-900 dark:text-red-200">Demanda crítica de infraestrutura</p>
              <p className="text-sm text-red-700 dark:text-red-300">Problema no abastecimento de água - Bairro Centro</p>
              <p className="text-xs text-red-600 dark:text-red-400 mt-1">Prazo: Hoje às 18:00</p>
            </div>
          </div>

          <div className="flex items-start space-x-3 p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5 text-amber-600 dark:text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-amber-900 dark:text-amber-200">Audiência pública agendada</p>
              <p className="text-sm text-amber-700 dark:text-amber-300">Discussão sobre novo projeto de lei - Câmara Municipal</p>
              <p className="text-xs text-amber-600 dark:text-amber-400 mt-1">Amanhã às 14:00</p>
            </div>
          </div>

          <div className="flex items-start space-x-3 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <div className="flex-shrink-0">
              <svg className="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-blue-900 dark:text-blue-200">Novas mensagens dos cidadãos</p>
              <p className="text-sm text-blue-700 dark:text-blue-300">12 novas solicitações recebidas hoje</p>
              <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">Resposta média: 2.1 dias</p>
            </div>
          </div>
        </div>
      </Card>

      {/* Áreas de Atuação Política */}
      <Card className="p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Áreas de Atuação</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
            <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">28</p>
            <p className="text-sm text-gray-600 dark:text-gray-300">Infraestrutura</p>
            <p className="text-xs text-blue-600">+5 esta semana</p>
          </div>

          <div className="text-center">
            <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <svg className="w-8 h-8 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </div>
            <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">22</p>
            <p className="text-sm text-gray-600 dark:text-gray-300">Saúde</p>
            <p className="text-xs text-emerald-600">+3 esta semana</p>
          </div>

          <div className="text-center">
            <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <svg className="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
            </div>
            <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">19</p>
            <p className="text-sm text-gray-600 dark:text-gray-300">Educação</p>
            <p className="text-xs text-indigo-600">+2 esta semana</p>
          </div>

          <div className="text-center">
            <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-2">
              <svg className="w-8 h-8 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">16</p>
            <p className="text-sm text-gray-600 dark:text-gray-300">Assistência Social</p>
            <p className="text-xs text-slate-600">+4 esta semana</p>
          </div>
        </div>

        <div className="mt-6">
          <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-300 mb-2">
            <span>Meta mensal de atendimentos</span>
            <span>85 de 100 demandas</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div className="bg-gradient-to-r from-blue-600 to-indigo-600 h-2 rounded-full" style={{ width: '85%' }}></div>
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">15 demandas restantes para atingir a meta</p>
        </div>
      </Card>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <Card title="Atividades Políticas Recentes" className="lg:col-span-2">
          {isLoadingActivities ? (
            <div className="flex justify-center items-center h-40">
              <LoadingSpinner />
            </div>
          ) : recentDemands.length > 0 ? (
            <ul className="divide-y divide-gray-200 dark:divide-neutral-dark">
              {recentDemands.map(demand => (
                <li key={demand.id} className="py-3">
                  <div className="flex items-center space-x-3">
                    <div className={`flex-shrink-0 p-2 rounded-full text-white ${
                      demand.priority === 'high' ? 'bg-red-500' :
                      demand.priority === 'medium' ? 'bg-yellow-500' : 'bg-blue-500'
                    }`}>
                      {React.cloneElement(ICONS.DEMANDS, {className: "w-5 h-5"})}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-neutral-dark dark:text-neutral-light truncate">
                        {demand.title}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-neutral-DEFAULT">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mr-2 ${STATUS_COLORS[demand.status]}`}>
                          {demand.status.replace('_', ' ')}
                        </span>
                        <span className="inline-block">
                          Criada em: {formatDate(demand.createdAt)}
                        </span>
                        {demand.deadline && (
                          <span className="inline-block ml-2 text-xs">
                            Prazo: {formatDate(demand.deadline)}
                          </span>
                        )}
                      </p>
                    </div>
                    <div className={`flex-shrink-0 text-sm font-medium ${PRIORITY_COLORS[demand.priority]}`}>
                      {demand.priority.toUpperCase()}
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-gray-500 dark:text-neutral-DEFAULT text-center py-4">
              Nenhuma atividade recente.
            </p>
          )}
          <div className="mt-4 text-right">
            <a 
              href={`#${ROUTE_PATHS.DEMANDS}`} 
              className="text-sm font-medium text-primary hover:text-primary-dark dark:text-primary-light dark:hover:text-primary"
            >
              Ver todas demandas &rarr;
            </a>
          </div>
        </Card>

        <div className="space-y-6">
          <Card title="Próximos Compromissos">
            {isLoadingEvents ? (
              <div className="flex justify-center items-center h-40">
                <LoadingSpinner />
              </div>
            ) : nextAgendaEvents.length > 0 ? (
              <ul className="divide-y divide-gray-200 dark:divide-neutral-dark">
                {nextAgendaEvents.map(event => (
                  <li key={event.id} className="py-3">
                    <div>
                      <p className="text-sm font-medium text-neutral-dark dark:text-neutral-light">
                        {event.title}
                      </p>
                      <div className="mt-1">
                        <p className="text-sm text-gray-500 dark:text-neutral-DEFAULT flex items-center">
                          {React.cloneElement(ICONS.CALENDAR, {className: "w-4 h-4 mr-1"})}
                          {formatDate(event.start, { day: 'numeric', month: 'short', hour: '2-digit', minute: '2-digit' })}
                        </p>
                        {event.location && (
                          <p className="text-sm text-gray-500 dark:text-neutral-DEFAULT flex items-center mt-1">
                            {React.cloneElement(ICONS.LOCATION, {className: "w-4 h-4 mr-1"})}
                            {event.location}
                          </p>
                        )}
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-gray-500 dark:text-neutral-DEFAULT text-center py-4">
                Nenhum evento próximo.
              </p>
            )}
            <div className="mt-4 text-right">
              <a 
                href={`#${ROUTE_PATHS.AGENDA}`} 
                className="text-sm font-medium text-primary hover:text-primary-dark dark:text-primary-light dark:hover:text-primary"
              >
                Ver agenda completa &rarr;
              </a>
            </div>
          </Card>

          <Card title="Documentos Políticos">
            {isLoadingDocuments ? (
              <div className="flex justify-center items-center h-40">
                <LoadingSpinner />
              </div>
            ) : recentDocuments.length > 0 ? (
              <ul className="divide-y divide-gray-200 dark:divide-neutral-dark">
                {recentDocuments.map(doc => (
                  <li key={doc.id} className="py-3">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        {React.cloneElement(ICONS.DOCUMENTS, {className: "w-5 h-5 text-gray-400"})}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-primary dark:text-primary-light hover:underline truncate">
                          <a href={doc.url} target="_blank" rel="noopener noreferrer">
                            {doc.name}
                          </a>
                        </p>
                        <p className="text-xs text-gray-500 dark:text-neutral-DEFAULT mt-1">
                          {formatFileSize(doc.size)} • {formatDate(doc.createdAt)}
                        </p>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-gray-500 dark:text-neutral-DEFAULT text-center py-4">
                Nenhum documento recente.
              </p>
            )}
            <div className="mt-4 text-right">
              <a 
                href={`#${ROUTE_PATHS.DOCUMENTS}`} 
                className="text-sm font-medium text-primary hover:text-primary-dark dark:text-primary-light dark:hover:text-primary"
              >
                Ver todos documentos &rarr;
              </a>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
