import React, { useState } from 'react';
import { 
  Mail, 
  Phone, 
  Building2, 
  User, 
  MessageSquare,
  Send,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { Card } from '../ui/Card';
import { Button } from '../ui/Button';

interface LeadFormData {
  name: string;
  email: string;
  phone?: string;
  organization?: string;
  position?: string;
  planInterest: 'basic' | 'standard' | 'professional';
  message?: string;
  source: 'landing_page' | 'demo_request' | 'contact_form';
  utmSource?: string;
  utmMedium?: string;
  utmCampaign?: string;
}

interface LeadCaptureProps {
  source: 'landing_page' | 'demo_request' | 'contact_form';
  planInterest?: 'basic' | 'standard' | 'professional';
  onLeadSubmit?: (lead: LeadFormData) => void;
  className?: string;
}

const LeadCapture: React.FC<LeadCaptureProps> = ({ 
  source, 
  planInterest = 'standard', 
  onLeadSubmit,
  className = ''
}) => {
  const [formData, setFormData] = useState<LeadFormData>({
    name: '',
    email: '',
    phone: '',
    organization: '',
    position: '',
    planInterest,
    message: '',
    source,
    utmSource: '',
    utmMedium: '',
    utmCampaign: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Capturar parâmetros UTM da URL
  React.useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    setFormData(prev => ({
      ...prev,
      utmSource: urlParams.get('utm_source') || '',
      utmMedium: urlParams.get('utm_medium') || '',
      utmCampaign: urlParams.get('utm_campaign') || ''
    }));
  }, []);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Nome é obrigatório';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email é obrigatório';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Email inválido';
    }

    if (formData.phone && !/^[\d\s\(\)\-\+]+$/.test(formData.phone)) {
      newErrors.phone = 'Telefone inválido';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      // Adicionar informações de contexto
      const leadData: LeadFormData = {
        ...formData,
        // Capturar informações do navegador
        // userAgent: navigator.userAgent,
        // timestamp: new Date().toISOString(),
        // referrer: document.referrer
      };

      // Simular envio para API
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Aqui você faria a chamada real para a API
      // const response = await fetch('/api/leads', {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      //   body: JSON.stringify(leadData)
      // });

      // if (!response.ok) {
      //   throw new Error('Erro ao enviar formulário');
      // }

      setSubmitStatus('success');
      
      // Callback para componente pai
      if (onLeadSubmit) {
        onLeadSubmit(leadData);
      }

      // Limpar formulário após sucesso
      setFormData({
        name: '',
        email: '',
        phone: '',
        organization: '',
        position: '',
        planInterest,
        message: '',
        source,
        utmSource: formData.utmSource,
        utmMedium: formData.utmMedium,
        utmCampaign: formData.utmCampaign
      });

      // Tracking de conversão (Google Analytics, Facebook Pixel, etc.)
      if (typeof gtag !== 'undefined') {
        gtag('event', 'conversion', {
          send_to: 'AW-CONVERSION_ID/CONVERSION_LABEL',
          value: 1.0,
          currency: 'BRL'
        });
      }

    } catch (error) {
      console.error('Erro ao enviar lead:', error);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof LeadFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Limpar erro do campo quando usuário começar a digitar
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const getFormTitle = () => {
    switch (source) {
      case 'demo_request':
        return 'Solicitar Demonstração';
      case 'contact_form':
        return 'Entre em Contato';
      default:
        return 'Começar Teste Gratuito';
    }
  };

  const getSubmitButtonText = () => {
    if (isSubmitting) return 'Enviando...';
    
    switch (source) {
      case 'demo_request':
        return 'Agendar Demo';
      case 'contact_form':
        return 'Enviar Mensagem';
      default:
        return 'Começar Teste Gratuito';
    }
  };

  if (submitStatus === 'success') {
    return (
      <Card className={`p-8 text-center ${className}`}>
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <CheckCircle className="h-8 w-8 text-green-600" />
        </div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          {source === 'demo_request' ? 'Demo Agendada!' : 'Obrigado pelo interesse!'}
        </h3>
        <p className="text-gray-600 mb-6">
          {source === 'demo_request' 
            ? 'Nossa equipe entrará em contato em breve para agendar sua demonstração.'
            : 'Recebemos sua solicitação e entraremos em contato em breve.'
          }
        </p>
        <Button 
          onClick={() => setSubmitStatus('idle')}
          variant="outline"
        >
          Enviar Outro Formulário
        </Button>
      </Card>
    );
  }

  return (
    <Card className={`p-6 ${className}`}>
      <div className="mb-6">
        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          {getFormTitle()}
        </h3>
        <p className="text-gray-600">
          {source === 'demo_request' 
            ? 'Veja o Promandato em ação com uma demonstração personalizada.'
            : 'Preencha o formulário e nossa equipe entrará em contato.'
          }
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Nome */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Nome Completo *
          </label>
          <div className="relative">
            <User className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                errors.name ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Seu nome completo"
            />
          </div>
          {errors.name && (
            <p className="mt-1 text-sm text-red-600">{errors.name}</p>
          )}
        </div>

        {/* Email */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Email Profissional *
          </label>
          <div className="relative">
            <Mail className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
            <input
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                errors.email ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="<EMAIL>"
            />
          </div>
          {errors.email && (
            <p className="mt-1 text-sm text-red-600">{errors.email}</p>
          )}
        </div>

        {/* Telefone */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Telefone
          </label>
          <div className="relative">
            <Phone className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
            <input
              type="tel"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                errors.phone ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="(11) 99999-9999"
            />
          </div>
          {errors.phone && (
            <p className="mt-1 text-sm text-red-600">{errors.phone}</p>
          )}
        </div>

        {/* Organização */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Organização
          </label>
          <div className="relative">
            <Building2 className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
            <input
              type="text"
              value={formData.organization}
              onChange={(e) => handleInputChange('organization', e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Prefeitura, Câmara, Assembleia..."
            />
          </div>
        </div>

        {/* Plano de Interesse */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Plano de Interesse
          </label>
          <select
            value={formData.planInterest}
            onChange={(e) => handleInputChange('planInterest', e.target.value)}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="basic">Básico - R$ 169/mês</option>
            <option value="standard">Padrão - R$ 259/mês</option>
            <option value="professional">Profissional - R$ 449/mês</option>
          </select>
        </div>

        {/* Mensagem (apenas para contato) */}
        {source === 'contact_form' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Mensagem
            </label>
            <div className="relative">
              <MessageSquare className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
              <textarea
                value={formData.message}
                onChange={(e) => handleInputChange('message', e.target.value)}
                rows={4}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Como podemos ajudar?"
              />
            </div>
          </div>
        )}

        {/* Status de Erro */}
        {submitStatus === 'error' && (
          <div className="flex items-center p-3 bg-red-50 border border-red-200 rounded-lg">
            <AlertCircle className="h-4 w-4 text-red-600 mr-2" />
            <span className="text-sm text-red-600">
              Erro ao enviar formulário. Tente novamente.
            </span>
          </div>
        )}

        {/* Submit Button */}
        <Button
          type="submit"
          disabled={isSubmitting}
          className="w-full"
        >
          <Send className="h-4 w-4 mr-2" />
          {getSubmitButtonText()}
        </Button>

        {/* Disclaimer */}
        <p className="text-xs text-gray-500 text-center">
          Ao enviar este formulário, você concorda com nossa{' '}
          <a href="/privacidade" className="text-blue-600 hover:underline">
            Política de Privacidade
          </a>{' '}
          e{' '}
          <a href="/termos" className="text-blue-600 hover:underline">
            Termos de Uso
          </a>
          .
        </p>
      </form>
    </Card>
  );
};

export default LeadCapture;
