@echo off
echo ========================================
echo   DEPLOY BACKEND COM CORS CONFIGURADO
echo ========================================
echo.

echo [1/7] Verificando Google Cloud CLI...
gcloud version >nul 2>&1
if %errorlevel% neq 0 (
    echo Google Cloud CLI nao encontrado!
    echo Instale em: https://cloud.google.com/sdk/docs/install
    pause
    exit /b 1
)

echo Google Cloud CLI encontrado!
echo.

echo [2/7] Configurando projeto...
gcloud config set project promandato-9a4cf

echo.
echo [3/7] Configurando variaveis de ambiente para CORS...
cd backend

echo.
echo [4/7] Criando arquivo de configuracao de producao...
echo # Configuracao automatica para Cloud Run > .env.cloudrun
echo NODE_ENV=production >> .env.cloudrun
echo PORT=8080 >> .env.cloudrun
echo FRONTEND_URL=https://promandato-backend-2h6tsoanrq-rj.a.run.app,https://promandato-9a4cf.web.app,https://promandato-9a4cf.firebaseapp.com >> .env.cloudrun
echo CORS_ORIGIN=https://promandato-backend-2h6tsoanrq-rj.a.run.app,https://promandato-9a4cf.web.app,https://promandato-9a4cf.firebaseapp.com >> .env.cloudrun
echo DEBUG_CORS=true >> .env.cloudrun

echo.
echo [5/7] Fazendo build e deploy com configuracoes CORS...
gcloud run deploy promandato-backend ^
  --source . ^
  --platform managed ^
  --region southamerica-east1 ^
  --allow-unauthenticated ^
  --port 8080 ^
  --memory 1Gi ^
  --cpu 1 ^
  --min-instances 0 ^
  --max-instances 10 ^
  --set-env-vars NODE_ENV=production,PORT=8080,DEBUG_CORS=true ^
  --set-env-vars FRONTEND_URL="https://promandato-backend-2h6tsoanrq-rj.a.run.app,https://promandato-9a4cf.web.app,https://promandato-9a4cf.firebaseapp.com" ^
  --set-env-vars CORS_ORIGIN="https://promandato-backend-2h6tsoanrq-rj.a.run.app,https://promandato-9a4cf.web.app,https://promandato-9a4cf.firebaseapp.com"

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo       BACKEND DEPLOYADO COM SUCESSO!
    echo ========================================
    echo.
    echo [6/7] Testando CORS...
    echo URL do Backend: 
    gcloud run services describe promandato-backend --region=southamerica-east1 --format="value(status.url)"
    echo.
    echo [7/7] URLs configuradas para CORS:
    echo - https://promandato-backend-2h6tsoanrq-rj.a.run.app
    echo - https://promandato-9a4cf.web.app
    echo - https://promandato-9a4cf.firebaseapp.com
    echo.
    echo Teste o endpoint de debug: /api/cors-debug
    echo.
) else (
    echo.
    echo ERRO: Deploy falhou!
    echo Verifique os logs acima.
)

echo.
echo [Limpeza] Removendo arquivos temporarios...
del .env.cloudrun 2>nul

cd ..
pause
