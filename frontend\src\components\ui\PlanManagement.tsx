import React, { useState } from 'react';
import { usePlan } from '../../hooks/usePlan';
import { PlanType, PLANS } from '../../types/plans';
import { Card } from './Card';
import { Button } from './Button';
import AIUsageDashboard from './AIUsageDashboard';

const PlanManagement: React.FC = () => {
  const { currentPlan, planType, upgradePlan, downgradePlan, isLoading } = usePlan();
  const [isUpgrading, setIsUpgrading] = useState(false);
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');

  const handleSelectPlan = async (selectedPlanType: PlanType) => {
    if (selectedPlanType === planType || isUpgrading) return;

    try {
      setIsUpgrading(true);
      
      if (selectedPlanType > planType) {
        await upgradePlan(selectedPlanType);
      } else {
        await downgradePlan(selectedPlanType);
      }
      
      // Mostrar notificação de sucesso
      console.log(`Plano alterado para ${selectedPlanType} com sucesso!`);
    } catch (error) {
      console.error('Erro ao alterar plano:', error);
      // Mostrar notificação de erro
    } finally {
      setIsUpgrading(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(price);
  };

  const getDiscountPercentage = (monthly: number, yearly: number) => {
    const monthlyTotal = monthly * 12;
    const discount = ((monthlyTotal - yearly) / monthlyTotal) * 100;
    return Math.round(discount);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Current Plan Status */}
      {currentPlan && (
        <Card title="Plano Atual">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center space-x-4">
                <span className="text-neutral-medium">Plano:</span>
                <span className="font-medium text-primary text-lg">{currentPlan.name}</span>
                {currentPlan.badge && (
                  <span className="bg-secondary text-neutral-dark px-2 py-1 rounded text-sm font-medium">
                    {currentPlan.badge}
                  </span>
                )}
              </div>
              <div className="mt-2 text-sm text-neutral-medium">
                {currentPlan.description}
              </div>
            </div>
            
            <div className="text-right">
              <div className="text-2xl font-bold text-neutral-dark dark:text-neutral-light">
                {formatPrice(currentPlan.price.monthly)}
              </div>
              <div className="text-sm text-neutral-medium">por mês</div>
            </div>
          </div>
        </Card>
      )}

      {/* AI Usage Dashboard - Only for Professional Plan */}
      {planType === PlanType.PROFESSIONAL && (
        <AIUsageDashboard />
      )}

      {/* Billing Cycle Toggle */}
      <Card title="Alterar Plano">
        <div className="mb-6">
          <div className="flex items-center justify-center">
            <div className="bg-neutral-light dark:bg-neutral-dark rounded-lg p-1 flex">
              <button
                type="button"
                onClick={() => setBillingCycle('monthly')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  billingCycle === 'monthly'
                    ? 'bg-white dark:bg-neutral-darker text-neutral-dark dark:text-neutral-light shadow-sm'
                    : 'text-neutral-medium hover:text-neutral-dark dark:hover:text-neutral-light'
                }`}
              >
                Mensal
              </button>
              <button
                type="button"
                onClick={() => setBillingCycle('yearly')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  billingCycle === 'yearly'
                    ? 'bg-white dark:bg-neutral-darker text-neutral-dark dark:text-neutral-light shadow-sm'
                    : 'text-neutral-medium hover:text-neutral-dark dark:hover:text-neutral-light'
                }`}
              >
                Anual
                <span className="ml-1 text-xs text-secondary font-bold">
                  (Economize até 20%)
                </span>
              </button>
            </div>
          </div>
        </div>

        {/* Plans Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {PLANS.filter(plan => plan.enabled).map((plan) => {
            const isCurrentPlan = plan.id === planType;
            const isEnterprise = plan.id === PlanType.PROFESSIONAL;
            
            return (
              <div
                key={plan.id}
                className={`relative bg-white dark:bg-neutral-darker rounded-xl shadow-lg border-2 transition-all duration-300 hover:shadow-xl ${
                  plan.popular 
                    ? 'border-primary scale-105' 
                    : isEnterprise
                    ? 'border-secondary'
                    : 'border-neutral-light dark:border-neutral-dark'
                } ${isCurrentPlan ? 'ring-2 ring-primary' : ''}`}
              >
                {/* Badge */}
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-primary text-white px-4 py-1 rounded-full text-sm font-medium">
                      Mais Popular
                    </span>
                  </div>
                )}
                
                {plan.badge && (
                  <div className="absolute -top-3 right-4">
                    <span className="bg-secondary text-neutral-dark px-3 py-1 rounded-full text-sm font-bold">
                      {plan.badge}
                    </span>
                  </div>
                )}

                <div className="p-6">
                  {/* Header */}
                  <div className="text-center mb-6">
                    <h3 className="text-xl font-bold text-neutral-dark dark:text-neutral-light mb-2">
                      {plan.name}
                    </h3>
                    <p className="text-neutral-medium text-sm">
                      {plan.description}
                    </p>
                    
                    {/* Price */}
                    <div className="mb-6">
                      <div className="flex items-baseline justify-center">
                        <span className="text-4xl font-bold text-neutral-dark dark:text-neutral-light">
                          {formatPrice(plan.price[billingCycle])}
                        </span>
                        <span className="text-neutral-medium ml-2">
                          /{billingCycle === 'monthly' ? 'mês' : 'ano'}
                        </span>
                      </div>
                      
                      {billingCycle === 'yearly' && (
                        <div className="text-sm text-neutral-medium mt-2">
                          {formatPrice(plan.price.monthly)}/mês se pago mensalmente
                          <br />
                          <span className="text-secondary font-medium">
                            Economize {getDiscountPercentage(plan.price.monthly, plan.price.yearly)}%
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* CTA Button */}
                  <button
                    type="button"
                    onClick={() => handleSelectPlan(plan.id)}
                    disabled={isCurrentPlan || isUpgrading}
                    className={`w-full py-3 px-6 rounded-lg font-medium transition-colors ${
                      isCurrentPlan
                        ? 'bg-neutral-light text-neutral-medium cursor-not-allowed'
                        : isEnterprise
                        ? 'bg-secondary text-neutral-dark hover:bg-secondary-dark'
                        : plan.popular
                        ? 'bg-primary text-white hover:bg-primary-dark'
                        : 'bg-neutral-dark text-white hover:bg-neutral-darker dark:bg-neutral-light dark:text-neutral-dark dark:hover:bg-neutral-extralight'
                    }`}
                  >
                    {isCurrentPlan ? 'Plano Atual' : `Escolher ${plan.name}`}
                  </button>
                </div>
              </div>
            );
          })}
        </div>
      </Card>
    </div>
  );
};

export default PlanManagement;
