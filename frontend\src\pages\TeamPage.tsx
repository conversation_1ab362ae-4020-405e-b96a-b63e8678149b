
import React, { useState, useEffect } from 'react';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Modal } from '../components/ui/Modal';
import { Input } from '../components/ui/Input';
import { ICONS } from '../constants';
import { TeamMember, UserRole } from '../types';
import { useAuth } from '../hooks/useAuth';
import { 
  getFirestore, 
  collection, 
  getDocs, 
  doc, 
  setDoc, 
  deleteDoc, 
  serverTimestamp,
  query,
  where
} from 'firebase/firestore';

// Collection name for team members
const TEAM_MEMBERS_COLLECTION = 'team_members';

const TeamPage: React.FC = () => {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentMember, setCurrentMember] = useState<Partial<TeamMember> | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { currentUser } = useAuth();
  const allPermissions = ['view_demands', 'edit_demands', 'manage_citizens', 'manage_agenda', 'view_documents', 'manage_social_media', 'view_reports', 'manage_team'];

  // Fetch team members from Firestore
  useEffect(() => {
    const fetchTeamMembers = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const db = getFirestore();
        const teamMembersCollection = collection(db, TEAM_MEMBERS_COLLECTION);
        const masterId = currentUser?.role === UserRole.MASTER ? currentUser.id : currentUser?.masterId;
        console.log("Usando masterId para filtro:", masterId);

        let teamQuery;
        if (masterId) {
          teamQuery = query(teamMembersCollection, where("masterId", "==", masterId));
        } else {
          teamQuery = teamMembersCollection;
        }

        const querySnapshot = await getDocs(teamQuery);
        
        const members: TeamMember[] = [];
        querySnapshot.forEach((doc) => {
          const data = doc.data();
          members.push({
            id: doc.id,
            userId: data.userId,
            name: data.name,
            roleInTeam: data.roleInTeam,
            permissions: data.permissions || [],
            joinedAt: data.joinedAt?.toDate?.() 
              ? data.joinedAt.toDate().toISOString() 
              : data.joinedAt
          } as TeamMember);
        });
        
        setTeamMembers(members);
      } catch (err) {
        console.error("Error fetching team members:", err);
        setError("Falha ao carregar membros da equipe. Tente novamente.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchTeamMembers();
  }, []);

  const handleOpenModal = (member?: TeamMember) => {
    setCurrentMember(member || { name: '', roleInTeam: '', permissions: [] });
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setCurrentMember(null);
  };

  const handleSaveMember = async () => {
    if (!currentMember?.name || !currentMember?.roleInTeam) {
      alert("Nome e função são campos obrigatórios.");
      return;
    }

    try {
      const db = getFirestore();
      
      if (currentMember.id) {
        // Update existing member
        const memberRef = doc(db, TEAM_MEMBERS_COLLECTION, currentMember.id);
        await setDoc(memberRef, {
          name: currentMember.name,
          roleInTeam: currentMember.roleInTeam,
          permissions: currentMember.permissions || [],
          updatedAt: serverTimestamp(),
        }, { merge: true });
        
        setTeamMembers(prev => 
          prev.map(m => m.id === currentMember.id 
            ? { ...m, ...currentMember } as TeamMember 
            : m
          )
        );
      } else {
        // Create new member
        const newMemberId = `tm-${Date.now()}`;
        const memberRef = doc(db, TEAM_MEMBERS_COLLECTION, newMemberId);
        
        const newMember = {
          userId: `user-${Date.now()}`, // In a real app, this would be a real user ID
          name: currentMember.name,
          roleInTeam: currentMember.roleInTeam,
          permissions: currentMember.permissions || [],
          joinedAt: new Date().toISOString(),
          createdBy: currentUser?.id,
          createdAt: serverTimestamp(),
        };
        
        await setDoc(memberRef, newMember);
        
        // For local state, use string timestamp instead of serverTimestamp
        const memberForState: TeamMember = {
          ...newMember,
          id: newMemberId,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        
        setTeamMembers(prev => [
          ...prev, 
          memberForState
        ]);
      }
      
      handleCloseModal();
    } catch (err) {
      console.error("Error saving team member:", err);
      alert("Falha ao salvar membro da equipe. Tente novamente.");
    }
  };
  
  const handleDeleteMember = async (id: string) => {
    if (window.confirm("Tem certeza que deseja remover este membro da equipe?")) {
      try {
        const db = getFirestore();
        const memberRef = doc(db, TEAM_MEMBERS_COLLECTION, id);
        await deleteDoc(memberRef);
        
        setTeamMembers(prev => prev.filter(m => m.id !== id));
      } catch (err) {
        console.error("Error deleting team member:", err);
        alert("Falha ao remover membro da equipe. Tente novamente.");
      }
    }
  };

  const handlePermissionChange = (permission: string) => {
    setCurrentMember(prev => {
      if (!prev) return null;
      const newPermissions = prev.permissions?.includes(permission)
        ? prev.permissions.filter(p => p !== permission)
        : [...(prev.permissions || []), permission];
      return { ...prev, permissions: newPermissions };
    });
  };

  if (isLoading) {
    return <div className="flex justify-center items-center h-64">Carregando membros da equipe...</div>;
  }

  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <strong className="font-bold">Erro!</strong>
        <span className="block sm:inline"> {error}</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-neutral-dark dark:text-neutral-light">Gerenciamento de Equipe</h1>
        <Button onClick={() => handleOpenModal()} leftIcon={ICONS.PLUS}>
          Adicionar Membro
        </Button>
      </div>

      <Card>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-neutral-dark">
            <thead className="bg-gray-50 dark:bg-neutral-darker">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-neutral-DEFAULT uppercase tracking-wider">Nome</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-neutral-DEFAULT uppercase tracking-wider">Função na Equipe</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-neutral-DEFAULT uppercase tracking-wider">Permissões</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-neutral-DEFAULT uppercase tracking-wider">Desde</th>
                <th scope="col" className="relative px-6 py-3"><span className="sr-only">Ações</span></th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-neutral-darker divide-y divide-gray-200 dark:divide-neutral-dark">
              {teamMembers.length === 0 ? (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-neutral-DEFAULT">
                    Nenhum membro da equipe encontrado. Adicione um novo membro.
                  </td>
                </tr>
              ) : (
                teamMembers.map((member) => (
                  <tr key={member.id} className="hover:bg-gray-50 dark:hover:bg-neutral-dark">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-neutral-dark dark:text-neutral-light">{member.name}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-neutral-DEFAULT">{member.roleInTeam}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-neutral-DEFAULT">
                      {member.permissions && member.permissions.length > 3 ? `${member.permissions.slice(0,3).join(', ')}...` : member.permissions?.join(', ') || 'Nenhuma permissão'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-neutral-DEFAULT">{new Date(member.joinedAt).toLocaleDateString()}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <Button variant="ghost" size="sm" onClick={() => handleOpenModal(member)} className="text-primary hover:text-primary-dark dark:text-primary-light dark:hover:text-primary mr-2">
                        {React.cloneElement(ICONS.PENCIL, { className: "w-5 h-5"})}
                      </Button>
                      <Button variant="ghost" size="sm" onClick={() => handleDeleteMember(member.id)} className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-500">
                        {React.cloneElement(ICONS.TRASH, { className: "w-5 h-5"})}
                      </Button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </Card>
      
      <Modal isOpen={isModalOpen} onClose={handleCloseModal} title={currentMember?.id ? "Editar Membro" : "Adicionar Membro"}>
        <div className="space-y-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-neutral-light">Nome</label>
            <Input
              id="name"
              value={currentMember?.name || ''}
              onChange={(e) => setCurrentMember(prev => prev ? { ...prev, name: e.target.value } : null)}
              placeholder="Nome do membro"
              required
            />
          </div>
          
          <div>
            <label htmlFor="roleInTeam" className="block text-sm font-medium text-gray-700 dark:text-neutral-light">Função na Equipe</label>
            <Input
              id="roleInTeam"
              value={currentMember?.roleInTeam || ''}
              onChange={(e) => setCurrentMember(prev => prev ? { ...prev, roleInTeam: e.target.value } : null)}
              placeholder="Ex: Assessor, Secretário, Analista"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-neutral-light mb-2">Permissões</label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {allPermissions.map(permission => (
                <div key={permission} className="flex items-center">
                  <input
                    type="checkbox"
                    id={`permission-${permission}`}
                    checked={currentMember?.permissions?.includes(permission) || false}
                    onChange={() => handlePermissionChange(permission)}
                    className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                  />
                  <label htmlFor={`permission-${permission}`} className="ml-2 block text-sm text-gray-700 dark:text-neutral-light">
                    {permission.replace('_', ' ')}
                  </label>
                </div>
              ))}
            </div>
          </div>
          
          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={handleCloseModal}>Cancelar</Button>
            <Button onClick={handleSaveMember}>Salvar Membro</Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default TeamPage;
