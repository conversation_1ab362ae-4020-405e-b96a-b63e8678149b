// Enhanced error handling utilities

/**
 * Safe JSON parsing with fallback
 */
export const safeJsonParse = <T>(jsonString: string, fallback: T): T => {
  try {
    // Check if the string is actually an object
    if (typeof jsonString === 'object') {
      console.warn('Attempted to parse object as JSON string:', jsonString);
      return fallback;
    }
    
    // Check for common invalid JSON patterns
    if (jsonString === '[object Object]' || jsonString === 'undefined' || jsonString === 'null') {
      console.warn('Invalid JSON string detected:', jsonString);
      return fallback;
    }
    
    return JSON.parse(jsonString) as T;
  } catch (error) {
    console.warn('JSON parsing failed, using fallback:', error);
    return fallback;
  }
};

/**
 * Safe localStorage operations
 */
export const safeLocalStorage = {
  getItem: <T>(key: string, fallback: T): T => {
    try {
      const item = localStorage.getItem(key);
      if (item === null) return fallback;
      return safeJsonParse(item, fallback);
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error);
      return fallback;
    }
  },

  setItem: (key: string, value: any): boolean => {
    try {
      const serialized = typeof value === 'string' ? value : JSON.stringify(value);
      localStorage.setItem(key, serialized);
      return true;
    } catch (error) {
      console.warn(`Error setting localStorage key "${key}":`, error);
      return false;
    }
  },

  removeItem: (key: string): boolean => {
    try {
      localStorage.removeItem(key);
      return true;
    } catch (error) {
      console.warn(`Error removing localStorage key "${key}":`, error);
      return false;
    }
  }
};

/**
 * Global error handler for unhandled promise rejections
 */
export const setupGlobalErrorHandlers = () => {
  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event) => {
    const error = event.reason;
    
    // Check if this is a browser extension error
    if (isExtensionError(error)) {
      console.warn('Browser extension error detected, preventing default handling:', error);
      event.preventDefault();
      return;
    }
    
    console.error('Unhandled promise rejection:', error);
  });

  // Handle general errors
  window.addEventListener('error', (event) => {
    const error = event.error;
    
    // Check if this is a browser extension error
    if (isExtensionError(error) || event.filename?.includes('extension://')) {
      console.warn('Browser extension error detected:', error);
      event.preventDefault();
      return;
    }
    
    console.error('Global error:', error);
  });
};

/**
 * Check if an error is likely from a browser extension
 */
export const isExtensionError = (error: any): boolean => {
  if (!error) return false;
  
  const errorString = error.toString();
  const stackString = error.stack?.toString() || '';
  
  // Common patterns for extension errors
  const extensionPatterns = [
    'content.js',
    'extension://',
    'chrome-extension://',
    'moz-extension://',
    'safari-extension://',
    '_storageChangeDispatcher',
    'not valid JSON',
    '[object Object]'
  ];
  
  return extensionPatterns.some(pattern => 
    errorString.includes(pattern) || stackString.includes(pattern)
  );
};

/**
 * Clean up corrupted localStorage data
 */
export const cleanupCorruptedStorage = () => {
  const keysToCheck = [
    'darkMode',
    'dashboard-widgets',
    'user-preferences',
    'theme-preference',
    'tour-completed'
  ];

  keysToCheck.forEach(key => {
    try {
      const value = localStorage.getItem(key);
      if (value) {
        safeJsonParse(value, null);
      }
    } catch (error) {
      console.warn(`Removing corrupted localStorage key: ${key}`);
      safeLocalStorage.removeItem(key);
    }
  });
};

/**
 * Initialize error handling
 */
export const initializeErrorHandling = () => {
  setupGlobalErrorHandlers();
  cleanupCorruptedStorage();
  
  console.log('✅ Error handling initialized');
};
