@echo off
echo ========================================
echo   CORRECAO DEFINITIVA GITHUB ACTIONS
echo ========================================
echo.

echo [1/5] Verificando workflows atuais...
if exist ".github\workflows" (
    echo Workflows encontrados:
    dir .github\workflows
) else (
    echo Nenhum workflow encontrado.
)

echo.
echo [2/5] Removendo conexao GitHub do Firebase...
echo (Isso remove workflows automaticos que podem estar conflitando)

firebase hosting:github:disconnect --project promandato-9a4cf 2>nul
if %errorlevel% equ 0 (
    echo Conexao GitHub removida com sucesso!
) else (
    echo Nenhuma conexao GitHub ativa encontrada.
)

echo.
echo [3/5] Verificando se nossos workflows estao corretos...

if not exist ".github\workflows\firebase-hosting-merge.yml" (
    echo ERRO: Workflow principal nao encontrado!
    echo Recriando workflow...
    goto create_workflows
)

echo Verificando conteudo do workflow...
findstr "FirebaseExtended" .github\workflows\firebase-hosting-merge.yml >nul
if %errorlevel% equ 0 (
    echo PROBLEMA: Workflow ainda usa FirebaseExtended action!
    echo Corrigindo...
    goto fix_workflows
) else (
    echo Workflow parece correto.
)

goto generate_token

:create_workflows
echo Criando workflows corretos...
mkdir .github\workflows 2>nul

echo # Deploy automatico para Firebase Hosting > .github\workflows\firebase-hosting-merge.yml
echo name: Deploy to Firebase Hosting on merge >> .github\workflows\firebase-hosting-merge.yml
echo 'on': >> .github\workflows\firebase-hosting-merge.yml
echo   push: >> .github\workflows\firebase-hosting-merge.yml
echo     branches: >> .github\workflows\firebase-hosting-merge.yml
echo       - main >> .github\workflows\firebase-hosting-merge.yml
echo     paths: >> .github\workflows\firebase-hosting-merge.yml
echo       - 'landingpage/**' >> .github\workflows\firebase-hosting-merge.yml
echo jobs: >> .github\workflows\firebase-hosting-merge.yml
echo   build_and_deploy: >> .github\workflows\firebase-hosting-merge.yml
echo     runs-on: ubuntu-latest >> .github\workflows\firebase-hosting-merge.yml
echo     steps: >> .github\workflows\firebase-hosting-merge.yml
echo       - name: Checkout code >> .github\workflows\firebase-hosting-merge.yml
echo         uses: actions/checkout@v4 >> .github\workflows\firebase-hosting-merge.yml
echo       - name: Setup Node.js >> .github\workflows\firebase-hosting-merge.yml
echo         uses: actions/setup-node@v4 >> .github\workflows\firebase-hosting-merge.yml
echo         with: >> .github\workflows\firebase-hosting-merge.yml
echo           node-version: '18' >> .github\workflows\firebase-hosting-merge.yml
echo       - name: Install Firebase CLI >> .github\workflows\firebase-hosting-merge.yml
echo         run: npm install -g firebase-tools >> .github\workflows\firebase-hosting-merge.yml
echo       - name: Deploy to Firebase Hosting >> .github\workflows\firebase-hosting-merge.yml
echo         run: ^| >> .github\workflows\firebase-hosting-merge.yml
echo           echo "Deploying to Firebase Hosting..." >> .github\workflows\firebase-hosting-merge.yml
echo           firebase deploy --only hosting --project promandato-9a4cf --token "${{ secrets.FIREBASE_TOKEN }}" >> .github\workflows\firebase-hosting-merge.yml
echo         env: >> .github\workflows\firebase-hosting-merge.yml
echo           FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }} >> .github\workflows\firebase-hosting-merge.yml

goto generate_token

:fix_workflows
echo Corrigindo workflows existentes...
echo Backup do workflow atual...
copy .github\workflows\firebase-hosting-merge.yml .github\workflows\firebase-hosting-merge.yml.backup

echo Recriando workflow correto...
goto create_workflows

:generate_token
echo.
echo [4/5] Gerando token Firebase...
echo.
echo ========================================
echo        COPIE O TOKEN ABAIXO!
echo ========================================
echo.

firebase login:ci

echo.
echo [5/5] Configuracao concluida!
echo.
echo ========================================
echo           PROXIMOS PASSOS
echo ========================================
echo.
echo 1. COPIE o token acima
echo 2. Va para GitHub > Settings > Secrets > Actions
echo 3. Crie um novo secret:
echo    - Nome: FIREBASE_TOKEN
echo    - Valor: [cole o token]
echo 4. Faca um commit para testar:
echo    git add .
echo    git commit -m "fix: corrigir github actions"
echo    git push origin main
echo.
echo O deploy automatico devera funcionar agora!
echo.

pause
