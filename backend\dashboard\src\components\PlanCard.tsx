import React from 'react';
import { Plan, PlanType } from '../types/plans';

interface PlanCardProps {
  plan: Plan;
  onEdit: (plan: Plan) => void;
  onToggle: (planId: PlanType, enabled: boolean) => void;
  formatPrice: (price: number) => string;
}

const PlanCard: React.FC<PlanCardProps> = ({ plan, onEdit, onToggle, formatPrice }) => {
  const getPlanColor = (planId: PlanType) => {
    switch (planId) {
      case PlanType.BASIC:
        return 'border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-800';
      case PlanType.STANDARD:
        return 'border-blue-300 dark:border-blue-600 bg-blue-50 dark:bg-blue-900/20';
      case PlanType.PROFESSIONAL:
        return 'border-purple-300 dark:border-purple-600 bg-purple-50 dark:bg-purple-900/20';
      default:
        return 'border-gray-300 dark:border-gray-600 bg-gray-50 dark:bg-gray-800';
    }
  };

  const getStatusColor = (enabled: boolean) => {
    return enabled 
      ? 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300' 
      : 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300';
  };

  return (
    <div className={`rounded-lg border-2 p-6 ${getPlanColor(plan.id)} ${!plan.enabled ? 'opacity-60' : ''}`}>
      {/* Header */}
      <div className="flex justify-between items-start mb-4">
        <div>
          <h3 className="text-xl font-bold text-gray-900 dark:text-white">{plan.name}</h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{plan.description}</p>
        </div>
        <div className="flex flex-col items-end space-y-2">
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(plan.enabled)}`}>
            {plan.enabled ? 'Ativo' : 'Inativo'}
          </span>
          {plan.popular && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300">
              Popular
            </span>
          )}
          {plan.badge && (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-300">
              {plan.badge}
            </span>
          )}
        </div>
      </div>

      {/* Pricing */}
      <div className="mb-6">
        <div className="flex items-baseline">
          <span className="text-3xl font-bold text-gray-900 dark:text-white">
            {formatPrice(plan.price.monthly)}
          </span>
          <span className="text-gray-500 dark:text-gray-400 ml-1">/mês</span>
        </div>
        <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          {formatPrice(plan.price.yearly)}/ano
          <span className="text-green-600 dark:text-green-400 ml-2">
            (Economize {Math.round(((plan.price.monthly * 12 - plan.price.yearly) / (plan.price.monthly * 12)) * 100)}%)
          </span>
        </div>
      </div>

      {/* Key Features */}
      <div className="mb-6">
        <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Recursos Principais</h4>
        <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
          <li className="flex items-center">
            <span className="w-2 h-2 bg-green-400 dark:bg-green-500 rounded-full mr-2"></span>
            {plan.features.maxUsers === -1 ? 'Usuários ilimitados' : `${plan.features.maxUsers} usuários`}
          </li>
          <li className="flex items-center">
            <span className="w-2 h-2 bg-green-400 dark:bg-green-500 rounded-full mr-2"></span>
            {plan.features.maxDemands === -1 ? 'Demandas ilimitadas' : `${plan.features.maxDemands} demandas`}
          </li>
          <li className="flex items-center">
            <span className="w-2 h-2 bg-green-400 dark:bg-green-500 rounded-full mr-2"></span>
            {plan.features.storageGB}GB de armazenamento
          </li>
          {plan.features.aiFeatures.length > 0 && (
            <li className="flex items-center">
              <span className="w-2 h-2 bg-purple-400 dark:bg-purple-500 rounded-full mr-2"></span>
              {plan.features.aiFeatures.length} recursos de IA
            </li>
          )}
        </ul>
      </div>

      {/* Actions */}
      <div className="flex space-x-3">
        <button
          type="button"
          onClick={() => onEdit(plan)}
          className="flex-1 bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors"
        >
          ✏️ Editar
        </button>
        <button
          type="button"
          onClick={() => onToggle(plan.id, !plan.enabled)}
          className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
            plan.enabled
              ? 'bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 hover:bg-red-200 dark:hover:bg-red-900/50'
              : 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 hover:bg-green-200 dark:hover:bg-green-900/50'
          }`}
        >
          {plan.enabled ? '🔴 Desativar' : '🟢 Ativar'}
        </button>
      </div>

      {/* Metadata */}
      <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
        <div className="text-xs text-gray-500 dark:text-gray-400">
          <div>Criado: {new Date(plan.createdAt).toLocaleDateString('pt-BR')}</div>
          <div>Atualizado: {new Date(plan.updatedAt).toLocaleDateString('pt-BR')}</div>
        </div>
      </div>
    </div>
  );
};

export default PlanCard;