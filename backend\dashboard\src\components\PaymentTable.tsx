import React from 'react';
import { PaymentData } from '../types/payment.types';
import { 
  formatCurrency, 
  formatDate, 
  getStatusIcon, 
  getStatusColor, 
  getPlanColor,
  getPlanDisplayName,
  getBillingCycleDisplayName,
  getStatusDisplayName
} from '../utils/paymentUtils';

interface PaymentTableProps {
  payments: PaymentData[];
  onViewDetails: (paymentId: string) => void;
  onRefund: (paymentId: string) => void;
}

const PaymentTable: React.FC<PaymentTableProps> = ({ 
  payments, 
  onViewDetails, 
  onRefund 
}) => {
  return (
    <div className="bg-white rounded-lg shadow overflow-hidden">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">
          Transações Recentes
        </h3>
      </div>
      
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Cliente
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Plano
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Valor
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Data
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Ações
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {payments.map((payment) => {
              const StatusIcon = getStatusIcon(payment.status);
              
              return (
                <tr key={payment.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">
                        {payment.customerName}
                      </div>
                      <div className="text-sm text-gray-500">
                        {payment.customerEmail}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPlanColor(payment.planType as any)}`}>
                      {getPlanDisplayName(payment.planType as any)}
                    </span>
                    <div className="text-xs text-gray-500 mt-1">
                      {getBillingCycleDisplayName(payment.billingCycle)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatCurrency(payment.amount)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <StatusIcon className="h-4 w-4 text-current" />
                      <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(payment.status)}`}>
                        {getStatusDisplayName(payment.status)}
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatDate(payment.createdAt)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button 
                      type="button"
                      onClick={() => onViewDetails(payment.id)}
                      className="text-blue-600 hover:text-blue-900 mr-3"
                    >
                      Ver Detalhes
                    </button>
                    <button 
                      type="button"
                      onClick={() => onRefund(payment.id)}
                      className="text-gray-600 hover:text-gray-900"
                    >
                      Reembolsar
                    </button>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default PaymentTable;
