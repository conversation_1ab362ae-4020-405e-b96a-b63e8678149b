import React, { useState, useEffect } from 'react';
import { CreditCard, RefreshCw, Download } from 'lucide-react';
import { PaymentData, PaymentMetrics } from '../types/payment.types';
import { mockPayments, mockMetrics } from '../data/mockPayments';
import { FILTER_OPTIONS } from '../constants/paymentConstants';
import PaymentMetricsCards from './PaymentMetricsCards';
import PaymentFilters from './PaymentFilters';
import PaymentTable from './PaymentTable';

const PaymentsMonitor: React.FC = () => {
  const [payments, setPayments] = useState<PaymentData[]>([]);
  const [metrics, setMetrics] = useState<PaymentMetrics>({
    totalRevenue: 0,
    monthlyRecurring: 0,
    yearlyRecurring: 0,
    totalCustomers: 0,
    successRate: 0,
    churnRate: 0,
    averageRevenuePerUser: 0,
    conversionRate: 0
  });
  const [loading, setLoading] = useState(true);
  const [dateFilter, setDateFilter] = useState(FILTER_OPTIONS.DATE[1].value);
  const [statusFilter, setStatusFilter] = useState(FILTER_OPTIONS.STATUS[0].value);
  const [planFilter, setPlanFilter] = useState(FILTER_OPTIONS.PLAN[0].value);

  useEffect(() => {
    loadPaymentData();
  }, [dateFilter, statusFilter, planFilter]);

  const loadPaymentData = async (): Promise<void> => {
    setLoading(true);

    // Simular delay da API
    await new Promise(resolve => setTimeout(resolve, 1000));

    setPayments(mockPayments);
    setMetrics(mockMetrics);
    setLoading(false);
  };

  const handleViewDetails = (paymentId: string): void => {
    console.log('Ver detalhes do pagamento:', paymentId);
    // TODO: Implementar visualização de detalhes
  };

  const handleRefund = (paymentId: string): void => {
    console.log('Reembolsar pagamento:', paymentId);
    // TODO: Implementar reembolso
  };

  const handleExportData = (): void => {
    console.log('Exportando dados de pagamentos...');
    // TODO: Implementar exportação de dados
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <CreditCard className="h-6 w-6 mr-2 text-blue-600" />
              Monitor de Pagamentos
            </h1>
            <p className="text-gray-600">
              Acompanhe receitas, assinaturas e métricas financeiras
            </p>
          </div>
          <div className="flex space-x-3">
            <button
              type="button"
              onClick={loadPaymentData}
              className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Atualizar
            </button>
            <button
              type="button"
              onClick={handleExportData}
              className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
            >
              <Download className="h-4 w-4 mr-2" />
              Exportar
            </button>
          </div>
        </div>

        {/* Métricas */}
        <PaymentMetricsCards metrics={metrics} />

        {/* Filtros */}
        <PaymentFilters
          dateFilter={dateFilter}
          statusFilter={statusFilter}
          planFilter={planFilter}
          onDateFilterChange={setDateFilter}
          onStatusFilterChange={setStatusFilter}
          onPlanFilterChange={setPlanFilter}
        />

        {/* Lista de Pagamentos */}
        <PaymentTable
          payments={payments}
          onViewDetails={handleViewDetails}
          onRefund={handleRefund}
        />
      </div>
    </div>
  );
};

export default PaymentsMonitor;


