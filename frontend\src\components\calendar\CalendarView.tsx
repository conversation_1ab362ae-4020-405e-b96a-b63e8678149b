import React, { useState, useCallback, useMemo } from 'react';
import { Calendar, dateFnsLocalizer, View, Views } from 'react-big-calendar';
import { format, parse, startOfWeek, getDay } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { AgendaEvent } from '../../types';
import { EventModal } from './EventModal';
import {
  convertAgendaEventToCalendarEvent,
  createNewEvent,
  getEventStyle,
  formats,
  messages,
} from '../../utils/calendarHelpers';
import 'react-big-calendar/lib/css/react-big-calendar.css';

// Configurar localizer com date-fns
const localizer = dateFnsLocalizer({
  format,
  parse,
  startOfWeek: () => startOfWeek(new Date(), { weekStartsOn: 0 }), // Domingo como primeiro dia
  getDay,
  locales: { 'pt-BR': ptBR },
});

interface CalendarViewProps {
  events: AgendaEvent[];
  onSaveEvent: (event: Partial<AgendaEvent>) => Promise<void>;
  onDeleteEvent: (eventId: string) => Promise<void>;
  isLoading?: boolean;
}

export const CalendarView: React.FC<CalendarViewProps> = ({
  events,
  onSaveEvent,
  onDeleteEvent,
  isLoading = false,
}) => {
  const [view, setView] = useState<View>(Views.MONTH);
  const [date, setDate] = useState(new Date());
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<Partial<AgendaEvent> | null>(null);

  // Converter eventos para formato do calendário
  const calendarEvents = useMemo(() => {
    return events.map(convertAgendaEventToCalendarEvent);
  }, [events]);

  // Manipular seleção de slot (criar novo evento)
  const handleSelectSlot = useCallback((slotInfo: any) => {
    const newEvent = createNewEvent(slotInfo);
    setSelectedEvent(newEvent);
    setIsModalOpen(true);
  }, []);

  // Manipular seleção de evento (editar evento existente)
  const handleSelectEvent = useCallback((calendarEvent: any) => {
    const agendaEvent = events.find(e => e.id === calendarEvent.id);
    if (agendaEvent) {
      setSelectedEvent(agendaEvent);
      setIsModalOpen(true);
    }
  }, [events]);

  // Manipular mudança de visualização
  const handleViewChange = useCallback((newView: View) => {
    setView(newView);
  }, []);

  // Manipular navegação de data
  const handleNavigate = useCallback((newDate: Date) => {
    setDate(newDate);
  }, []);

  // Fechar modal
  const handleCloseModal = useCallback(() => {
    setIsModalOpen(false);
    setSelectedEvent(null);
  }, []);

  // Salvar evento
  const handleSaveEvent = useCallback(async (eventData: Partial<AgendaEvent>) => {
    await onSaveEvent(eventData);
    handleCloseModal();
  }, [onSaveEvent]);

  // Excluir evento
  const handleDeleteEvent = useCallback(async (eventId: string) => {
    await onDeleteEvent(eventId);
    handleCloseModal();
  }, [onDeleteEvent]);

  // Componente customizado para eventos
  const EventComponent = ({ event }: { event: any }) => (
    <div className="text-xs">
      <strong>{event.title}</strong>
      {event.resource?.location && (
        <div className="opacity-75">📍 {event.resource.location}</div>
      )}
    </div>
  );

  // Componente customizado para agenda
  const AgendaEvent = ({ event }: { event: any }) => (
    <div className="flex items-center space-x-2">
      <div className="flex-1">
        <div className="font-medium">{event.title}</div>
        {event.resource?.description && (
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {event.resource.description}
          </div>
        )}
        {event.resource?.location && (
          <div className="text-sm text-gray-500 dark:text-gray-500">
            📍 {event.resource.location}
          </div>
        )}
        {event.resource?.attendees && event.resource.attendees.length > 0 && (
          <div className="text-sm text-gray-500 dark:text-gray-500">
            👥 {event.resource.attendees.join(', ')}
          </div>
        )}
      </div>
    </div>
  );

  return (
    <div className="h-full">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              Calendário de Eventos
            </h2>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500 dark:text-gray-400">
                Clique em um espaço vazio para criar um evento
              </span>
            </div>
          </div>
        </div>

        <div className="p-4">
          <div style={{ height: '600px' }} className="calendar-container">
            <Calendar
              localizer={localizer}
              events={calendarEvents}
              startAccessor="start"
              endAccessor="end"
              style={{ height: '100%' }}
              view={view}
              date={date}
              onView={handleViewChange}
              onNavigate={handleNavigate}
              onSelectSlot={handleSelectSlot}
              onSelectEvent={handleSelectEvent}
              selectable
              popup
              formats={formats}
              messages={messages}
              culture="pt-BR"
              eventPropGetter={getEventStyle}
              components={{
                event: EventComponent,
                agenda: {
                  event: AgendaEvent,
                },
              }}
              views={[Views.MONTH, Views.WEEK, Views.DAY, Views.AGENDA]}
              step={30}
              showMultiDayTimes
              defaultDate={new Date()}
              scrollToTime={new Date(1970, 1, 1, 8)}
            />
          </div>
        </div>
      </div>

      <EventModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSave={handleSaveEvent}
        onDelete={selectedEvent?.id ? handleDeleteEvent : undefined}
        event={selectedEvent}
        isLoading={isLoading}
      />
    </div>
  );
};
