#!/bin/bash

echo "🚀 Configurando Dashboard de Administração de Planos..."

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para imprimir mensagens coloridas
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verificar se Node.js está instalado
if ! command -v node &> /dev/null; then
    print_error "Node.js não está instalado. Por favor, instale o Node.js primeiro."
    exit 1
fi

print_status "Node.js encontrado: $(node --version)"

# Verificar se npm está instalado
if ! command -v npm &> /dev/null; then
    print_error "npm não está instalado. Por favor, instale o npm primeiro."
    exit 1
fi

print_status "npm encontrado: $(npm --version)"

# Instalar dependências do backend
print_status "Instalando dependências do backend..."
npm install

if [ $? -eq 0 ]; then
    print_success "Dependências do backend instaladas com sucesso!"
else
    print_error "Erro ao instalar dependências do backend"
    exit 1
fi

# Navegar para o diretório do dashboard
cd dashboard

# Instalar dependências do dashboard
print_status "Instalando dependências do dashboard..."
npm install

if [ $? -eq 0 ]; then
    print_success "Dependências do dashboard instaladas com sucesso!"
else
    print_error "Erro ao instalar dependências do dashboard"
    exit 1
fi

# Build do dashboard
print_status "Fazendo build do dashboard..."
npm run build

if [ $? -eq 0 ]; then
    print_success "Build do dashboard concluído com sucesso!"
else
    print_error "Erro ao fazer build do dashboard"
    exit 1
fi

# Voltar para o diretório raiz
cd ..

# Criar diretório de dados se não existir
mkdir -p data

print_success "Setup concluído com sucesso! 🎉"
echo ""
echo "Para iniciar o dashboard:"
echo "  ${BLUE}npm start${NC}        - Produção (API + Dashboard)"
echo "  ${BLUE}npm run dev${NC}      - Desenvolvimento (apenas API)"
echo ""
echo "Para desenvolvimento do dashboard:"
echo "  ${BLUE}npm run dashboard:dev${NC}  - Servidor de desenvolvimento do dashboard"
echo ""
echo "URLs:"
echo "  Dashboard: ${YELLOW}http://localhost:3002${NC}"
echo "  API:       ${YELLOW}http://localhost:3002/api${NC}"
echo ""
print_warning "Lembre-se de configurar autenticação antes de usar em produção!"