#!/usr/bin/env node

/**
 * Script para testar a integração Stripe do lado do servidor
 */

const fetch = require('node-fetch');

const API_BASE_URL = 'http://localhost:3002';

async function testStripeIntegration() {
    console.log('🔍 Testando integração Stripe...\n');

    // 1. Testar health check
    console.log('1. Testando health check...');
    try {
        const response = await fetch(`${API_BASE_URL}/api/health`);
        if (response.ok) {
            const data = await response.json();
            console.log(`   ✅ Health check OK: ${data.message}`);
        } else {
            throw new Error(`HTTP ${response.status}`);
        }
    } catch (error) {
        console.log(`   ❌ Health check falhou: ${error.message}`);
        return;
    }

    // 2. Testar criação de checkout session
    console.log('\n2. Testando criação de checkout session...');
    try {
        const testData = {
            planType: 'basic',
            billingCycle: 'monthly',
            leadData: {
                name: 'Teste Usuario',
                email: '<EMAIL>',
                phone: '(11) 99999-9999',
                organization: 'Teste Org',
                source: 'test_script'
            },
            successUrl: `${API_BASE_URL}/landingpage/success.html`,
            cancelUrl: `${API_BASE_URL}/landingpage/index.html`
        };

        const response = await fetch(`${API_BASE_URL}/api/stripe/create-checkout-session`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(testData)
        });

        if (response.ok) {
            const session = await response.json();
            console.log(`   ✅ Checkout session criada: ${session.sessionId}`);
            console.log(`   🔗 URL do Stripe: https://checkout.stripe.com/pay/${session.sessionId}`);
        } else {
            const errorText = await response.text();
            throw new Error(`HTTP ${response.status}: ${errorText}`);
        }
    } catch (error) {
        console.log(`   ❌ Erro na criação de checkout: ${error.message}`);
    }

    // 3. Testar diferentes planos
    console.log('\n3. Testando diferentes planos...');
    const plans = ['basic', 'standard', 'professional'];
    const billingCycles = ['monthly', 'yearly'];

    for (const plan of plans) {
        for (const billing of billingCycles) {
            try {
                const testData = {
                    planType: plan,
                    billingCycle: billing,
                    leadData: {
                        name: 'Teste Usuario',
                        email: '<EMAIL>',
                        source: 'test_script'
                    },
                    successUrl: `${API_BASE_URL}/landingpage/success.html`,
                    cancelUrl: `${API_BASE_URL}/landingpage/index.html`
                };

                const response = await fetch(`${API_BASE_URL}/api/stripe/create-checkout-session`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });

                if (response.ok) {
                    const session = await response.json();
                    console.log(`   ✅ ${plan} (${billing}): ${session.sessionId}`);
                } else {
                    const errorText = await response.text();
                    console.log(`   ❌ ${plan} (${billing}): HTTP ${response.status}`);
                }
            } catch (error) {
                console.log(`   ❌ ${plan} (${billing}): ${error.message}`);
            }
        }
    }

    // 4. Testar acesso à landing page
    console.log('\n4. Testando acesso à landing page...');
    try {
        const response = await fetch(`${API_BASE_URL}/landingpage/index.html`);
        if (response.ok) {
            console.log('   ✅ Landing page acessível');
        } else {
            throw new Error(`HTTP ${response.status}`);
        }
    } catch (error) {
        console.log(`   ❌ Landing page inacessível: ${error.message}`);
    }

    console.log('\n✅ Teste completo!');
    console.log('\n📋 Próximos passos:');
    console.log('   1. Abra http://localhost:3002/landingpage/index.html');
    console.log('   2. Abra o console do navegador (F12)');
    console.log('   3. Execute: diagnoseStripeIntegration()');
    console.log('   4. Clique nos botões de plano para testar');
}

// Executar teste
testStripeIntegration().catch(console.error);
