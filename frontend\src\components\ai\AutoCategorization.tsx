import React, { useState } from 'react';
import { ICONS } from '../../constants';
import { usePlan } from '../../hooks/usePlan';
import { Button } from '../ui/Button';
import { Card } from '../ui/Card';
import { Textarea } from '../ui/Textarea';

interface CategoryResult {
  text: string;
  category: string;
  subcategory: string | null;
  confidence: number;
  suggestedTags: string[];
  priority: 'low' | 'medium' | 'high';
}

const AutoCategorization: React.FC = () => {
  const { canUseAIFeature, incrementAIUsage, getAIFeatureUsage, currentPlan } = usePlan();
  const [text, setText] = useState('');
  const [result, setResult] = useState<CategoryResult | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const canUseFeature = canUseAIFeature('auto-categorization');
  const usage = getAIFeatureUsage('auto-categorization');

  const categories = {
    'infraestrutura': {
      name: 'Infraestrutura',
      keywords: ['buraco', 'asfalto', 'rua', 'calçada', 'ponte', 'estrada', 'iluminação', 'semáforo'],
      subcategories: ['Pavimentação', 'Iluminação Pública', 'Sinalização', 'Pontes e Viadutos'],
      color: 'bg-orange-100 text-orange-800'
    },
    'saude': {
      name: 'Saúde',
      keywords: ['posto', 'hospital', 'médico', 'remédio', 'consulta', 'saúde', 'ambulância', 'ubs'],
      subcategories: ['Atendimento Médico', 'Medicamentos', 'Infraestrutura de Saúde', 'Emergência'],
      color: 'bg-red-100 text-red-800'
    },
    'educacao': {
      name: 'Educação',
      keywords: ['escola', 'professor', 'ensino', 'educação', 'creche', 'universidade', 'biblioteca'],
      subcategories: ['Ensino Fundamental', 'Ensino Médio', 'Educação Infantil', 'Infraestrutura Escolar'],
      color: 'bg-blue-100 text-blue-800'
    },
    'seguranca': {
      name: 'Segurança',
      keywords: ['polícia', 'segurança', 'roubo', 'violência', 'crime', 'patrulhamento'],
      subcategories: ['Policiamento', 'Segurança Pública', 'Prevenção', 'Emergência'],
      color: 'bg-purple-100 text-purple-800'
    },
    'meio-ambiente': {
      name: 'Meio Ambiente',
      keywords: ['lixo', 'poluição', 'árvore', 'parque', 'meio ambiente', 'coleta', 'reciclagem'],
      subcategories: ['Coleta de Lixo', 'Arborização', 'Poluição', 'Parques e Praças'],
      color: 'bg-green-100 text-green-800'
    },
    'transporte': {
      name: 'Transporte',
      keywords: ['ônibus', 'transporte', 'trânsito', 'estacionamento', 'ciclovia'],
      subcategories: ['Transporte Público', 'Trânsito', 'Ciclovias', 'Estacionamento'],
      color: 'bg-indigo-100 text-indigo-800'
    }
  };

  const handleCategorize = async () => {
    if (!text.trim()) {
      setError('Por favor, insira um texto para categorização');
      return;
    }

    if (!canUseFeature) {
      setError('Funcionalidade não disponível no seu plano atual');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      // Incrementar uso antes da análise
      const canProceed = await incrementAIUsage('auto-categorization');
      if (!canProceed) {
        setError('Limite de uso atingido para este mês');
        return;
      }

      // Simular processamento de IA
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Análise simples baseada em palavras-chave
      const words = text.toLowerCase().split(/\s+/);
      const scores: Record<string, number> = {};
      
      Object.keys(categories).forEach(categoryKey => {
        scores[categoryKey] = 0;
        categories[categoryKey as keyof typeof categories].keywords.forEach(keyword => {
          words.forEach(word => {
            if (word.includes(keyword)) scores[categoryKey]++;
          });
        });
      });

      // Encontrar categoria com maior pontuação
      const bestCategory = Object.keys(scores).reduce((a, b) => 
        scores[a] > scores[b] ? a : b
      );
      
      const confidence = scores[bestCategory] > 0 ? 
        Math.min(0.95, 0.6 + (scores[bestCategory] / words.length) * 2) : 0.3;

      const categoryInfo = categories[bestCategory as keyof typeof categories];
      const subcategory = categoryInfo.subcategories[
        Math.floor(Math.random() * categoryInfo.subcategories.length)
      ];

      // Determinar prioridade baseada em palavras-chave urgentes
      const urgentWords = ['urgente', 'emergência', 'grave', 'perigo', 'risco'];
      const hasUrgentWords = words.some(word => 
        urgentWords.some(urgent => word.includes(urgent))
      );
      
      const priority: 'low' | 'medium' | 'high' = 
        hasUrgentWords ? 'high' : 
        confidence > 0.7 ? 'medium' : 'low';

      setResult({
        text: text.substring(0, 200) + (text.length > 200 ? '...' : ''),
        category: categoryInfo.name,
        subcategory,
        confidence,
        suggestedTags: categoryInfo.keywords.slice(0, 3),
        priority
      });

    } catch (error) {
      console.error('Erro na categorização:', error);
      setError('Erro ao processar categorização. Tente novamente.');
    } finally {
      setIsProcessing(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-green-100 text-green-800 border-green-200';
    }
  };

  const getPriorityLabel = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'Alta';
      case 'medium':
        return 'Média';
      default:
        return 'Baixa';
    }
  };

  // Se não tem acesso à funcionalidade
  if (!canUseFeature) {
    return (
      <Card className="p-6">
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <div className="bg-gray-100 rounded-full p-3">
              <div className="w-8 h-8 text-gray-400">
                {ICONS.LOCK}
              </div>
            </div>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Categorização Automática
          </h3>
          <p className="text-gray-600 mb-4">
            Esta funcionalidade está disponível apenas no plano Profissional.
          </p>
          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4 mb-4">
            <div className="flex items-center justify-center space-x-2 mb-2">
              <div className="w-5 h-5 text-purple-600">
                {ICONS.CROWN}
              </div>
              <span className="font-medium text-purple-900">Plano Profissional</span>
            </div>
            <p className="text-sm text-purple-700">
              Categorize automaticamente demandas e conteúdo com IA avançada.
            </p>
          </div>
          <Button variant="primary">
            Fazer Upgrade
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <div className="w-6 h-6 text-purple-600">
              {ICONS.TAGS}
            </div>
            <h2 className="text-xl font-semibold text-gray-900">Categorização Automática</h2>
          </div>
          {usage && usage.limit > 0 && (
            <div className="text-sm text-gray-500">
              {usage.used} / {usage.limit} categorizações este mês
            </div>
          )}
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Texto para categorização
            </label>
            <Textarea
              value={text}
              onChange={(e) => setText(e.target.value)}
              placeholder="Cole aqui o texto da demanda ou conteúdo que deseja categorizar automaticamente"
              rows={4}
              className="w-full"
            />
          </div>

          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          )}

          <Button
            onClick={handleCategorize}
            disabled={!text.trim() || isProcessing}
            isLoading={isProcessing}
            loadingText="Categorizando..."
            className="w-full"
          >
            <div className="w-4 h-4 mr-2">
              {ICONS.ZAP}
            </div>
            Categorizar Automaticamente
          </Button>
        </div>
      </Card>

      {result && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Resultado da Categorização</h3>
          
          <div className="space-y-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Texto analisado:</h4>
              <p className="text-gray-900">{result.text}</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-700">Categoria Principal</label>
                  <div className="mt-1">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                      Object.values(categories).find(cat => cat.name === result.category)?.color || 'bg-gray-100 text-gray-800'
                    }`}>
                      {result.category}
                    </span>
                  </div>
                </div>

                {result.subcategory && (
                  <div>
                    <label className="text-sm font-medium text-gray-700">Subcategoria</label>
                    <div className="mt-1">
                      <span className="inline-flex items-center px-2 py-1 rounded text-sm bg-gray-100 text-gray-800">
                        {result.subcategory}
                      </span>
                    </div>
                  </div>
                )}
              </div>

              <div className="space-y-3">
                <div>
                  <label className="text-sm font-medium text-gray-700">Prioridade Sugerida</label>
                  <div className="mt-1">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getPriorityColor(result.priority)}`}>
                      {getPriorityLabel(result.priority)}
                    </span>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-gray-700">Confiança</label>
                  <div className="mt-1">
                    <div className="flex items-center space-x-2">
                      <div className="flex-1 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-purple-600 h-2 rounded-full" 
                          style={{ width: `${result.confidence * 100}%` }}
                        ></div>
                      </div>
                      <span className="text-sm text-gray-600">
                        {(result.confidence * 100).toFixed(1)}%
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {result.suggestedTags.length > 0 && (
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">Tags Sugeridas</label>
                <div className="flex flex-wrap gap-2">
                  {result.suggestedTags.map((tag, index) => (
                    <span 
                      key={index}
                      className="inline-flex items-center px-2 py-1 rounded text-xs bg-blue-100 text-blue-800"
                    >
                      #{tag}
                    </span>
                  ))}
                </div>
              </div>
            )}

            <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-purple-900 mb-2">🤖 IA Profissional</h4>
              <p className="text-sm text-purple-700">
                Esta categorização foi gerada automaticamente pela IA. Use-a como base para 
                organizar e priorizar suas demandas de forma mais eficiente.
              </p>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default AutoCategorization;
