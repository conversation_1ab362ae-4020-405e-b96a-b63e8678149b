import { useState, useCallback, useEffect } from 'react';
import aiService from '../services/aiService';
import { AI_FEATURES, AI_ERROR_MESSAGES, AI_SUCCESS_MESSAGES } from '../services/aiConfig';
import type {
  AIAnalysisRequest,
  TextAnalysisResult,
  CategorizationResult,
  PredictiveAnalysisResult,
  SmartResponseSuggestion,
  AIBatchAnalysisRequest,
  AIBatchAnalysisResult,
  AIInsight,
  AIUsageMetrics
} from '../services/aiService';

interface UseAIState {
  loading: boolean;
  error: string | null;
  success: string | null;
}

interface UseAIReturn {
  // Estado
  loading: boolean;
  error: string | null;
  success: string | null;
  
  // Funcionalidades principais
  analyzeText: (request: AIAnalysisRequest) => Promise<TextAnalysisResult | null>;
  categorizeText: (request: AIAnalysisRequest) => Promise<CategorizationResult | null>;
  predictAnalysis: (request: AIAnalysisRequest) => Promise<PredictiveAnalysisResult | null>;
  generateResponses: (request: AIAnalysisRequest) => Promise<SmartResponseSuggestion[] | null>;
  completeAnalysis: (request: AIAnalysisRequest) => Promise<any | null>;
  
  // Funcionalidades avançadas
  batchAnalysis: (request: AIBatchAnalysisRequest) => Promise<AIBatchAnalysisResult | null>;
  generateInsights: (data: any) => Promise<AIInsight[] | null>;
  detectAnomalies: (data: any) => Promise<any[] | null>;
  getRecommendations: (userId: string) => Promise<any[] | null>;
  
  // Utilitários
  checkFeatureAvailability: (featureId: string) => boolean;
  getAvailableFeatures: () => any[];
  getUsageStatistics: () => any;
  checkUsageLimits: () => any;
  clearError: () => void;
  clearSuccess: () => void;
}

export const useAI = (): UseAIReturn => {
  const [state, setState] = useState<UseAIState>({
    loading: false,
    error: null,
    success: null
  });

  // Função auxiliar para gerenciar estado
  const updateState = useCallback((updates: Partial<UseAIState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  // Função auxiliar para executar operações de IA
  const executeAIOperation = useCallback(async <T>(
    operation: () => Promise<T>,
    successMessage?: string
  ): Promise<T | null> => {
    updateState({ loading: true, error: null, success: null });
    
    try {
      const result = await operation();
      updateState({ 
        loading: false, 
        success: successMessage || AI_SUCCESS_MESSAGES.ANALYSIS_COMPLETE 
      });
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : AI_ERROR_MESSAGES.API_ERROR;
      updateState({ loading: false, error: errorMessage });
      return null;
    }
  }, [updateState]);

  // Análise de texto
  const analyzeText = useCallback(async (request: AIAnalysisRequest): Promise<TextAnalysisResult | null> => {
    if (!aiService.isFeatureAvailable(AI_FEATURES.TEXT_ANALYSIS)) {
      updateState({ error: AI_ERROR_MESSAGES.FEATURE_NOT_AVAILABLE });
      return null;
    }

    return executeAIOperation(
      () => aiService.analyzeText(request),
      AI_SUCCESS_MESSAGES.ANALYSIS_COMPLETE
    );
  }, [executeAIOperation, updateState]);

  // Categorização
  const categorizeText = useCallback(async (request: AIAnalysisRequest): Promise<CategorizationResult | null> => {
    if (!aiService.isFeatureAvailable(AI_FEATURES.AUTO_CATEGORIZATION)) {
      updateState({ error: AI_ERROR_MESSAGES.FEATURE_NOT_AVAILABLE });
      return null;
    }

    return executeAIOperation(
      () => aiService.categorizeDemand(request),
      AI_SUCCESS_MESSAGES.CATEGORIZATION_COMPLETE
    );
  }, [executeAIOperation, updateState]);

  // Análise preditiva
  const predictAnalysis = useCallback(async (request: AIAnalysisRequest): Promise<PredictiveAnalysisResult | null> => {
    if (!aiService.isFeatureAvailable(AI_FEATURES.PREDICTIVE_ANALYTICS)) {
      updateState({ error: AI_ERROR_MESSAGES.FEATURE_NOT_AVAILABLE });
      return null;
    }

    return executeAIOperation(
      () => aiService.predictiveAnalysis(request),
      AI_SUCCESS_MESSAGES.PREDICTION_COMPLETE
    );
  }, [executeAIOperation, updateState]);

  // Geração de respostas
  const generateResponses = useCallback(async (request: AIAnalysisRequest): Promise<SmartResponseSuggestion[] | null> => {
    if (!aiService.isFeatureAvailable(AI_FEATURES.SMART_RESPONSES)) {
      updateState({ error: AI_ERROR_MESSAGES.FEATURE_NOT_AVAILABLE });
      return null;
    }

    return executeAIOperation(
      () => aiService.generateSmartResponse(request),
      AI_SUCCESS_MESSAGES.RESPONSE_GENERATED
    );
  }, [executeAIOperation, updateState]);

  // Análise completa
  const completeAnalysis = useCallback(async (request: AIAnalysisRequest): Promise<any | null> => {
    return executeAIOperation(
      () => aiService.completeAnalysis(request),
      'Análise completa finalizada'
    );
  }, [executeAIOperation]);

  // Análise em lote
  const batchAnalysis = useCallback(async (request: AIBatchAnalysisRequest): Promise<AIBatchAnalysisResult | null> => {
    return executeAIOperation(
      () => aiService.batchAnalysis(request),
      'Análise em lote concluída'
    );
  }, [executeAIOperation]);

  // Geração de insights
  const generateInsights = useCallback(async (data: any): Promise<AIInsight[] | null> => {
    if (!aiService.isFeatureAvailable(AI_FEATURES.PREDICTIVE_ANALYTICS)) {
      updateState({ error: AI_ERROR_MESSAGES.FEATURE_NOT_AVAILABLE });
      return null;
    }

    return executeAIOperation(
      () => aiService.generateInsights(data),
      'Insights gerados com sucesso'
    );
  }, [executeAIOperation, updateState]);

  // Detecção de anomalias
  const detectAnomalies = useCallback(async (data: any): Promise<any[] | null> => {
    if (!aiService.isFeatureAvailable(AI_FEATURES.PREDICTIVE_ANALYTICS)) {
      updateState({ error: AI_ERROR_MESSAGES.FEATURE_NOT_AVAILABLE });
      return null;
    }

    return executeAIOperation(
      () => aiService.detectAnomalies(data),
      'Anomalias detectadas'
    );
  }, [executeAIOperation, updateState]);

  // Recomendações personalizadas
  const getRecommendations = useCallback(async (userId: string): Promise<any[] | null> => {
    if (!aiService.isFeatureAvailable(AI_FEATURES.PREDICTIVE_ANALYTICS)) {
      updateState({ error: AI_ERROR_MESSAGES.FEATURE_NOT_AVAILABLE });
      return null;
    }

    return executeAIOperation(
      () => aiService.getPersonalizedRecommendations(userId),
      'Recomendações geradas'
    );
  }, [executeAIOperation, updateState]);

  // Verificação de disponibilidade de funcionalidades
  const checkFeatureAvailability = useCallback((featureId: string): boolean => {
    return aiService.isFeatureAvailable(featureId);
  }, []);

  // Obter funcionalidades disponíveis
  const getAvailableFeatures = useCallback(() => {
    return aiService.getAvailableFeatures();
  }, []);

  // Obter estatísticas de uso
  const getUsageStatistics = useCallback(() => {
    return aiService.getUsageStatistics();
  }, []);

  // Verificar limites de uso
  const checkUsageLimits = useCallback(() => {
    return aiService.checkUsageLimits();
  }, []);

  // Limpar erro
  const clearError = useCallback(() => {
    updateState({ error: null });
  }, [updateState]);

  // Limpar sucesso
  const clearSuccess = useCallback(() => {
    updateState({ success: null });
  }, [updateState]);

  // Auto-limpar mensagens após um tempo
  useEffect(() => {
    if (state.success) {
      const timer = setTimeout(() => {
        updateState({ success: null });
      }, 5000); // 5 segundos

      return () => clearTimeout(timer);
    }
  }, [state.success, updateState]);

  useEffect(() => {
    if (state.error) {
      const timer = setTimeout(() => {
        updateState({ error: null });
      }, 10000); // 10 segundos

      return () => clearTimeout(timer);
    }
  }, [state.error, updateState]);

  return {
    // Estado
    loading: state.loading,
    error: state.error,
    success: state.success,
    
    // Funcionalidades principais
    analyzeText,
    categorizeText,
    predictAnalysis,
    generateResponses,
    completeAnalysis,
    
    // Funcionalidades avançadas
    batchAnalysis,
    generateInsights,
    detectAnomalies,
    getRecommendations,
    
    // Utilitários
    checkFeatureAvailability,
    getAvailableFeatures,
    getUsageStatistics,
    checkUsageLimits,
    clearError,
    clearSuccess
  };
};

// Hook especializado para análise de demandas
export const useAIDemandAnalysis = () => {
  const ai = useAI();

  const analyzeDemand = useCallback(async (demandText: string, context?: any) => {
    const request: AIAnalysisRequest = {
      text: demandText,
      context,
      options: {
        includeEntities: true,
        includeTopics: true,
        includeSimilarCases: true,
        maxSimilarCases: 5
      }
    };

    return ai.completeAnalysis(request);
  }, [ai]);

  const categorizeDemand = useCallback(async (demandText: string, context?: any) => {
    const request: AIAnalysisRequest = {
      text: demandText,
      context
    };

    return ai.categorizeText(request);
  }, [ai]);

  const generateResponseSuggestions = useCallback(async (demandText: string, citizenContext?: any) => {
    const request: AIAnalysisRequest = {
      text: demandText,
      context: {
        citizenId: citizenContext?.id,
        previousInteractions: citizenContext?.interactions || 0,
        preferredCommunicationStyle: citizenContext?.communicationStyle
      }
    };

    return ai.generateResponses(request);
  }, [ai]);

  return {
    ...ai,
    analyzeDemand,
    categorizeDemand,
    generateResponseSuggestions
  };
};

// Hook para métricas e analytics de IA
export const useAIAnalytics = () => {
  const [metrics, setMetrics] = useState<AIUsageMetrics | null>(null);
  const [loading, setLoading] = useState(false);

  const refreshMetrics = useCallback(async () => {
    setLoading(true);
    try {
      const stats = aiService.getUsageStatistics();
      const limits = aiService.checkUsageLimits();
      const features = aiService.getAvailableFeatures();
      
      setMetrics({
        ...aiService.getMetrics(),
        usageStatistics: stats,
        usageLimits: limits,
        availableFeatures: features
      } as any);
    } catch (error) {
      console.error('Erro ao carregar métricas de IA:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    refreshMetrics();
  }, [refreshMetrics]);

  return {
    metrics,
    loading,
    refreshMetrics,
    clearCache: aiService.clearCache.bind(aiService),
    resetMonthlyUsage: aiService.resetMonthlyUsage.bind(aiService)
  };
};

export default useAI;