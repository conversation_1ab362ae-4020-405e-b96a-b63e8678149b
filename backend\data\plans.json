[{"id": "BASIC", "name": "Básico", "description": "Ideal para pequenas equipes e projetos iniciais", "price": {"monthly": 169.9, "yearly": 1834.92}, "features": {"maxUsers": 5, "maxDemands": 100, "maxCitizens": 500, "storageGB": 10, "bulkMessages": false, "socialMediaIntegration": false, "smsNotifications": false, "advancedReports": false, "customReports": false, "dataExport": true, "apiAccess": false, "webhooks": false, "supportLevel": "basic", "aiFeatures": []}, "enabled": true, "popular": false, "createdAt": "2025-06-06T15:07:43.261Z", "updatedAt": "2025-06-08T19:04:19.986Z"}, {"id": "STANDARD", "name": "Padrão", "description": "Para equipes em crescimento que precisam de mais recursos", "price": {"monthly": 259.9, "yearly": 2807.08}, "features": {"maxUsers": 15, "maxDemands": 500, "maxCitizens": 2000, "storageGB": 50, "bulkMessages": true, "socialMediaIntegration": true, "smsNotifications": false, "advancedReports": true, "customReports": false, "dataExport": true, "apiAccess": true, "webhooks": false, "supportLevel": "priority", "aiFeatures": [{"id": "text-analysis", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Análise automática de sentimentos em demandas", "category": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true}]}, "enabled": true, "popular": true, "createdAt": "2025-06-06T15:07:43.262Z", "updatedAt": "2025-06-06T15:48:41.126Z"}, {"id": "PROFESSIONAL", "name": "Profissional", "description": "Solução completa com IA avançada para grandes organizações", "price": {"monthly": 599.9, "yearly": 6478.92}, "features": {"maxUsers": -1, "maxDemands": -1, "maxCitizens": -1, "storageGB": 200, "bulkMessages": true, "socialMediaIntegration": true, "smsNotifications": true, "advancedReports": true, "customReports": true, "dataExport": true, "apiAccess": true, "webhooks": true, "supportLevel": "dedicated", "aiFeatures": [{"id": "text-analysis", "name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Análise automática de sentimentos em demandas", "category": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true}, {"id": "auto-categorization", "name": "Categorização Automática", "description": "Categorização inteligente de demandas", "category": "Automação", "enabled": true}, {"id": "predictive-analytics", "name": "Análise Preditiva", "description": "Previsões baseadas em dados históricos", "category": "<PERSON><PERSON><PERSON><PERSON>", "enabled": true}, {"id": "smart-responses", "name": "Respostas Inteligentes", "description": "Sugestões automáticas de respostas", "category": "Automação", "enabled": true}]}, "enabled": true, "popular": false, "badge": "IA Incluída", "createdAt": "2025-06-06T15:07:43.262Z", "updatedAt": "2025-06-06T15:49:01.266Z"}]