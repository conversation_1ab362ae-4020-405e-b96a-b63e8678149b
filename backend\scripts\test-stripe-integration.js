import 'dotenv/config';
import Stripe from 'stripe';

// Inicializar <PERSON>
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2023-10-16',
});

// IDs dos preços existentes
const PRICE_IDS = {
  basic_monthly: 'price_1RY5DHClUIoqY19kkU0D8pmf',
  standard_monthly: 'price_1RY5FSClUIoqY19kh9kieCaY',
  professional_monthly: 'price_1RY5GJClUIoqY19k1bCNt4lG'
};

async function testStripeIntegration() {
  console.log('🧪 Testando integração do Stripe...\n');

  try {
    // 1. Verificar conexão com Stripe
    console.log('1️⃣ Verificando conexão com Stripe...');
    const account = await stripe.accounts.retrieve();
    console.log(`✅ Conectado à conta: ${account.display_name || account.id}`);
    console.log(`   País: ${account.country}`);
    console.log(`   Moe<PERSON> padrão: ${account.default_currency?.toUpperCase()}`);
    console.log('');

    // 2. Listar produtos
    console.log('2️⃣ Verificando produtos...');
    const products = await stripe.products.list({ limit: 10 });
    console.log(`✅ Encontrados ${products.data.length} produtos:`);
    products.data.forEach(product => {
      console.log(`   - ${product.name} (${product.id})`);
    });
    console.log('');

    // 3. Verificar preços específicos
    console.log('3️⃣ Verificando preços configurados...');
    for (const [name, priceId] of Object.entries(PRICE_IDS)) {
      try {
        const price = await stripe.prices.retrieve(priceId);
        console.log(`✅ ${name}:`);
        console.log(`   ID: ${price.id}`);
        console.log(`   Valor: R$ ${(price.unit_amount / 100).toFixed(2)}`);
        console.log(`   Intervalo: ${price.recurring?.interval || 'único'}`);
        console.log(`   Produto: ${price.product}`);
        console.log('');
      } catch (error) {
        console.log(`❌ ${name}: ${error.message}`);
      }
    }

    // 4. Testar criação de sessão de checkout
    console.log('4️⃣ Testando criação de sessão de checkout...');
    try {
      const session = await stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        mode: 'subscription',
        line_items: [
          {
            price: PRICE_IDS.basic_monthly,
            quantity: 1,
          },
        ],
        success_url: 'https://example.com/success?session_id={CHECKOUT_SESSION_ID}',
        cancel_url: 'https://example.com/cancel',
        metadata: {
          planType: 'basic',
          billingCycle: 'monthly',
          source: 'test'
        }
      });

      console.log('✅ Sessão de checkout criada com sucesso!');
      console.log(`   ID: ${session.id}`);
      console.log(`   URL: ${session.url}`);
      console.log('');

      // Cancelar a sessão de teste
      await stripe.checkout.sessions.expire(session.id);
      console.log('✅ Sessão de teste cancelada');
      console.log('');

    } catch (error) {
      console.log(`❌ Erro ao criar sessão: ${error.message}`);
    }

    // 5. Verificar webhooks
    console.log('5️⃣ Verificando configuração de webhooks...');
    const webhooks = await stripe.webhookEndpoints.list();
    if (webhooks.data.length > 0) {
      console.log(`✅ Encontrados ${webhooks.data.length} webhooks:`);
      webhooks.data.forEach(webhook => {
        console.log(`   - ${webhook.url}`);
        console.log(`     Status: ${webhook.status}`);
        console.log(`     Eventos: ${webhook.enabled_events.join(', ')}`);
      });
    } else {
      console.log('⚠️ Nenhum webhook configurado');
      console.log('   Recomendação: Configure webhooks para receber eventos de pagamento');
    }

    console.log('\n🎉 Teste da integração Stripe concluído!');

  } catch (error) {
    console.error('❌ Erro durante o teste:', error.message);
    process.exit(1);
  }
}

// Executar teste
testStripeIntegration();
