import React, { useState } from 'react';
import { Card } from '../ui/Card';

export interface WidgetConfig {
  id: string;
  title: string;
  type: 'chart' | 'metric' | 'list' | 'calendar' | 'map';
  size: 'small' | 'medium' | 'large';
  position: { x: number; y: number };
  data?: any;
  settings?: Record<string, any>;
}

interface DashboardWidgetProps {
  config: WidgetConfig;
  onEdit?: (config: WidgetConfig) => void;
  onRemove?: (id: string) => void;
  onMove?: (id: string, position: { x: number; y: number }) => void;
  isEditing?: boolean;
  children: React.ReactNode;
}

export const DashboardWidget: React.FC<DashboardWidgetProps> = ({
  config,
  onEdit,
  onRemove,
  onMove,
  isEditing = false,
  children
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  const getSizeClasses = (size: string) => {
    switch (size) {
      case 'small':
        return 'col-span-1 row-span-1';
      case 'medium':
        return 'col-span-2 row-span-1';
      case 'large':
        return 'col-span-2 row-span-2';
      default:
        return 'col-span-1 row-span-1';
    }
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    if (!isEditing) return;
    
    setIsDragging(true);
    setDragStart({
      x: e.clientX - config.position.x,
      y: e.clientY - config.position.y
    });
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !isEditing) return;
    
    const newPosition = {
      x: e.clientX - dragStart.x,
      y: e.clientY - dragStart.y
    };
    
    onMove?.(config.id, newPosition);
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  return (
    <div
      className={`
        ${getSizeClasses(config.size)}
        ${isEditing ? 'cursor-move' : ''}
        ${isDragging ? 'opacity-75 z-50' : ''}
        transition-all duration-200
      `}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
    >
      <Card className={`h-full ${isEditing ? 'ring-2 ring-primary ring-opacity-50' : ''}`}>
        {/* Widget Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-neutral-medium">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-neutral-light">
            {config.title}
          </h3>
          
          {isEditing && (
            <div className="flex items-center space-x-2">
              <button
                type="button"
                onClick={() => onEdit?.(config)}
                className="p-1 text-gray-400 hover:text-gray-600 dark:text-neutral-medium dark:hover:text-neutral-light"
                title="Editar widget"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </button>
              
              <button
                type="button"
                onClick={() => onRemove?.(config.id)}
                className="p-1 text-red-400 hover:text-red-600"
                title="Remover widget"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            </div>
          )}
        </div>

        {/* Widget Content */}
        <div className="p-4 flex-1">
          {children}
        </div>
      </Card>
    </div>
  );
};

// Widget de Métrica
export const MetricWidget: React.FC<{
  title: string;
  value: number | string;
  change?: number;
  icon?: React.ReactElement;
  color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple';
}> = ({ title, value, change, icon, color = 'blue' }) => {
  const getColorClasses = (color: string) => {
    switch (color) {
      case 'green':
        return 'text-green-600 bg-green-100 dark:bg-green-900/20';
      case 'yellow':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20';
      case 'red':
        return 'text-red-600 bg-red-100 dark:bg-red-900/20';
      case 'purple':
        return 'text-purple-600 bg-purple-100 dark:bg-purple-900/20';
      default:
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900/20';
    }
  };

  return (
    <div className="flex items-center">
      {icon && (
        <div className={`p-3 rounded-lg ${getColorClasses(color)} mr-4`}>
          {React.isValidElement(icon) && React.cloneElement(icon as React.ReactElement<any>, { className: 'w-6 h-6' })}
        </div>
      )}
      
      <div className="flex-1">
        <p className="text-sm text-gray-600 dark:text-neutral-DEFAULT">{title}</p>
        <div className="flex items-baseline">
          <p className="text-2xl font-bold text-gray-900 dark:text-neutral-light">
            {value}
          </p>
          {change !== undefined && (
            <span className={`ml-2 text-sm ${
              change >= 0 
                ? 'text-green-600 dark:text-green-400' 
                : 'text-red-600 dark:text-red-400'
            }`}>
              {change >= 0 ? '+' : ''}{change}%
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

// Widget de Lista
export const ListWidget: React.FC<{
  items: Array<{
    id: string;
    title: string;
    subtitle?: string;
    status?: string;
    action?: () => void;
  }>;
  emptyMessage?: string;
}> = ({ items, emptyMessage = "Nenhum item encontrado" }) => {
  if (items.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500 dark:text-neutral-medium">
        {emptyMessage}
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {items.slice(0, 5).map((item) => (
        <div
          key={item.id}
          className={`
            flex items-center justify-between p-3 rounded-lg border border-gray-200 dark:border-neutral-medium
            ${item.action ? 'cursor-pointer hover:bg-gray-50 dark:hover:bg-neutral-darker' : ''}
          `}
          onClick={item.action}
        >
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 dark:text-neutral-light truncate">
              {item.title}
            </p>
            {item.subtitle && (
              <p className="text-xs text-gray-500 dark:text-neutral-medium truncate">
                {item.subtitle}
              </p>
            )}
          </div>
          
          {item.status && (
            <span className="ml-2 px-2 py-1 text-xs rounded-full bg-gray-100 dark:bg-neutral-darker text-gray-600 dark:text-neutral-DEFAULT">
              {item.status}
            </span>
          )}
        </div>
      ))}
      
      {items.length > 5 && (
        <div className="text-center pt-2">
          <span className="text-sm text-gray-500 dark:text-neutral-medium">
            +{items.length - 5} mais itens
          </span>
        </div>
      )}
    </div>
  );
};

// Widget de Gráfico Simples
export const ChartWidget: React.FC<{
  data: Array<{ label: string; value: number; color?: string }>;
  type?: 'bar' | 'pie' | 'line';
}> = ({ data, type = 'bar' }) => {
  const maxValue = Math.max(...data.map(d => d.value));

  if (type === 'bar') {
    return (
      <div className="space-y-3">
        {data.map((item, index) => (
          <div key={index} className="flex items-center">
            <div className="w-20 text-sm text-gray-600 dark:text-neutral-DEFAULT">
              {item.label}
            </div>
            <div className="flex-1 mx-3">
              <div className="bg-gray-200 dark:bg-neutral-medium rounded-full h-2">
                <div
                  className={`h-2 rounded-full ${item.color || 'bg-primary'}`}
                  style={{ width: `${(item.value / maxValue) * 100}%` }}
                />
              </div>
            </div>
            <div className="w-12 text-sm text-gray-900 dark:text-neutral-light text-right">
              {item.value}
            </div>
          </div>
        ))}
      </div>
    );
  }

  // Implementação simples para outros tipos de gráfico
  return (
    <div className="text-center py-8 text-gray-500 dark:text-neutral-medium">
      Gráfico {type} - Em desenvolvimento
    </div>
  );
};