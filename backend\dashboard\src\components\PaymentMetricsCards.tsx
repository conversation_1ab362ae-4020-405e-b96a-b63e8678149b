import React from 'react';
import { DollarSign, TrendingUp, Users, Calendar } from 'lucide-react';
import { PaymentMetrics } from '../types/payment.types';
import { formatCurrency } from '../utils/paymentUtils';

interface PaymentMetricsCardsProps {
  metrics: PaymentMetrics;
}

const PaymentMetricsCards: React.FC<PaymentMetricsCardsProps> = ({ metrics }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
      <div className="bg-white p-6 rounded-lg shadow">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">Receita Total</p>
            <p className="text-2xl font-bold text-green-600">
              {formatCurrency(metrics.totalRevenue)}
            </p>
          </div>
          <DollarSign className="h-8 w-8 text-green-600" />
        </div>
        <p className="text-xs text-gray-500 mt-2">
          +12.5% vs mês anterior
        </p>
      </div>

      <div className="bg-white p-6 rounded-lg shadow">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">MRR</p>
            <p className="text-2xl font-bold text-blue-600">
              {formatCurrency(metrics.monthlyRecurring)}
            </p>
          </div>
          <TrendingUp className="h-8 w-8 text-blue-600" />
        </div>
        <p className="text-xs text-gray-500 mt-2">
          Monthly Recurring Revenue
        </p>
      </div>

      <div className="bg-white p-6 rounded-lg shadow">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">Clientes Ativos</p>
            <p className="text-2xl font-bold text-purple-600">{metrics.totalCustomers}</p>
          </div>
          <Users className="h-8 w-8 text-purple-600" />
        </div>
        <p className="text-xs text-gray-500 mt-2">
          Taxa de sucesso: {metrics.successRate}%
        </p>
      </div>

      <div className="bg-white p-6 rounded-lg shadow">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600">ARPU</p>
            <p className="text-2xl font-bold text-orange-600">
              {formatCurrency(metrics.averageRevenuePerUser)}
            </p>
          </div>
          <Calendar className="h-8 w-8 text-orange-600" />
        </div>
        <p className="text-xs text-gray-500 mt-2">
          Average Revenue Per User
        </p>
      </div>
    </div>
  );
};

export default PaymentMetricsCards;
