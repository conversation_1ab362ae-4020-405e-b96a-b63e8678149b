#!/usr/bin/env node

/**
 * Script de desenvolvimento para o Promandato
 * Facilita o desenvolvimento com hot reload e logs melhorados
 */

import { spawn } from 'child_process';
import { watch } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

let serverProcess = null;

// Cores para logs
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  const timestamp = new Date().toLocaleTimeString();
  console.log(`${colors[color]}[${timestamp}] ${message}${colors.reset}`);
}

function startServer() {
  if (serverProcess) {
    log('Parando servidor anterior...', 'yellow');
    serverProcess.kill();
  }

  log('Iniciando servidor...', 'green');
  
  serverProcess = spawn('node', ['server.js'], {
    cwd: path.join(__dirname, '..'),
    stdio: 'inherit'
  });

  serverProcess.on('error', (err) => {
    log(`Erro no servidor: ${err.message}`, 'red');
  });

  serverProcess.on('exit', (code) => {
    if (code !== null && code !== 0) {
      log(`Servidor encerrado com código ${code}`, 'red');
    }
  });
}

function setupWatcher() {
  const watchPaths = [
    path.join(__dirname, '..', 'server.js'),
    path.join(__dirname, '..', 'config.js'),
    path.join(__dirname, '..', 'routes'),
    path.join(__dirname, '..', 'middleware'),
    path.join(__dirname, '..', 'models'),
    path.join(__dirname, '..', 'utils')
  ];

  log('Configurando watchers para hot reload...', 'cyan');

  watchPaths.forEach(watchPath => {
    try {
      watch(watchPath, { recursive: true }, (eventType, filename) => {
        if (filename && (filename.endsWith('.js') || filename.endsWith('.json'))) {
          log(`Arquivo alterado: ${filename}`, 'yellow');
          log('Reiniciando servidor...', 'blue');
          startServer();
        }
      });
      log(`Watching: ${watchPath}`, 'cyan');
    } catch (err) {
      log(`Erro ao configurar watcher para ${watchPath}: ${err.message}`, 'red');
    }
  });
}

function showBanner() {
  console.log(`
${colors.cyan}
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║  🏛️  PROMANDATO - MODO DESENVOLVIMENTO                       ║
║                                                              ║
║  🚀 Servidor com hot reload ativo                           ║
║  📊 Dashboard: http://localhost:3002                        ║
║  🔧 API: http://localhost:3002/api                          ║
║                                                              ║
║  📝 Logs em tempo real                                       ║
║  🔄 Reinicialização automática                              ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
${colors.reset}
  `);
}

function handleExit() {
  log('Encerrando modo desenvolvimento...', 'yellow');
  if (serverProcess) {
    serverProcess.kill();
  }
  process.exit(0);
}

// Configurar handlers de saída
process.on('SIGINT', handleExit);
process.on('SIGTERM', handleExit);
process.on('exit', handleExit);

// Iniciar modo desenvolvimento
showBanner();
setupWatcher();
startServer();

log('Modo desenvolvimento ativo! Pressione Ctrl+C para sair.', 'green');
