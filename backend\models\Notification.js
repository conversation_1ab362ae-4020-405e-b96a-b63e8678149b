import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';
import { v4 as uuidv4 } from 'uuid';
import config from '../config.js';

// ES module equivalent of __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class Notification {
  constructor(data) {
    this.id = data.id || uuidv4();
    this.userId = data.userId; // ID do usuário que deve receber a notificação
    this.title = data.title;
    this.message = data.message;
    this.type = data.type || 'info'; // 'info', 'success', 'warning', 'error'
    this.category = data.category || 'general'; // 'demand', 'event', 'system', 'general'
    this.read = data.read || false;
    this.actionUrl = data.actionUrl || null;
    this.actionLabel = data.actionLabel || null;
    this.metadata = data.metadata || {}; // Dados adicionais específicos do tipo
    this.createdAt = data.createdAt || new Date().toISOString();
    this.readAt = data.readAt || null;
    this.expiresAt = data.expiresAt || null; // Para notificações temporárias
  }

  // Métodos estáticos para manipulação do arquivo JSON
  static getNotificationsPath() {
    return config.database.notificationsPath || path.join(__dirname, '../data/notifications.json');
  }

  static async loadNotifications() {
    try {
      const notificationsPath = this.getNotificationsPath();
      
      // Criar arquivo se não existir
      if (!await fs.pathExists(notificationsPath)) {
        await fs.ensureDir(path.dirname(notificationsPath));
        await fs.writeJson(notificationsPath, []);
        return [];
      }

      const notifications = await fs.readJson(notificationsPath);
      return notifications.map(data => new Notification(data));
    } catch (error) {
      console.error('Erro ao carregar notificações:', error);
      return [];
    }
  }

  static async saveNotifications(notifications) {
    try {
      const notificationsPath = this.getNotificationsPath();
      await fs.ensureDir(path.dirname(notificationsPath));
      
      const data = notifications.map(notification => 
        notification instanceof Notification ? notification.toJSON() : notification
      );
      
      await fs.writeJson(notificationsPath, data, { spaces: 2 });
      return true;
    } catch (error) {
      console.error('Erro ao salvar notificações:', error);
      return false;
    }
  }

  // Buscar notificações por usuário
  static async findByUserId(userId, options = {}) {
    const notifications = await this.loadNotifications();
    let userNotifications = notifications.filter(n => n.userId === userId);

    // Filtrar por lidas/não lidas
    if (options.read !== undefined) {
      userNotifications = userNotifications.filter(n => n.read === options.read);
    }

    // Filtrar por categoria
    if (options.category) {
      userNotifications = userNotifications.filter(n => n.category === options.category);
    }

    // Filtrar por tipo
    if (options.type) {
      userNotifications = userNotifications.filter(n => n.type === options.type);
    }

    // Remover notificações expiradas
    const now = new Date();
    userNotifications = userNotifications.filter(n => 
      !n.expiresAt || new Date(n.expiresAt) > now
    );

    // Ordenar por data de criação (mais recentes primeiro)
    userNotifications.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    // Limitar quantidade se especificado
    if (options.limit) {
      userNotifications = userNotifications.slice(0, options.limit);
    }

    return userNotifications;
  }

  // Buscar notificação por ID
  static async findById(id) {
    const notifications = await this.loadNotifications();
    const notification = notifications.find(n => n.id === id);
    return notification || null;
  }

  // Criar nova notificação
  static async create(notificationData) {
    const notifications = await this.loadNotifications();
    const newNotification = new Notification(notificationData);
    
    notifications.push(newNotification);
    
    const saved = await this.saveNotifications(notifications);
    if (!saved) {
      throw new Error('Erro ao salvar notificação');
    }

    return newNotification;
  }

  // Criar notificações em lote
  static async createBatch(notificationsData) {
    const notifications = await this.loadNotifications();
    const newNotifications = notificationsData.map(data => new Notification(data));
    
    notifications.push(...newNotifications);
    
    const saved = await this.saveNotifications(notifications);
    if (!saved) {
      throw new Error('Erro ao salvar notificações em lote');
    }

    return newNotifications;
  }

  // Marcar como lida
  async markAsRead() {
    this.read = true;
    this.readAt = new Date().toISOString();
    return await this.save();
  }

  // Marcar como não lida
  async markAsUnread() {
    this.read = false;
    this.readAt = null;
    return await this.save();
  }

  // Salvar notificação
  async save() {
    const notifications = await Notification.loadNotifications();
    const index = notifications.findIndex(n => n.id === this.id);
    
    if (index !== -1) {
      notifications[index] = this;
    } else {
      notifications.push(this);
    }
    
    const saved = await Notification.saveNotifications(notifications);
    if (!saved) {
      throw new Error('Erro ao salvar notificação');
    }
    
    return this;
  }

  // Deletar notificação
  async delete() {
    const notifications = await Notification.loadNotifications();
    const filteredNotifications = notifications.filter(n => n.id !== this.id);
    return await Notification.saveNotifications(filteredNotifications);
  }

  // Marcar todas as notificações de um usuário como lidas
  static async markAllAsReadForUser(userId) {
    const notifications = await this.loadNotifications();
    const now = new Date().toISOString();
    
    let updated = false;
    notifications.forEach(notification => {
      if (notification.userId === userId && !notification.read) {
        notification.read = true;
        notification.readAt = now;
        updated = true;
      }
    });

    if (updated) {
      await this.saveNotifications(notifications);
    }

    return updated;
  }

  // Limpar notificações antigas
  static async cleanupExpired() {
    const notifications = await this.loadNotifications();
    const now = new Date();
    
    const validNotifications = notifications.filter(n => 
      !n.expiresAt || new Date(n.expiresAt) > now
    );

    if (validNotifications.length !== notifications.length) {
      await this.saveNotifications(validNotifications);
      return notifications.length - validNotifications.length;
    }

    return 0;
  }

  // Contar notificações não lidas de um usuário
  static async getUnreadCountForUser(userId) {
    const notifications = await this.findByUserId(userId, { read: false });
    return notifications.length;
  }

  // Converter para JSON
  toJSON() {
    return {
      id: this.id,
      userId: this.userId,
      title: this.title,
      message: this.message,
      type: this.type,
      category: this.category,
      read: this.read,
      actionUrl: this.actionUrl,
      actionLabel: this.actionLabel,
      metadata: this.metadata,
      createdAt: this.createdAt,
      readAt: this.readAt,
      expiresAt: this.expiresAt
    };
  }
}

export default Notification;
