{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "ESNext", "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "isolatedModules": true, "moduleDetection": "force", "noEmit": true, "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "noUncheckedSideEffectImports": true, "composite": true}, "include": ["vite.config.ts"]}