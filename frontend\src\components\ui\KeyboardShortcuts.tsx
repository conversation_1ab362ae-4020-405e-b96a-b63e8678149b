import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Modal } from './Modal';

interface Shortcut {
  key: string;
  description: string;
  action: () => void;
  category: string;
}

interface KeyboardShortcutsProps {
  onSearchFocus?: () => void;
}

export const KeyboardShortcuts: React.FC<KeyboardShortcutsProps> = ({ onSearchFocus }) => {
  const [isHelpOpen, setIsHelpOpen] = useState(false);
  const navigate = useNavigate();

  const shortcuts: Shortcut[] = [
    // Navegação
    {
      key: 'g d',
      description: 'Ir para Dashboard',
      action: () => navigate('/dashboard'),
      category: 'Navegação'
    },
    {
      key: 'g m',
      description: 'Ir para Demandas',
      action: () => navigate('/demands'),
      category: 'Navegação'
    },
    {
      key: 'g c',
      description: 'Ir para Cidadãos',
      action: () => navigate('/citizens'),
      category: 'Navegação'
    },
    {
      key: 'g a',
      description: 'Ir para Agenda',
      action: () => navigate('/agenda'),
      category: 'Navegação'
    },
    {
      key: 'g r',
      description: 'Ir para Relatórios',
      action: () => navigate('/reports'),
      category: 'Navegação'
    },
    {
      key: 'g s',
      description: 'Ir para Configurações',
      action: () => navigate('/settings'),
      category: 'Navegação'
    },
    // Ações
    {
      key: 'ctrl+k',
      description: 'Busca global',
      action: () => onSearchFocus?.(),
      category: 'Ações'
    },
    {
      key: 'n',
      description: 'Nova demanda',
      action: () => navigate('/demands/new'),
      category: 'Ações'
    },
    {
      key: 'shift+n',
      description: 'Novo cidadão',
      action: () => navigate('/citizens/new'),
      category: 'Ações'
    },
    {
      key: 'ctrl+shift+n',
      description: 'Novo evento',
      action: () => navigate('/agenda/new'),
      category: 'Ações'
    },
    // Ajuda
    {
      key: '?',
      description: 'Mostrar atalhos',
      action: () => setIsHelpOpen(true),
      category: 'Ajuda'
    },
    {
      key: 'esc',
      description: 'Fechar modais/menus',
      action: () => setIsHelpOpen(false),
      category: 'Ajuda'
    }
  ];

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Ignorar se estiver digitando em um input
    if (event.target instanceof HTMLInputElement || 
        event.target instanceof HTMLTextAreaElement ||
        event.target instanceof HTMLSelectElement) {
      return;
    }

    const key = event.key.toLowerCase();
    const isCtrl = event.ctrlKey;
    const isShift = event.shiftKey;

    // Construir string da combinação de teclas
    let keyCombo = '';
    if (isCtrl) keyCombo += 'ctrl+';
    if (isShift) keyCombo += 'shift+';
    keyCombo += key;

    // Buscar atalho correspondente
    const shortcut = shortcuts.find(s => s.key === keyCombo);
    
    if (shortcut) {
      event.preventDefault();
      shortcut.action();
      return;
    }

    // Atalhos especiais para sequências (como 'g d')
    if (!isCtrl && !isShift) {
      handleSequenceShortcut(key);
    }
  }, [shortcuts, onSearchFocus, navigate]);

  // Estado para atalhos em sequência (como 'g d')
  const [sequenceBuffer, setSequenceBuffer] = useState('');
  const [sequenceTimeout, setSequenceTimeout] = useState<NodeJS.Timeout | null>(null);

  const handleSequenceShortcut = (key: string) => {
    // Limpar timeout anterior
    if (sequenceTimeout) {
      clearTimeout(sequenceTimeout);
    }

    const newBuffer = sequenceBuffer + key;
    setSequenceBuffer(newBuffer);

    // Buscar atalho de sequência
    const sequenceShortcut = shortcuts.find(s => s.key === newBuffer);
    
    if (sequenceShortcut) {
      sequenceShortcut.action();
      setSequenceBuffer('');
      return;
    }

    // Definir timeout para limpar buffer
    const timeout = setTimeout(() => {
      setSequenceBuffer('');
    }, 1000);
    
    setSequenceTimeout(timeout);
  };

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      if (sequenceTimeout) {
        clearTimeout(sequenceTimeout);
      }
    };
  }, [handleKeyDown, sequenceTimeout]);

  const groupedShortcuts = shortcuts.reduce((acc, shortcut) => {
    if (!acc[shortcut.category]) {
      acc[shortcut.category] = [];
    }
    acc[shortcut.category].push(shortcut);
    return acc;
  }, {} as Record<string, Shortcut[]>);

  const formatKey = (key: string) => {
    return key
      .split('+')
      .map(k => {
        switch (k) {
          case 'ctrl': return 'Ctrl';
          case 'shift': return 'Shift';
          case 'alt': return 'Alt';
          case ' ': return 'Espaço';
          default: return k.toUpperCase();
        }
      })
      .join(' + ');
  };

  return (
    <>
      {/* Indicador de sequência ativa */}
      {sequenceBuffer && (
        <div className="fixed top-4 right-4 bg-black bg-opacity-75 text-white px-3 py-2 rounded-lg text-sm font-mono z-50">
          {sequenceBuffer}...
        </div>
      )}

      {/* Modal de ajuda */}
      <Modal
        isOpen={isHelpOpen}
        onClose={() => setIsHelpOpen(false)}
        title="Atalhos de Teclado"
        size="lg"
      >
        <div className="space-y-6">
          {Object.entries(groupedShortcuts).map(([category, categoryShortcuts]) => (
            <div key={category}>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-neutral-light mb-3">
                {category}
              </h3>
              <div className="space-y-2">
                {categoryShortcuts.map((shortcut, index) => (
                  <div key={index} className="flex justify-between items-center py-2 px-3 bg-gray-50 dark:bg-neutral-darker rounded-lg">
                    <span className="text-gray-700 dark:text-neutral-DEFAULT">
                      {shortcut.description}
                    </span>
                    <kbd className="px-2 py-1 bg-white dark:bg-neutral-dark border border-gray-300 dark:border-neutral-medium rounded text-sm font-mono text-gray-600 dark:text-neutral-light">
                      {formatKey(shortcut.key)}
                    </kbd>
                  </div>
                ))}
              </div>
            </div>
          ))}
          
          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
              Dicas:
            </h4>
            <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
              <li>• Para atalhos de sequência (como "g d"), digite as teclas rapidamente</li>
              <li>• Atalhos não funcionam quando você está digitando em campos de texto</li>
              <li>• Pressione "?" a qualquer momento para ver esta ajuda</li>
              <li>• Pressione "Esc" para fechar modais e menus</li>
            </ul>
          </div>
        </div>
      </Modal>
    </>
  );
};

// Hook para usar atalhos de teclado em componentes específicos
export const useKeyboardShortcut = (
  key: string,
  callback: () => void,
  deps: React.DependencyList = []
) => {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ignorar se estiver digitando em um input
      if (event.target instanceof HTMLInputElement || 
          event.target instanceof HTMLTextAreaElement ||
          event.target instanceof HTMLSelectElement) {
        return;
      }

      const pressedKey = event.key.toLowerCase();
      const isCtrl = event.ctrlKey;
      const isShift = event.shiftKey;
      const isAlt = event.altKey;

      // Construir string da combinação
      let keyCombo = '';
      if (isCtrl) keyCombo += 'ctrl+';
      if (isShift) keyCombo += 'shift+';
      if (isAlt) keyCombo += 'alt+';
      keyCombo += pressedKey;

      if (keyCombo === key.toLowerCase()) {
        event.preventDefault();
        callback();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, deps);
};