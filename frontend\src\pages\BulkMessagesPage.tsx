import React, { useState, useEffect, useCallback } from 'react';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Modal } from '../components/ui/Modal';
import { Input, Textarea } from '../components/ui/Input';
import { ICONS } from '../constants';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';
import { useAuth } from '../hooks/useAuth';
import { getCitizens } from '../services/firebaseService';
import { Citizen } from '../types';

const BulkMessagesPage: React.FC = () => {
  const [citizens, setCitizens] = useState<Citizen[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCitizens, setSelectedCitizens] = useState<Set<string>>(new Set());
  const [selectAll, setSelectAll] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [messageTitle, setMessageTitle] = useState('');
  const [messageBody, setMessageBody] = useState('');
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);
  const [isSending, setIsSending] = useState(false);

  const { currentUser } = useAuth();

  const fetchCitizens = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const fetchedCitizens = await getCitizens();
      setCitizens(fetchedCitizens);
    } catch (err) {
      console.error("Failed to fetch citizens:", err);
      setError("Falha ao carregar cidadãos. Tente novamente.");
    } finally {
      setIsLoading(false);
    }
  }, [currentUser]);

  useEffect(() => {
    fetchCitizens();
  }, [fetchCitizens]);

  // Filtrar cidadãos com base na busca
  const filteredCitizens = citizens.filter(citizen => {
    return searchTerm === '' || 
      citizen.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (citizen.email && citizen.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (citizen.phone && citizen.phone.includes(searchTerm));
  });

  // Manipular seleção de todos os cidadãos
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedCitizens(new Set());
    } else {
      setSelectedCitizens(new Set(filteredCitizens.map(citizen => citizen.id)));
    }
    setSelectAll(!selectAll);
  };

  // Manipular seleção individual de cidadão
  const handleSelectCitizen = (id: string) => {
    const newSelected = new Set(selectedCitizens);
    if (newSelected.has(id)) {
      newSelected.delete(id);
    } else {
      newSelected.add(id);
    }
    setSelectedCitizens(newSelected);
    
    // Atualizar estado de "selecionar todos" se necessário
    if (newSelected.size === filteredCitizens.length) {
      setSelectAll(true);
    } else {
      setSelectAll(false);
    }
  };

  // Abrir modal de pré-visualização
  const handleOpenPreviewModal = () => {
    if (!messageTitle.trim()) {
      setError("O título da mensagem é obrigatório.");
      return;
    }
    
    if (!messageBody.trim()) {
      setError("O corpo da mensagem é obrigatório.");
      return;
    }
    
    if (selectedCitizens.size === 0) {
      setError("Selecione pelo menos um cidadão para enviar a mensagem.");
      return;
    }
    
    setError(null);
    setIsPreviewModalOpen(true);
  };

  // Fechar modal de pré-visualização
  const handleClosePreviewModal = () => {
    setIsPreviewModalOpen(false);
  };

  // Enviar mensagens
  const handleSendMessages = async () => {
    setIsSending(true);
    setError(null);
    
    try {
      // Aqui seria implementada a lógica real de envio
      // Por exemplo, chamar uma API ou serviço Firebase
      
      // Simulação de envio
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Limpar formulário após envio
      setMessageTitle('');
      setMessageBody('');
      setSelectedCitizens(new Set());
      setSelectAll(false);
      
      alert(`Mensagem enviada com sucesso para ${selectedCitizens.size} cidadão(s)!`);
      handleClosePreviewModal();
    } catch (err) {
      console.error("Failed to send messages:", err);
      setError("Falha ao enviar mensagens. Tente novamente.");
    } finally {
      setIsSending(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-neutral-dark dark:text-neutral-light">Mensagens em Massa</h1>
        <Button 
          onClick={handleOpenPreviewModal} 
          leftIcon={ICONS.MAIL}
          disabled={selectedCitizens.size === 0 || !messageTitle.trim() || !messageBody.trim()}
        >
          Enviar Mensagens
        </Button>
      </div>

      {error && <p className="text-red-500 dark:text-red-400 bg-red-100 dark:bg-red-900 dark:bg-opacity-30 p-3 rounded-md">{error}</p>}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Coluna da Esquerda - Seleção de Cidadãos */}
        <Card title="Selecionar Destinatários">
          <div className="space-y-4">
            <Input 
              placeholder="Buscar por nome, email, telefone..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />

            <div className="flex items-center justify-between mb-2 px-2">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="selectAll"
                  checked={selectAll}
                  onChange={handleSelectAll}
                  className="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary dark:border-neutral-medium dark:bg-neutral-dark"
                />
                <label htmlFor="selectAll" className="ml-2 text-sm text-neutral-dark dark:text-neutral-light">
                  Selecionar Todos
                </label>
              </div>
              <span className="text-sm text-gray-500 dark:text-neutral-DEFAULT">
                {selectedCitizens.size} de {filteredCitizens.length} selecionados
              </span>
            </div>

            {isLoading ? (
              <div className="flex justify-center items-center py-10">
                <LoadingSpinner size="lg" />
              </div>
            ) : (
              <div className="overflow-y-auto max-h-96 border border-gray-200 dark:border-neutral-medium rounded-md">
                {filteredCitizens.length > 0 ? (
                  <ul className="divide-y divide-gray-200 dark:divide-neutral-dark">
                    {filteredCitizens.map(citizen => (
                      <li key={citizen.id} className="p-3 hover:bg-gray-50 dark:hover:bg-neutral-dark">
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            id={`citizen-${citizen.id}`}
                            checked={selectedCitizens.has(citizen.id)}
                            onChange={() => handleSelectCitizen(citizen.id)}
                            className="h-4 w-4 text-primary border-gray-300 rounded focus:ring-primary dark:border-neutral-medium dark:bg-neutral-dark"
                          />
                          <label htmlFor={`citizen-${citizen.id}`} className="ml-3 block cursor-pointer">
                            <span className="block text-sm font-medium text-neutral-dark dark:text-neutral-light">{citizen.fullName}</span>
                            <span className="block text-xs text-gray-500 dark:text-neutral-DEFAULT">
                              {citizen.email || 'Sem email'} | {citizen.phone || 'Sem telefone'}
                            </span>
                          </label>
                        </div>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <p className="p-4 text-center text-gray-500 dark:text-neutral-DEFAULT">
                    Nenhum cidadão encontrado com os filtros aplicados.
                  </p>
                )}
              </div>
            )}
          </div>
        </Card>

        {/* Coluna da Direita - Composição da Mensagem */}
        <Card title="Compor Mensagem">
          <div className="space-y-4">
            <Input
              label="Título da Mensagem"
              value={messageTitle}
              onChange={(e) => setMessageTitle(e.target.value)}
              placeholder="Ex: Convite para Audiência Pública"
              required
            />
            
            <Textarea
              label="Corpo da Mensagem"
              value={messageBody}
              onChange={(e) => setMessageBody(e.target.value)}
              placeholder="Digite aqui o conteúdo da sua mensagem..."
              rows={10}
              required
            />
          </div>
        </Card>
      </div>

      {/* Modal de Pré-visualização */}
      <Modal 
        isOpen={isPreviewModalOpen} 
        onClose={handleClosePreviewModal} 
        title="Pré-visualização da Mensagem"
        size="lg"
      >
        <div className="space-y-4">
          <div className="border p-4 rounded-md bg-white dark:bg-neutral-darker">
            <h2 className="text-xl font-bold mb-4">{messageTitle}</h2>
            <div className="whitespace-pre-line">{messageBody}</div>
          </div>
          
          <div className="flex justify-between items-center">
            <p className="text-sm text-gray-500 dark:text-neutral-DEFAULT">
              Esta mensagem será enviada para {selectedCitizens.size} cidadão(s).
            </p>
            <div className="space-x-2">
              <Button variant="outline" onClick={handleClosePreviewModal}>
                Cancelar
              </Button>
              <Button 
                onClick={handleSendMessages} 
                isLoading={isSending}
                loadingText="Enviando..."
              >
                Confirmar Envio
              </Button>
            </div>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default BulkMessagesPage;









