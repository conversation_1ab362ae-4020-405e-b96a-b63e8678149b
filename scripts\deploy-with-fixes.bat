@echo off
echo ========================================
echo   DEPLOY COMPLETO COM CORREÇÕES
echo ========================================
echo.

echo [1/8] Verificando dependencias...
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo Node.js nao encontrado!
    pause
    exit /b 1
)

where firebase >nul 2>&1
if %errorlevel% neq 0 (
    echo Firebase CLI nao encontrado!
    echo Instale com: npm install -g firebase-tools
    pause
    exit /b 1
)

where gcloud >nul 2>&1
if %errorlevel% neq 0 (
    echo Google Cloud CLI nao encontrado!
    echo Instale em: https://cloud.google.com/sdk/docs/install
    pause
    exit /b 1
)

echo Todas as dependencias encontradas!
echo.

echo [2/8] Configurando projeto Firebase...
firebase use promandato-9a4cf

echo.
echo [3/8] Configurando projeto Google Cloud...
gcloud config set project promandato-9a4cf

echo.
echo [4/8] Fazendo deploy do backend com CORS corrigido...
cd backend

echo Criando variaveis de ambiente para producao...
echo NODE_ENV=production > .env.deploy
echo PORT=8080 >> .env.deploy
echo FRONTEND_URL=https://promandato-backend-2h6tsoanrq-rj.a.run.app,https://promandato-9a4cf.web.app,https://promandato-9a4cf.firebaseapp.com >> .env.deploy
echo CORS_ORIGIN=https://promandato-backend-2h6tsoanrq-rj.a.run.app,https://promandato-9a4cf.web.app,https://promandato-9a4cf.firebaseapp.com >> .env.deploy
echo DEBUG_CORS=true >> .env.deploy

echo Fazendo deploy do backend...
gcloud run deploy promandato-backend ^
  --source . ^
  --platform managed ^
  --region southamerica-east1 ^
  --allow-unauthenticated ^
  --port 8080 ^
  --memory 1Gi ^
  --cpu 1 ^
  --min-instances 0 ^
  --max-instances 10 ^
  --set-env-vars NODE_ENV=production,PORT=8080,DEBUG_CORS=true ^
  --set-env-vars FRONTEND_URL="https://promandato-backend-2h6tsoanrq-rj.a.run.app,https://promandato-9a4cf.web.app,https://promandato-9a4cf.firebaseapp.com" ^
  --set-env-vars CORS_ORIGIN="https://promandato-backend-2h6tsoanrq-rj.a.run.app,https://promandato-9a4cf.web.app,https://promandato-9a4cf.firebaseapp.com"

if %errorlevel% neq 0 (
    echo ERRO: Deploy do backend falhou!
    pause
    exit /b 1
)

echo Backend deployado com sucesso!
cd ..

echo.
echo [5/8] Preparando frontend com configuracoes corrigidas...
cd frontend

echo Instalando dependencias do frontend...
npm install

echo Criando arquivo .env.production com URLs corretas...
echo VITE_API_URL=https://promandato-backend-************.southamerica-east1.run.app/api > .env.production
echo VITE_AI_API_URL=https://promandato-backend-************.southamerica-east1.run.app/api/ai >> .env.production
echo VITE_FIREBASE_PROJECT_ID=promandato-9a4cf >> .env.production
echo VITE_FIREBASE_AUTH_DOMAIN=promandato-9a4cf.firebaseapp.com >> .env.production
echo VITE_FIREBASE_STORAGE_BUCKET=promandato-9a4cf.firebasestorage.app >> .env.production
echo VITE_ENVIRONMENT=production >> .env.production
echo VITE_NODE_ENV=production >> .env.production
echo VITE_FRONTEND_URL=https://promandato-9a4cf.web.app >> .env.production
echo VITE_LANDING_URL=https://promandato-9a4cf.web.app >> .env.production

echo Fazendo build do frontend...
npm run build

if %errorlevel% neq 0 (
    echo ERRO: Build do frontend falhou!
    pause
    exit /b 1
)

echo.
echo [6/8] Fazendo deploy do frontend...
firebase deploy --only hosting:frontend

if %errorlevel% neq 0 (
    echo ERRO: Deploy do frontend falhou!
    pause
    exit /b 1
)

cd ..

echo.
echo [7/8] Fazendo deploy da landing page...
firebase deploy --only hosting:default

if %errorlevel% neq 0 (
    echo ERRO: Deploy da landing page falhou!
    pause
    exit /b 1
)

echo.
echo [8/8] Limpando arquivos temporarios...
del backend\.env.deploy 2>nul

echo.
echo ========================================
echo       DEPLOY CONCLUIDO COM SUCESSO!
echo ========================================
echo.
echo URLs da aplicacao:
echo.
echo 🌐 Landing Page: https://promandato-9a4cf.web.app
echo 🖥️  Frontend App: https://promandato-backend-2h6tsoanrq-rj.a.run.app
echo 🔧 Backend API: https://promandato-backend-************.southamerica-east1.run.app
echo.
echo 🔍 Endpoints de teste:
echo - Health Check: https://promandato-backend-************.southamerica-east1.run.app/api/health
echo - CORS Debug: https://promandato-backend-************.southamerica-east1.run.app/api/cors-debug
echo.
echo 🛠️  Para resolver problemas do frontend:
echo Abra: scripts/fix-frontend-issues.html
echo.
echo ✅ Problemas corrigidos:
echo - CORS configurado para todas as URLs de producao
echo - Service Worker corrigido para nao cachear POST requests
echo - URLs de API atualizadas em todos os servicos
echo - Configuracoes de ambiente de producao aplicadas
echo.

pause
