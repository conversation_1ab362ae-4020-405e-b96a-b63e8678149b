@echo off
echo ========================================
echo   LIMPEZA COMPLETA GITHUB ACTIONS
echo ========================================
echo.

echo [1/8] Fazendo backup dos workflows atuais...
if exist ".github\workflows" (
    if not exist "backup" mkdir backup
    xcopy .github\workflows backup\workflows\ /E /I /Y
    echo Backup criado em: backup\workflows\
)

echo.
echo [2/8] Removendo workflows existentes...
if exist ".github\workflows" (
    rmdir /S /Q .github\workflows
    echo Workflows removidos.
)

echo.
echo [3/8] Verificando conexoes GitHub no Firebase...
echo Removendo qualquer conexao automatica...

firebase hosting:github:disconnect --project promandato-9a4cf 2>nul
firebase hosting:github:disconnect --project promandato-landing 2>nul

echo.
echo [4/8] Criando estrutura limpa...
mkdir .github\workflows

echo.
echo [5/8] Criando workflow manual e limpo...

echo name: Deploy ProMandato Landing > .github\workflows\deploy-landing.yml
echo. >> .github\workflows\deploy-landing.yml
echo on: >> .github\workflows\deploy-landing.yml
echo   push: >> .github\workflows\deploy-landing.yml
echo     branches: [main] >> .github\workflows\deploy-landing.yml
echo     paths: ['landingpage/**'] >> .github\workflows\deploy-landing.yml
echo   workflow_dispatch: >> .github\workflows\deploy-landing.yml
echo. >> .github\workflows\deploy-landing.yml
echo jobs: >> .github\workflows\deploy-landing.yml
echo   deploy: >> .github\workflows\deploy-landing.yml
echo     name: Deploy to Firebase >> .github\workflows\deploy-landing.yml
echo     runs-on: ubuntu-latest >> .github\workflows\deploy-landing.yml
echo. >> .github\workflows\deploy-landing.yml
echo     steps: >> .github\workflows\deploy-landing.yml
echo     - name: Checkout Repository >> .github\workflows\deploy-landing.yml
echo       uses: actions/checkout@v4 >> .github\workflows\deploy-landing.yml
echo. >> .github\workflows\deploy-landing.yml
echo     - name: Setup Node.js >> .github\workflows\deploy-landing.yml
echo       uses: actions/setup-node@v4 >> .github\workflows\deploy-landing.yml
echo       with: >> .github\workflows\deploy-landing.yml
echo         node-version: '18' >> .github\workflows\deploy-landing.yml
echo. >> .github\workflows\deploy-landing.yml
echo     - name: Install Firebase CLI >> .github\workflows\deploy-landing.yml
echo       run: npm install -g firebase-tools >> .github\workflows\deploy-landing.yml
echo. >> .github\workflows\deploy-landing.yml
echo     - name: Verify Firebase Project >> .github\workflows\deploy-landing.yml
echo       run: firebase use promandato-9a4cf >> .github\workflows\deploy-landing.yml
echo       env: >> .github\workflows\deploy-landing.yml
echo         FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }} >> .github\workflows\deploy-landing.yml
echo. >> .github\workflows\deploy-landing.yml
echo     - name: Deploy to Firebase Hosting >> .github\workflows\deploy-landing.yml
echo       run: firebase deploy --only hosting --project promandato-9a4cf >> .github\workflows\deploy-landing.yml
echo       env: >> .github\workflows\deploy-landing.yml
echo         FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }} >> .github\workflows\deploy-landing.yml

echo.
echo [6/8] Verificando arquivo firebase.json...
if not exist "firebase.json" (
    echo Criando firebase.json...
    echo { > firebase.json
    echo   "hosting": { >> firebase.json
    echo     "public": "landingpage", >> firebase.json
    echo     "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], >> firebase.json
    echo     "rewrites": [{"source": "**", "destination": "/index.html"}] >> firebase.json
    echo   } >> firebase.json
    echo } >> firebase.json
)

echo.
echo [7/8] Verificando arquivo .firebaserc...
if not exist ".firebaserc" (
    echo Criando .firebaserc...
    echo { > .firebaserc
    echo   "projects": { >> .firebaserc
    echo     "default": "promandato-9a4cf" >> .firebaserc
    echo   } >> .firebaserc
    echo } >> .firebaserc
)

echo.
echo [8/8] Gerando token Firebase...
echo.
echo ========================================
echo        COPIE O TOKEN ABAIXO!
echo ========================================
echo.
echo Este token sera usado no GitHub Secret: FIREBASE_TOKEN
echo.

firebase login:ci

echo.
echo ========================================
echo         CONFIGURACAO CONCLUIDA!
echo ========================================
echo.
echo Arquivos criados:
echo - .github/workflows/deploy-landing.yml (workflow limpo)
echo - firebase.json (configuracao hosting)
echo - .firebaserc (projeto correto)
echo.
echo PROXIMOS PASSOS:
echo.
echo 1. COPIE o token Firebase acima
echo 2. Va para GitHub > Settings > Secrets > Actions
echo 3. Crie/atualize o secret:
echo    Nome: FIREBASE_TOKEN
echo    Valor: [cole o token]
echo.
echo 4. Teste o deploy:
echo    git add .
echo    git commit -m "fix: workflow limpo"
echo    git push origin main
echo.
echo 5. Ou execute manualmente:
echo    GitHub > Actions > Deploy ProMandato Landing > Run workflow
echo.

pause
