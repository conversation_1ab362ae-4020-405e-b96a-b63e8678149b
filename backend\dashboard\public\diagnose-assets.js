// Script de diagnóstico para verificar assets e Service Worker

(async function diagnoseAssets() {
  console.log('🔍 Iniciando diagnóstico de assets...\n');
  
  // 1. Verificar Service Worker
  console.log('📋 Service Worker Status:');
  if ('serviceWorker' in navigator) {
    try {
      const registrations = await navigator.serviceWorker.getRegistrations();
      if (registrations.length > 0) {
        registrations.forEach((reg, index) => {
          console.log(`  ${index + 1}. Scope: ${reg.scope}`);
          console.log(`     State: ${reg.active?.state || 'inactive'}`);
        });
      } else {
        console.log('  ✅ Nenhum Service Worker registrado');
      }
    } catch (error) {
      console.log('  ❌ Erro ao verificar Service Worker:', error);
    }
  } else {
    console.log('  ⚠️ Service Worker não suportado');
  }
  
  // 2. Verificar caches
  console.log('\n📋 Cache Status:');
  if ('caches' in window) {
    try {
      const cacheNames = await caches.keys();
      if (cacheNames.length > 0) {
        for (let cacheName of cacheNames) {
          const cache = await caches.open(cacheName);
          const keys = await cache.keys();
          console.log(`  📦 ${cacheName}: ${keys.length} items`);
          
          // Mostrar alguns itens do cache
          if (keys.length > 0) {
            keys.slice(0, 3).forEach(request => {
              console.log(`    - ${request.url}`);
            });
            if (keys.length > 3) {
              console.log(`    ... e mais ${keys.length - 3} itens`);
            }
          }
        }
      } else {
        console.log('  ✅ Nenhum cache encontrado');
      }
    } catch (error) {
      console.log('  ❌ Erro ao verificar caches:', error);
    }
  } else {
    console.log('  ⚠️ Cache API não suportada');
  }
  
  // 3. Verificar assets específicos
  console.log('\n📋 Asset Availability:');
  const assetsToCheck = [
    '/assets/critical.css',
    '/assets/index-Dj5g2f-X.js',
    '/assets/react-vendor-CMDrCEic.js',
    '/assets/vendor-3CIACNA8.js',
    '/assets/user-management-Dw9qCxEp.js',
    '/assets/firebase-vendor-BhatmcAL.js'
  ];
  
  for (let asset of assetsToCheck) {
    try {
      const response = await fetch(asset, { method: 'HEAD' });
      const status = response.ok ? '✅' : '❌';
      console.log(`  ${status} ${asset} (${response.status})`);
    } catch (error) {
      console.log(`  ❌ ${asset} (Network Error)`);
    }
  }
  
  // 4. Verificar localStorage
  console.log('\n📋 LocalStorage Status:');
  try {
    const keys = Object.keys(localStorage);
    if (keys.length > 0) {
      console.log(`  📦 ${keys.length} itens no localStorage:`);
      keys.forEach(key => {
        const value = localStorage.getItem(key);
        const preview = value.length > 50 ? value.substring(0, 50) + '...' : value;
        console.log(`    - ${key}: ${preview}`);
      });
    } else {
      console.log('  ✅ localStorage vazio');
    }
  } catch (error) {
    console.log('  ❌ Erro ao verificar localStorage:', error);
  }
  
  // 5. Verificar console errors
  console.log('\n📋 Console Errors:');
  const originalError = console.error;
  const errors = [];
  
  console.error = function(...args) {
    errors.push(args.join(' '));
    originalError.apply(console, args);
  };
  
  setTimeout(() => {
    console.error = originalError;
    if (errors.length > 0) {
      console.log(`  ❌ ${errors.length} erro(s) detectado(s):`);
      errors.forEach((error, index) => {
        console.log(`    ${index + 1}. ${error}`);
      });
    } else {
      console.log('  ✅ Nenhum erro detectado');
    }
  }, 1000);
  
  console.log('\n✅ Diagnóstico completo!');
  console.log('💡 Para limpar cache e resolver problemas, execute: clearSWCache()');
})();

// Função para executar manualmente
window.diagnoseAssets = diagnoseAssets;

console.log('💡 Para executar diagnóstico manualmente, execute: diagnoseAssets()');
