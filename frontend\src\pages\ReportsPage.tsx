import React, { useState, useEffect } from 'react';
import { Card } from '../components/ui/Card';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/Input';
import { generateFullReport } from '../services/firebaseService';
import { ReportData } from '../types';
import { useReportHelpers } from '../utils/useReportHelpers';
import { exportToPDF, exportToExcel } from '../utils/exportHelpers';
import { 
  ResponsiveContainer, 
  PieChart, 
  Pie, 
  Legend, 
  Tooltip,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  LineChart,
  Line,
  ComposedChart
} from 'recharts';

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042'];
const RADIAN = Math.PI / 180;

interface DateFilter {
  startDate: string;
  endDate: string;
}

const ReportsPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [dateFilter, setDateFilter] = useState<DateFilter>({
    startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // Últimos 30 dias
    endDate: new Date().toISOString().split('T')[0]
  });
  const { formatPercentage, formatNumber, formatDateRange, calculateEngagementMetrics } = useReportHelpers();

  // Componente para exibir quando não há dados

  // Adicione esta função de utilidade para calcular métricas de desempenho
  const calculateEfficiencyScore = (completed: number, total: number, overdue: number): number => {
    if (total === 0) return 0;
    
    // Base: taxa de conclusão (60% do peso)
    const completionRate = completed / total;
    
    // Penalidade por tarefas atrasadas (40% do peso)
    const overdueRate = total > 0 ? overdue / total : 0;
    
    // Pontuação final (0-100)
    return Math.round((completionRate * 0.6 + (1 - overdueRate) * 0.4) * 100);
  };

  useEffect(() => {
    const loadReportData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        console.log(`Carregando relatório para o período: ${dateFilter.startDate} a ${dateFilter.endDate}`);
        const data = await generateFullReport(dateFilter.startDate, dateFilter.endDate);
        console.log('Dados do relatório carregados:', data);
        
        // Verificar especificamente os dados de desempenho da equipe
        if (data.teamPerformance) {
          console.log(`Dados de desempenho da equipe: ${data.teamPerformance.length} registros`);
          data.teamPerformance.forEach(member => {
            console.log(`Membro: ${member.userName}, ID: ${member.userId}, Demandas: ${member.completedDemands}/${member.totalDemands}`);
          });
        } else {
          console.warn('Dados de desempenho da equipe não encontrados no relatório');
        }
        
        setReportData(data);
      } catch (err) {
        console.error('Erro ao carregar relatório:', err);
        setError('Falha ao carregar dados do relatório. Por favor, tente novamente.');
      } finally {
        setIsLoading(false);
      }
    };

    loadReportData();
  }, [dateFilter]);

  const handleDateFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setDateFilter(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleApplyFilter = () => {
    setIsLoading(true);
    // O useEffect será disparado automaticamente quando o filtro mudar
  };

  const handleExportPDF = () => {
    if (reportData) {
      console.log('Exportando PDF com dados:', reportData);
      try {
        exportToPDF(reportData);
      } catch (error) {
        console.error('Erro ao exportar PDF:', error);
        // Opcional: adicionar notificação de erro para o usuário
      }
    } else {
      console.warn('Tentativa de exportar PDF sem dados disponíveis');
      // Opcional: adicionar notificação para o usuário
    }
  };

  const handleExportExcel = () => {
    if (reportData) {
      console.log('Exportando Excel com dados:', reportData);
      try {
        exportToExcel(reportData);
      } catch (error) {
        console.error('Erro ao exportar Excel:', error);
        // Opcional: adicionar notificação de erro para o usuário
      }
    } else {
      console.warn('Tentativa de exportar Excel sem dados disponíveis');
      // Opcional: adicionar notificação para o usuário
    }
  };

  const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
    const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
    const x = cx + radius * Math.cos(-midAngle * RADIAN);
    const y = cy + radius * Math.sin(-midAngle * RADIAN);
  
    return (
      <text x={x} y={y} fill="white" textAnchor="middle" dominantBaseline="central">
        {formatPercentage(percent * 100)}
      </text>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <LoadingSpinner size="lg" />
        <p className="ml-4 text-xl dark:text-neutral-light">Carregando relatórios...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-100 dark:bg-red-900 dark:bg-opacity-30 rounded-lg">
        <p className="text-red-600 dark:text-red-400">{error}</p>
      </div>
    );
  }

  if (!reportData) {
    return null;
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-neutral-dark dark:text-neutral-light">Relatórios e Análises</h1>
        <div className="flex space-x-4">
          <div className="flex items-center space-x-2">
            <div>
              <label htmlFor="startDate" className="block text-sm font-medium text-gray-500 dark:text-neutral-DEFAULT">Data Inicial</label>
              <Input
                type="date"
                id="startDate"
                name="startDate"
                value={dateFilter.startDate}
                onChange={handleDateFilterChange}
              />
            </div>
            <div>
              <label htmlFor="endDate" className="block text-sm font-medium text-gray-500 dark:text-neutral-DEFAULT">Data Final</label>
              <Input
                type="date"
                id="endDate"
                name="endDate"
                value={dateFilter.endDate}
                onChange={handleDateFilterChange}
              />
            </div>
            <Button
              onClick={handleApplyFilter}
              variant="primary"
              size="sm"
              className="mt-6"
            >
              Aplicar Filtro
            </Button>
          </div>
          <div className="flex items-end space-x-2">
            <Button
              onClick={handleExportPDF}
              variant="outline"
              size="sm"
              disabled={!reportData || isLoading}
            >
              Exportar PDF
            </Button>
            <Button
              onClick={handleExportExcel}
              variant="outline"
              size="sm"
              disabled={!reportData || isLoading}
            >
              Exportar Excel
            </Button>
          </div>
        </div>
      </div>

      <Card>
        <p className="text-sm text-gray-500 dark:text-neutral-DEFAULT">
          Período: {formatDateRange(reportData.dateRange.start, reportData.dateRange.end)}
        </p>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card title="Status das Demandas">
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={reportData.demandStatus}
                  dataKey="count"
                  nameKey="status"
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={renderCustomizedLabel}
                >
                  {reportData.demandStatus.map((entry, index) => (
                    <Cell key={entry.status} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value: number) => formatNumber(value)} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </Card>

        <Card title="Engajamento de Cidadãos (Últimos 6 Meses)">
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={reportData.citizenEngagement}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value: number) => formatNumber(value)} />
                <Legend />
                <Bar dataKey="newCitizens" name="Novos Cidadãos" fill="#0088FE" />
                <Bar dataKey="activeContacts" name="Contatos Ativos" fill="#00C49F" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </Card>
      </div>

      <Card title="Desempenho da Equipe">
        {isLoading ? (
          <div className="flex justify-center items-center h-80">
            <LoadingSpinner size="lg" />
          </div>
        ) : reportData?.teamPerformance && reportData.teamPerformance.length > 0 ? (
          <>
            <div className="overflow-x-auto mb-4">
              <table className="min-w-full">
                <thead>
                  <tr>
                    <th className="px-4 py-2 text-left">Membro</th>
                    <th className="px-4 py-2 text-center">Demandas Concluídas</th>
                    <th className="px-4 py-2 text-center">Total de Demandas</th>
                    <th className="px-4 py-2 text-center">Taxa de Conclusão</th>
                    <th className="px-4 py-2 text-center">Tempo Médio (h)</th>
                    <th className="px-4 py-2 text-center">Tarefas Atrasadas</th>
                    <th className="px-4 py-2 text-center">Eficiência</th>
                  </tr>
                </thead>
                <tbody>
                  {reportData.teamPerformance
                    .map(member => {
                      const completionRate = member.totalDemands > 0 
                        ? (member.completedDemands / member.totalDemands) * 100 
                        : 0;
                      
                      const efficiencyScore = calculateEfficiencyScore(
                        member.completedDemands,
                        member.totalDemands,
                        member.overdueTasks
                      );
                      
                      // Determinar classes de cor com base na eficiência
                      const efficiencyClass = 
                        efficiencyScore >= 80 ? 'text-green-600 font-semibold' :
                        efficiencyScore >= 60 ? 'text-yellow-600' :
                        'text-red-600';

                      return (
                        <tr key={member.userId}>
                          <td className="px-4 py-2 font-medium">{member.userName || 'Usuário sem nome'}</td>
                          <td className="px-4 py-2 text-center">{formatNumber(member.completedDemands)}</td>
                          <td className="px-4 py-2 text-center">{formatNumber(member.totalDemands)}</td>
                          <td className="px-4 py-2 text-center">{formatPercentage(completionRate)}</td>
                          <td className="px-4 py-2 text-center">{member.averageResponseTime > 0 ? `${member.averageResponseTime}h` : '-'}</td>
                          <td className="px-4 py-2 text-center">{formatNumber(member.overdueTasks)}</td>
                          <td className={`px-4 py-2 text-center ${efficiencyClass}`}>
                            {formatPercentage(efficiencyScore)}
                          </td>
                        </tr>
                      );
                    })}
                </tbody>
              </table>
            </div>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <ComposedChart
                  data={reportData.teamPerformance
                    // Filtrar membros sem demandas para o gráfico
                    .filter(member => member.totalDemands > 0)
                    // Ordenar por eficiência (decrescente)
                    .sort((a, b) => {
                      const effA = calculateEfficiencyScore(a.completedDemands, a.totalDemands, a.overdueTasks);
                      const effB = calculateEfficiencyScore(b.completedDemands, b.totalDemands, b.overdueTasks);
                      return effB - effA;
                    })
                    // Limitar a 5 membros para melhor visualização
                    .slice(0, 5)
                    // Mapear para um formato mais adequado para o gráfico
                    .map(member => {
                      const efficiency = calculateEfficiencyScore(
                        member.completedDemands, 
                        member.totalDemands, 
                        member.overdueTasks
                      );
                      
                      return {
                        name: member.userName || `ID: ${member.userId.substring(0, 6)}`,
                        eficiência: efficiency,
                        concluídas: member.completedDemands,
                        atrasadas: member.overdueTasks,
                        total: member.totalDemands,
                        tempoMédio: member.averageResponseTime
                      };
                    })
                  }
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis yAxisId="left" orientation="left" />
                  <YAxis yAxisId="right" orientation="right" domain={[0, 100]} />
                  <Tooltip 
                    formatter={(value, name) => {
                      if (name === 'eficiência') return [`${value}%`, 'Eficiência'];
                      if (name === 'concluídas') return [value, 'Demandas Concluídas'];
                      if (name === 'atrasadas') return [value, 'Tarefas Atrasadas'];
                      if (name === 'total') return [value, 'Total de Demandas'];
                      if (name === 'tempoMédio') return [`${value}h`, 'Tempo Médio'];
                      return [value, name];
                    }}
                  />
                  <Legend />
                  <Bar yAxisId="left" dataKey="total" name="Total de Demandas" fill="#94a3b8" />
                  <Bar yAxisId="left" dataKey="concluídas" name="Demandas Concluídas" fill="#4ade80" />
                  <Bar yAxisId="left" dataKey="atrasadas" name="Tarefas Atrasadas" fill="#f87171" />
                  <Line yAxisId="right" type="monotone" dataKey="eficiência" name="Eficiência (%)" stroke="#8884d8" strokeWidth={2} />
                </ComposedChart>
              </ResponsiveContainer>
            </div>
          </>
        ) : (
          <div className="text-center py-8 text-gray-500 dark:text-neutral-DEFAULT">
            <p>Nenhum dado de desempenho disponível.</p>
            <p className="mt-2">Atribua demandas aos membros da equipe para gerar métricas de desempenho.</p>
          </div>
        )}
      </Card>
      
      <Card title="Análise de Redes Sociais">
        <div className="overflow-x-auto mb-4">
          <table className="min-w-full">
            <thead>
              <tr>
                <th className="px-4 py-2 text-left">Plataforma</th>
                <th className="px-4 py-2 text-right">Seguidores</th>
                <th className="px-4 py-2 text-right">Engajamento</th>
                <th className="px-4 py-2 text-right">Alcance</th>
                <th className="px-4 py-2 text-center">Sentimento</th>
              </tr>
            </thead>
            <tbody>
              {reportData.socialMedia.map(platform => {
                const metrics = calculateEngagementMetrics(
                  platform.followers,
                  platform.engagement,
                  platform.reach
                );

                return (
                  <tr key={platform.platform}>
                    <td className="px-4 py-2">{platform.platform}</td>
                    <td className="px-4 py-2 text-right">{metrics.followersFormatted}</td>
                    <td className="px-4 py-2 text-right">{metrics.engagementFormatted}</td>
                    <td className="px-4 py-2 text-right">{metrics.reachFormatted}</td>
                    <td className="px-4 py-2">
                      <div className="flex justify-center gap-2">
                        <span className="text-green-500">{formatPercentage(platform.sentiment.positive)}</span>
                        <span className="text-gray-500">{formatPercentage(platform.sentiment.neutral)}</span>
                        <span className="text-red-500">{formatPercentage(platform.sentiment.negative)}</span>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={reportData.socialMedia}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="platform" />
              <YAxis yAxisId="left" />
              <YAxis yAxisId="right" orientation="right" />
              <Tooltip formatter={(value: number) => formatNumber(value)} />
              <Legend />
              <Line 
                yAxisId="left" 
                type="monotone" 
                dataKey="followers" 
                name="Seguidores" 
                stroke="#0088FE" 
              />
              <Line 
                yAxisId="right" 
                type="monotone" 
                dataKey="engagement" 
                name="Taxa de Engajamento (%)" 
                stroke="#00C49F" 
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </Card>
    </div>
  );
};

export default ReportsPage;

















