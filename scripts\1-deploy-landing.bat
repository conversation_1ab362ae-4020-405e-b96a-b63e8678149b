@echo off
echo ========================================
echo     ETAPA 1: DEPLOY LANDING PAGE
echo ========================================
echo.

echo [1/5] Verificando configuracao...
if not exist "landingpage\index.html" (
    echo ERRO: Landing page nao encontrada!
    pause
    exit /b 1
)

if not exist "firebase.json" (
    echo ERRO: firebase.json nao encontrado!
    pause
    exit /b 1
)

echo Landing page encontrada!
echo.

echo [2/5] Verificando Firebase CLI...
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Firebase CLI nao encontrado. Instalando...
    npm install -g firebase-tools
)

echo.
echo [3/5] Fazendo login no Firebase...
firebase login

echo.
echo [4/5] Configurando projeto...
firebase use promandato-9a4cf

echo.
echo [5/5] Fazendo deploy da landing page...
firebase deploy --only hosting

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo        LANDING PAGE DEPLOYADA!
    echo ========================================
    echo.
    echo URL: https://promandato-9a4cf.web.app
    echo.
    echo Proximos passos:
    echo 1. Configurar GitHub Actions (FIREBASE_TOKEN)
    echo 2. Testar fluxo de pagamento
    echo 3. Deploy do backend
    echo.
) else (
    echo.
    echo ERRO: Deploy falhou!
    echo Verifique sua conexao e tente novamente.
)

pause
