import React, { useState, useEffect, useCallback } from 'react';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Modal } from '../components/ui/Modal';
import { Input, Textarea } from '../components/ui/Input';
import { ICONS } from '../constants';
import { Service, ServiceCategory, ServiceStatus } from '../types';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';
import { useAuth } from '../hooks/useAuth';
import { 
  getServices, 
  addService, 
  updateService, 
  deleteService 
} from '../services/firebaseService';

const ServicesPage: React.FC = () => {
  const [services, setServices] = useState<Service[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [currentService, setCurrentService] = useState<Service | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: ServiceCategory.OTHER,
    priority: 'medium' as 'low' | 'medium' | 'high',
    deadline: '',
    notes: '',
  });
  const [filterStatus, setFilterStatus] = useState<ServiceStatus | 'all'>('all');
  const [filterCategory, setFilterCategory] = useState<ServiceCategory | 'all'>('all');
  const [searchTerm, setSearchTerm] = useState('');

  const { currentUser } = useAuth();

  const fetchServices = useCallback(async () => {
    if (!currentUser) return;
    
    setIsLoading(true);
    setError(null);
    try {
      const fetchedServices = await getServices();
      setServices(fetchedServices);
    } catch (err) {
      console.error("Failed to fetch services:", err);
      setError("Falha ao carregar serviços. Tente novamente.");
    } finally {
      setIsLoading(false);
    }
  }, [currentUser]);

  useEffect(() => {
    fetchServices();
  }, [fetchServices]);

  const handleOpenModal = (service: Service | null = null) => {
    if (service) {
      setCurrentService(service);
      setFormData({
        title: service.title,
        description: service.description,
        category: service.category,
        priority: service.priority,
        deadline: service.deadline || '',
        notes: service.notes || '',
      });
    } else {
      setCurrentService(null);
      setFormData({
        title: '',
        description: '',
        category: ServiceCategory.OTHER,
        priority: 'medium',
        deadline: '',
        notes: '',
      });
    }
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setCurrentService(null);
  };

  const handleOpenDeleteModal = (service: Service) => {
    setCurrentService(service);
    setIsDeleteModalOpen(true);
  };

  const handleCloseDeleteModal = () => {
    setIsDeleteModalOpen(false);
    setCurrentService(null);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentUser) return;

    setError(null);
    try {
      if (currentService) {
        // Atualizar serviço existente
        await updateService(currentService.id, {
          ...formData,
          status: currentService.status,
        });
      } else {
        // Adicionar novo serviço
        await addService({
          ...formData,
          status: ServiceStatus.PENDING,
        }, currentUser.id);
      }
      
      fetchServices();
      handleCloseModal();
    } catch (err) {
      console.error("Failed to save service:", err);
      setError("Falha ao salvar serviço. Tente novamente.");
    }
  };

  const handleDelete = async () => {
    if (!currentService) return;

    setError(null);
    try {
      await deleteService(currentService.id);
      fetchServices();
      handleCloseDeleteModal();
    } catch (err) {
      console.error("Failed to delete service:", err);
      setError("Falha ao excluir serviço. Tente novamente.");
    }
  };

  const handleStatusChange = async (serviceId: string, newStatus: ServiceStatus) => {
    setError(null);
    try {
      await updateService(serviceId, { status: newStatus });
      fetchServices();
    } catch (err) {
      console.error("Failed to update service status:", err);
      setError("Falha ao atualizar status do serviço. Tente novamente.");
    }
  };

  // Filtrar serviços
  const filteredServices = services.filter(service => {
    const matchesStatus = filterStatus === 'all' || service.status === filterStatus;
    const matchesCategory = filterCategory === 'all' || service.category === filterCategory;
    const matchesSearch = searchTerm === '' || 
      service.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      service.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesStatus && matchesCategory && matchesSearch;
  });

  // Renderizar status com cores
  const renderStatus = (status: ServiceStatus) => {
    const statusMap = {
      [ServiceStatus.PENDING]: { text: 'Pendente', color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' },
      [ServiceStatus.IN_PROGRESS]: { text: 'Em Andamento', color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' },
      [ServiceStatus.COMPLETED]: { text: 'Concluído', color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' },
      [ServiceStatus.CANCELLED]: { text: 'Cancelado', color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' },
    };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusMap[status].color}`}>
        {statusMap[status].text}
      </span>
    );
  };

  // Renderizar categoria
  const renderCategory = (category: ServiceCategory) => {
    const categoryMap = {
      [ServiceCategory.INFRASTRUCTURE]: 'Infraestrutura',
      [ServiceCategory.HEALTH]: 'Saúde',
      [ServiceCategory.EDUCATION]: 'Educação',
      [ServiceCategory.SECURITY]: 'Segurança',
      [ServiceCategory.ENVIRONMENT]: 'Meio Ambiente',
      [ServiceCategory.SOCIAL]: 'Social',
      [ServiceCategory.OTHER]: 'Outros',
    };
    
    return categoryMap[category];
  };

  // Renderizar prioridade com cores
  const renderPriority = (priority: 'low' | 'medium' | 'high') => {
    const priorityMap = {
      'low': { text: 'Baixa', color: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300' },
      'medium': { text: 'Média', color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' },
      'high': { text: 'Alta', color: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' },
    };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${priorityMap[priority].color}`}>
        {priorityMap[priority].text}
      </span>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-neutral-dark dark:text-neutral-light">Serviços</h1>
        <Button 
          onClick={() => handleOpenModal()} 
          leftIcon={ICONS.PLUS}
        >
          Novo Serviço
        </Button>
      </div>

      {error && <p className="text-red-500 dark:text-red-400 bg-red-100 dark:bg-red-900 dark:bg-opacity-30 p-3 rounded-md">{error}</p>}

      <Card>
        <div className="space-y-4">
          {/* Filtros */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 p-4 bg-gray-50 dark:bg-neutral-dark rounded-md">
            <Input 
              placeholder="Buscar serviços..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              aria-label="Buscar serviços"
            />
            
            <select 
              className="block w-full px-3 py-2 border border-gray-300 dark:border-neutral-medium rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm bg-white dark:bg-neutral-dark dark:text-neutral-light"
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as ServiceStatus | 'all')}
              aria-label="Filtrar por status"
            >
              <option value="all">Todos os Status</option>
              <option value={ServiceStatus.PENDING}>Pendentes</option>
              <option value={ServiceStatus.IN_PROGRESS}>Em Andamento</option>
              <option value={ServiceStatus.COMPLETED}>Concluídos</option>
              <option value={ServiceStatus.CANCELLED}>Cancelados</option>
            </select>
            
            <select 
              className="block w-full px-3 py-2 border border-gray-300 dark:border-neutral-medium rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm bg-white dark:bg-neutral-dark dark:text-neutral-light"
              value={filterCategory}
              onChange={(e) => setFilterCategory(e.target.value as ServiceCategory | 'all')}
              aria-label="Filtrar por categoria"
            >
              <option value="all">Todas as Categorias</option>
              <option value={ServiceCategory.INFRASTRUCTURE}>Infraestrutura</option>
              <option value={ServiceCategory.HEALTH}>Saúde</option>
              <option value={ServiceCategory.EDUCATION}>Educação</option>
              <option value={ServiceCategory.SECURITY}>Segurança</option>
              <option value={ServiceCategory.ENVIRONMENT}>Meio Ambiente</option>
              <option value={ServiceCategory.SOCIAL}>Social</option>
              <option value={ServiceCategory.OTHER}>Outros</option>
            </select>
          </div>

          {/* Lista de Serviços */}
          {isLoading ? (
            <div className="flex justify-center items-center py-10">
              <LoadingSpinner size="lg" />
            </div>
          ) : filteredServices.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-neutral-dark">
                <thead className="bg-gray-50 dark:bg-neutral-dark">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-neutral-DEFAULT uppercase tracking-wider">
                      Título
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-neutral-DEFAULT uppercase tracking-wider">
                      Categoria
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-neutral-DEFAULT uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-neutral-DEFAULT uppercase tracking-wider">
                      Prioridade
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-neutral-DEFAULT uppercase tracking-wider">
                      Prazo
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-neutral-DEFAULT uppercase tracking-wider">
                      Ações
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-neutral-darker divide-y divide-gray-200 dark:divide-neutral-dark">
                  {filteredServices.map(service => (
                    <tr key={service.id} className="hover:bg-gray-50 dark:hover:bg-neutral-dark">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-neutral-dark dark:text-neutral-light">{service.title}</div>
                        <div className="text-xs text-gray-500 dark:text-neutral-DEFAULT truncate max-w-xs">{service.description}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-neutral-light">{renderCategory(service.category)}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {renderStatus(service.status)}
                          <div className="ml-2">
                            <select
                              className="text-xs border-none bg-transparent focus:ring-0 cursor-pointer"
                              value={service.status}
                              onChange={(e) => handleStatusChange(service.id, e.target.value as ServiceStatus)}
                              aria-label={`Alterar status de ${service.title}`}
                            >
                              <option value={ServiceStatus.PENDING}>Pendente</option>
                              <option value={ServiceStatus.IN_PROGRESS}>Em Andamento</option>
                              <option value={ServiceStatus.COMPLETED}>Concluído</option>
                              <option value={ServiceStatus.CANCELLED}>Cancelado</option>
                            </select>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {renderPriority(service.priority)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-neutral-light">
                          {service.deadline ? new Date(service.deadline).toLocaleDateString('pt-BR') : 'Sem prazo'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleOpenModal(service)}
                          leftIcon={ICONS.PENCIL}
                          className="mr-2"
                        >
                          Editar
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleOpenDeleteModal(service)}
                          leftIcon={ICONS.TRASH}
                          className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-500"
                        >
                          Excluir
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <p className="text-center py-10 text-gray-500 dark:text-neutral-DEFAULT">
              Nenhum serviço encontrado. Clique em "Novo Serviço" para adicionar.
            </p>
          )}
        </div>
      </Card>

      {/* Modal de Adicionar/Editar Serviço */}
      <Modal 
        isOpen={isModalOpen} 
        onClose={handleCloseModal} 
        title={currentService ? "Editar Serviço" : "Novo Serviço"}
      >
        <form onSubmit={handleSubmit} className="space-y-4">
          <Input
            label="Título"
            name="title"
            value={formData.title}
            onChange={handleInputChange}
            required
            placeholder="Ex: Manutenção de Praça"
          />
          
          <Textarea
            label="Descrição"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            required
            placeholder="Descreva o serviço em detalhes..."
            rows={4}
          />
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-neutral-dark dark:text-neutral-light mb-1" htmlFor="category">
                Categoria
              </label>
              <select
                id="category"
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                className="block w-full px-3 py-2 border border-gray-300 dark:border-neutral-medium rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm bg-white dark:bg-neutral-dark dark:text-neutral-light"
                required
                aria-label="Categoria do serviço"
              >
                <option value={ServiceCategory.INFRASTRUCTURE}>Infraestrutura</option>
                <option value={ServiceCategory.HEALTH}>Saúde</option>
                <option value={ServiceCategory.EDUCATION}>Educação</option>
                <option value={ServiceCategory.SECURITY}>Segurança</option>
                <option value={ServiceCategory.ENVIRONMENT}>Meio Ambiente</option>
                <option value={ServiceCategory.SOCIAL}>Social</option>
                <option value={ServiceCategory.OTHER}>Outros</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-neutral-dark dark:text-neutral-light mb-1" htmlFor="priority">
                Prioridade
              </label>
              <select
                id="priority"
                name="priority"
                value={formData.priority}
                onChange={handleInputChange}
                className="block w-full px-3 py-2 border border-gray-300 dark:border-neutral-medium rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm bg-white dark:bg-neutral-dark dark:text-neutral-light"
                required
                aria-label="Prioridade do serviço"
              >
                <option value="low">Baixa</option>
                <option value="medium">Média</option>
                <option value="high">Alta</option>
              </select>
            </div>
          </div>
          
          <Input
            label="Prazo (opcional)"
            name="deadline"
            type="date"
            value={formData.deadline}
            onChange={handleInputChange}
          />
          
          <Textarea
            label="Observações (opcional)"
            name="notes"
            value={formData.notes}
            onChange={handleInputChange}
            placeholder="Informações adicionais sobre o serviço..."
            rows={3}
          />
          
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleCloseModal}
            >
              Cancelar
            </Button>
            <Button
              type="submit"
            >
              {currentService ? "Salvar Alterações" : "Adicionar Serviço"}
            </Button>
          </div>
        </form>
      </Modal>

      {/* Modal de Confirmação de Exclusão */}
      <Modal 
        isOpen={isDeleteModalOpen} 
        onClose={handleCloseDeleteModal} 
        title="Confirmar Exclusão"
        size="sm"
      >
        <div className="space-y-4">
          <p className="text-neutral-dark dark:text-neutral-light">
            Tem certeza que deseja excluir o serviço <span className="font-semibold">{currentService?.title}</span>?
          </p>
          <p className="text-sm text-red-600 dark:text-red-400">
            Esta ação não pode ser desfeita.
          </p>
          
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              variant="outline"
              onClick={handleCloseDeleteModal}
            >
              Cancelar
            </Button>
            <Button
              variant="secondary"
              onClick={handleDelete}
              className="bg-red-600 hover:bg-red-700 text-white"
            >
              Excluir
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default ServicesPage;


