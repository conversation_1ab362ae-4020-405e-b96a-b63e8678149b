import React, { useState, useEffect } from 'react';
import { Card } from './Card';
import { Button } from './Button';
import { Input } from './Input';
import { ThemeToggle } from './ThemeToggle';
import { safeGetItem, safeSetItem } from '../../utils/storage';

interface SettingsSection {
  id: string;
  title: string;
  description: string;
  icon: React.ReactElement;
  component: React.ReactElement;
}

interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  timezone: string;
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
  dashboard: {
    autoRefresh: boolean;
    refreshInterval: number;
    defaultView: string;
  };
  accessibility: {
    highContrast: boolean;
    largeText: boolean;
    reducedMotion: boolean;
  };
}

const DEFAULT_PREFERENCES: UserPreferences = {
  theme: 'system',
  language: 'pt-BR',
  timezone: 'America/Sao_Paulo',
  notifications: {
    email: true,
    push: true,
    sms: false
  },
  dashboard: {
    autoRefresh: true,
    refreshInterval: 30,
    defaultView: 'overview'
  },
  accessibility: {
    highContrast: false,
    largeText: false,
    reducedMotion: false
  }
};

export const AdvancedSettings: React.FC = () => {
  const [preferences, setPreferences] = useState<UserPreferences>(DEFAULT_PREFERENCES);
  const [activeSection, setActiveSection] = useState('appearance');
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    // Carregar preferências salvas usando função segura
    const savedPreferences = safeGetItem('user-preferences', {});
    if (Object.keys(savedPreferences).length > 0) {
      setPreferences({ ...DEFAULT_PREFERENCES, ...savedPreferences });
    }
  }, []);

  const updatePreference = (path: string, value: any) => {
    setPreferences(prev => {
      const keys = path.split('.');
      const updated = { ...prev };
      let current: any = updated;

      for (let i = 0; i < keys.length - 1; i++) {
        current[keys[i]] = { ...current[keys[i]] };
        current = current[keys[i]];
      }

      current[keys[keys.length - 1]] = value;
      return updated;
    });
    setHasChanges(true);
  };

  const savePreferences = () => {
    safeSetItem('user-preferences', preferences);
    setHasChanges(false);
    
    // Aplicar algumas configurações imediatamente
    if (preferences.accessibility.highContrast) {
      document.documentElement.classList.add('high-contrast');
    } else {
      document.documentElement.classList.remove('high-contrast');
    }
    
    if (preferences.accessibility.largeText) {
      document.documentElement.classList.add('large-text');
    } else {
      document.documentElement.classList.remove('large-text');
    }
    
    if (preferences.accessibility.reducedMotion) {
      document.documentElement.classList.add('reduced-motion');
    } else {
      document.documentElement.classList.remove('reduced-motion');
    }
  };

  const resetToDefaults = () => {
    setPreferences(DEFAULT_PREFERENCES);
    setHasChanges(true);
  };

  const sections: SettingsSection[] = [
    {
      id: 'appearance',
      title: 'Aparência',
      description: 'Tema, cores e personalização visual',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
        </svg>
      ),
      component: (
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-neutral-DEFAULT mb-3">
              Tema
            </label>
            <ThemeToggle />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-neutral-DEFAULT mb-2">
              Idioma
            </label>
            <select
              value={preferences.language}
              onChange={(e) => updatePreference('language', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-neutral-medium rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-neutral-dark dark:text-neutral-light"
              title="Selecionar idioma"
            >
              <option value="pt-BR">Português (Brasil)</option>
              <option value="en-US">English (US)</option>
              <option value="es-ES">Español</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-neutral-DEFAULT mb-2">
              Fuso Horário
            </label>
            <select
              value={preferences.timezone}
              onChange={(e) => updatePreference('timezone', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-neutral-medium rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-neutral-dark dark:text-neutral-light"
              title="Selecionar fuso horário"
            >
              <option value="America/Sao_Paulo">São Paulo (GMT-3)</option>
              <option value="America/New_York">New York (GMT-5)</option>
              <option value="Europe/London">London (GMT+0)</option>
            </select>
          </div>
        </div>
      )
    },
    {
      id: 'notifications',
      title: 'Notificações',
      description: 'Configure como receber alertas e atualizações',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM4 19h10a2 2 0 002-2V7a2 2 0 00-2-2H4a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
      ),
      component: (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-neutral-light">
                Notificações por Email
              </h4>
              <p className="text-sm text-gray-500 dark:text-neutral-medium">
                Receber atualizações importantes por email
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.notifications.email}
                onChange={(e) => updatePreference('notifications.email', e.target.checked)}
                className="sr-only peer"
                aria-label="Ativar notificações por email"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/25 dark:peer-focus:ring-primary/50 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary"></div>
            </label>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-neutral-light">
                Notificações Push
              </h4>
              <p className="text-sm text-gray-500 dark:text-neutral-medium">
                Receber notificações no navegador
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.notifications.push}
                onChange={(e) => updatePreference('notifications.push', e.target.checked)}
                className="sr-only peer"
                aria-label="Ativar notificações push"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/25 dark:peer-focus:ring-primary/50 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary"></div>
            </label>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-neutral-light">
                Notificações SMS
              </h4>
              <p className="text-sm text-gray-500 dark:text-neutral-medium">
                Receber alertas urgentes por SMS
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.notifications.sms}
                onChange={(e) => updatePreference('notifications.sms', e.target.checked)}
                className="sr-only peer"
                aria-label="Ativar notificações SMS"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/25 dark:peer-focus:ring-primary/50 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary"></div>
            </label>
          </div>
        </div>
      )
    },
    {
      id: 'dashboard',
      title: 'Dashboard',
      description: 'Configurações de exibição e atualização',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      component: (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-neutral-light">
                Atualização Automática
              </h4>
              <p className="text-sm text-gray-500 dark:text-neutral-medium">
                Atualizar dados automaticamente
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.dashboard.autoRefresh}
                onChange={(e) => updatePreference('dashboard.autoRefresh', e.target.checked)}
                className="sr-only peer"
                aria-label="Ativar atualização automática do dashboard"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/25 dark:peer-focus:ring-primary/50 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary"></div>
            </label>
          </div>
          
          {preferences.dashboard.autoRefresh && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-neutral-DEFAULT mb-2">
                Intervalo de Atualização (segundos)
              </label>
              <Input
                type="number"
                min="10"
                max="300"
                value={preferences.dashboard.refreshInterval}
                onChange={(e) => updatePreference('dashboard.refreshInterval', parseInt(e.target.value))}
              />
            </div>
          )}
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-neutral-DEFAULT mb-2">
              Visualização Padrão
            </label>
            <select
              value={preferences.dashboard.defaultView}
              onChange={(e) => updatePreference('dashboard.defaultView', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-neutral-medium rounded-md focus:outline-none focus:ring-2 focus:ring-primary dark:bg-neutral-dark dark:text-neutral-light"
              title="Selecionar visualização padrão"
            >
              <option value="overview">Visão Geral</option>
              <option value="demands">Demandas</option>
              <option value="citizens">Cidadãos</option>
              <option value="reports">Relatórios</option>
            </select>
          </div>
        </div>
      )
    },
    {
      id: 'accessibility',
      title: 'Acessibilidade',
      description: 'Opções para melhorar a experiência de uso',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      ),
      component: (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-neutral-light">
                Alto Contraste
              </h4>
              <p className="text-sm text-gray-500 dark:text-neutral-medium">
                Aumentar contraste para melhor visibilidade
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.accessibility.highContrast}
                onChange={(e) => updatePreference('accessibility.highContrast', e.target.checked)}
                className="sr-only peer"
                aria-label="Ativar alto contraste"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/25 dark:peer-focus:ring-primary/50 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary"></div>
            </label>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-neutral-light">
                Texto Grande
              </h4>
              <p className="text-sm text-gray-500 dark:text-neutral-medium">
                Aumentar tamanho da fonte
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.accessibility.largeText}
                onChange={(e) => updatePreference('accessibility.largeText', e.target.checked)}
                className="sr-only peer"
                aria-label="Ativar texto grande"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/25 dark:peer-focus:ring-primary/50 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary"></div>
            </label>
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <h4 className="text-sm font-medium text-gray-900 dark:text-neutral-light">
                Reduzir Movimento
              </h4>
              <p className="text-sm text-gray-500 dark:text-neutral-medium">
                Minimizar animações e transições
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={preferences.accessibility.reducedMotion}
                onChange={(e) => updatePreference('accessibility.reducedMotion', e.target.checked)}
                className="sr-only peer"
                aria-label="Ativar movimento reduzido"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary/25 dark:peer-focus:ring-primary/50 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-primary"></div>
            </label>
          </div>
        </div>
      )
    }
  ];

  const activeConfig = sections.find(s => s.id === activeSection);

  return (
    <div className="max-w-6xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-neutral-light">
          Configurações Avançadas
        </h1>
        <p className="text-gray-600 dark:text-neutral-DEFAULT mt-2">
          Personalize sua experiência no sistema
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Menu lateral */}
        <div className="lg:col-span-1">
          <Card className="p-0">
            <nav className="space-y-1">
              {sections.map((section) => (
                <button
                  key={section.id}                  type="button"                  onClick={() => setActiveSection(section.id)}
                  className={`
                    w-full text-left px-4 py-3 flex items-center space-x-3 transition-colors
                    ${activeSection === section.id
                      ? 'bg-primary text-white'
                      : 'text-gray-700 dark:text-neutral-DEFAULT hover:bg-gray-100 dark:hover:bg-neutral-darker'
                    }
                  `}
                >
                  {section.icon}
                  <div>
                    <div className="font-medium">{section.title}</div>
                    <div className={`text-sm ${
                      activeSection === section.id 
                        ? 'text-primary-light' 
                        : 'text-gray-500 dark:text-neutral-medium'
                    }`}>
                      {section.description}
                    </div>
                  </div>
                </button>
              ))}
            </nav>
          </Card>
        </div>

        {/* Conteúdo principal */}
        <div className="lg:col-span-3">
          <Card>
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-neutral-light">
                    {activeConfig?.title}
                  </h2>
                  <p className="text-gray-600 dark:text-neutral-DEFAULT">
                    {activeConfig?.description}
                  </p>
                </div>
                
                {hasChanges && (
                  <div className="flex space-x-3">
                    <Button variant="outline" onClick={resetToDefaults}>
                      Restaurar Padrões
                    </Button>
                    <Button variant="primary" onClick={savePreferences}>
                      Salvar Alterações
                    </Button>
                  </div>
                )}
              </div>

              {activeConfig?.component}
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};