import fs from 'fs-extra';
import csv from 'csv-parser';
import User from './models/User.js';
import { sendWelcomeEmail } from './services/emailService.js';

// Função para gerar senha temporária
function generateTempPassword() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let password = '';
    for (let i = 0; i < 12; i++) {
        password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
}

// Mapear produtos para planos
function mapProductToPlan(productName) {
    const name = productName.toLowerCase();
    if (name.includes('basico') || name.includes('básico')) return 'basic';
    if (name.includes('padrão') || name.includes('padrao')) return 'standard';
    if (name.includes('profissional') || name.includes('professional')) return 'professional';
    return 'basic'; // default
}

async function importPricesCSV() {
    console.log('🚀 Iniciando importação do prices.csv...');
    
    const results = [];
    const csvPath = './data/prices.csv';
    
    return new Promise((resolve, reject) => {
        fs.createReadStream(csvPath)
            .pipe(csv())
            .on('data', (data) => results.push(data))
            .on('end', async () => {
                console.log(`📊 ${results.length} registros encontrados no CSV`);
                
                let created = 0;
                let errors = 0;
                
                for (const row of results) {
                    try {
                        // Criar dados do usuário baseado no produto
                        const planType = mapProductToPlan(row['Product Name'] || '');
                        const tempPassword = generateTempPassword();
                        
                        // Criar email fictício baseado no produto (você pode alterar)
                        const productName = row['Product Name'] || 'Cliente';
                        const email = `cliente.${planType}.${Date.now()}@promandato.com.br`;
                        
                        console.log(`\n📝 Criando usuário para: ${row['Product Name']}`);
                        console.log(`   Email: ${email}`);
                        console.log(`   Plano: ${planType}`);
                        console.log(`   Preço: R$ ${row['Amount']}`);
                        
                        // Criar usuário
                        const user = await User.create({
                            email: email,
                            name: `Cliente ${productName}`,
                            password: tempPassword,
                            role: 'ADMIN',
                            planId: planType,
                            isActive: true,
                            emailVerified: false,
                            stripeCustomerId: `cus_${Date.now()}`,
                            stripePriceId: row['Price ID'],
                            profile: {
                                phone: '+5511999999999',
                                country: 'BR'
                            }
                        });
                        
                        console.log(`✅ Usuário criado: ${user.email}`);
                        console.log(`🔑 Senha temporária: ${tempPassword}`);
                        
                        // Enviar email de boas-vindas
                        try {
                            await sendWelcomeEmail(user, tempPassword, planType);
                            console.log(`📧 Email enviado para: ${user.email}`);
                        } catch (emailError) {
                            console.log(`⚠️  Erro ao enviar email: ${emailError.message}`);
                        }
                        
                        created++;
                        
                    } catch (error) {
                        console.error(`❌ Erro ao processar linha:`, error.message);
                        errors++;
                    }
                }
                
                console.log('\n📊 RESUMO DA IMPORTAÇÃO:');
                console.log(`✅ Usuários criados: ${created}`);
                console.log(`❌ Erros: ${errors}`);
                console.log(`📧 Emails enviados: ${created}`);
                
                if (created > 0) {
                    console.log('\n🎉 IMPORTAÇÃO CONCLUÍDA COM SUCESSO!');
                    console.log('\n📋 PRÓXIMOS PASSOS:');
                    console.log('1. Verifique os emails enviados (ou arquivos em backend/data/emails/)');
                    console.log('2. Teste o login dos usuários criados');
                    console.log('3. Acesse: http://localhost:5174');
                    console.log('4. Use as credenciais mostradas acima');
                }
                
                resolve({ created, errors });
            })
            .on('error', reject);
    });
}

// Executar importação
importPricesCSV()
    .then((result) => {
        console.log('\n✅ Processo finalizado!');
        process.exit(0);
    })
    .catch((error) => {
        console.error('\n❌ Erro na importação:', error);
        process.exit(1);
    });
