@echo off
echo ========================================
echo      ETAPA 3: DEPLOY BACKEND/API
echo ========================================
echo.

echo [1/6] Verificando backend...
if not exist "backend\server.js" (
    echo ERRO: Backend nao encontrado!
    pause
    exit /b 1
)

echo Backend encontrado!
echo.

echo [2/6] Escolha o metodo de deploy:
echo.
echo 1. Firebase Functions (Recomendado)
echo 2. Google Cloud Run
echo 3. Heroku
echo.
set /p choice="Digite sua escolha (1-3): "

if "%choice%"=="1" goto firebase_functions
if "%choice%"=="2" goto cloud_run
if "%choice%"=="3" goto heroku
goto invalid_choice

:firebase_functions
echo.
echo [3/6] Configurando Firebase Functions...
cd backend
npm install

echo.
echo [4/6] Inicializando Functions...
firebase init functions --project promandato-9a4cf

echo.
echo [5/6] Fazendo deploy das Functions...
firebase deploy --only functions

echo.
echo [6/6] Functions deployadas!
echo URL: https://us-central1-promandato-9a4cf.cloudfunctions.net/api
goto end

:cloud_run
echo.
echo [3/6] Configurando Cloud Run...
echo Criando Dockerfile...

echo FROM node:18 > backend\Dockerfile
echo WORKDIR /app >> backend\Dockerfile
echo COPY package*.json ./ >> backend\Dockerfile
echo RUN npm install >> backend\Dockerfile
echo COPY . . >> backend\Dockerfile
echo EXPOSE 3002 >> backend\Dockerfile
echo CMD ["npm", "start"] >> backend\Dockerfile

echo.
echo [4/6] Building container...
cd backend
gcloud builds submit --tag gcr.io/promandato-9a4cf/backend

echo.
echo [5/6] Deploying to Cloud Run...
gcloud run deploy backend --image gcr.io/promandato-9a4cf/backend --platform managed --region us-central1 --allow-unauthenticated

echo.
echo [6/6] Cloud Run deployado!
goto end

:heroku
echo.
echo [3/6] Configurando Heroku...
echo Instale Heroku CLI primeiro: https://devcenter.heroku.com/articles/heroku-cli
echo.
echo [4/6] Criando app Heroku...
cd backend
heroku create promandato-backend

echo.
echo [5/6] Fazendo deploy...
git add .
git commit -m "Deploy to Heroku"
git push heroku main

echo.
echo [6/6] Heroku deployado!
goto end

:invalid_choice
echo Escolha invalida!
pause
exit /b 1

:end
echo.
echo ========================================
echo         BACKEND DEPLOYADO!
echo ========================================
echo.
echo Proximos passos:
echo 1. Configurar dominio customizado (opcional)
echo 2. Testar API endpoints
echo 3. Deploy da aplicacao frontend
echo.
pause
