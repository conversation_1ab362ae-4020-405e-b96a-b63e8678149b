#!/bin/bash

# ===========================================
# DEPLOY PARA GOOGLE CLOUD RUN
# ProMandato Backend
# ===========================================

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# Configurações
PROJECT_ID="promandato-9a4cf"
REGION="southamerica-east1"
SERVICE_NAME="promandato-backend"
IMAGE_NAME="gcr.io/${PROJECT_ID}/${SERVICE_NAME}"

echo "🚀 Deploy do ProMandato Backend para Google Cloud Run"
echo "📋 Projeto: $PROJECT_ID"
echo "🌎 Região: $REGION"
echo "🐳 Imagem: $IMAGE_NAME"
echo ""

# Verificar se gcloud está instalado
if ! command -v gcloud &> /dev/null; then
    log_error "Google Cloud SDK não está instalado"
    echo "Instale em: https://cloud.google.com/sdk/docs/install"
    exit 1
fi

# Verificar se está logado no gcloud
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    log_error "Não está logado no Google Cloud"
    echo "Execute: gcloud auth login"
    exit 1
fi

# Configurar projeto
log_info "Configurando projeto..."
gcloud config set project $PROJECT_ID

# Habilitar APIs necessárias
log_info "Habilitando APIs necessárias..."
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com

# Build da imagem
log_info "Fazendo build da imagem Docker..."
cd backend

# Verificar se Dockerfile existe
if [ ! -f "Dockerfile" ]; then
    log_error "Dockerfile não encontrado no diretório backend"
    exit 1
fi

# Build e push da imagem
gcloud builds submit --tag $IMAGE_NAME .

log_success "Imagem criada: $IMAGE_NAME"

# Deploy no Cloud Run
log_info "Fazendo deploy no Cloud Run..."

# Verificar se arquivo de variáveis de ambiente existe
if [ ! -f "../cloud-run.env" ]; then
    log_warning "Arquivo cloud-run.env não encontrado"
    log_info "Criando arquivo de exemplo..."
    
    cat > ../cloud-run.env << 'EOF'
NODE_ENV=production
PORT=8080
JWT_SECRET=sua_chave_jwt_super_secreta_aqui
STRIPE_SECRET_KEY=sk_live_sua_chave_secreta_stripe
STRIPE_WEBHOOK_SECRET=whsec_sua_chave_webhook_stripe
FRONTEND_URL=https://promandato-9a4cf.web.app
ALLOWED_ORIGINS=https://promandato-9a4cf.web.app,https://promandato.web.app
ADMIN_EMAIL=<EMAIL>
EOF
    
    log_error "Configure as variáveis em cloud-run.env antes de continuar"
    exit 1
fi

# Deploy com variáveis de ambiente
gcloud run deploy $SERVICE_NAME \
    --image $IMAGE_NAME \
    --platform managed \
    --region $REGION \
    --allow-unauthenticated \
    --port 8080 \
    --memory 1Gi \
    --cpu 1 \
    --min-instances 0 \
    --max-instances 10 \
    --timeout 300 \
    --concurrency 80 \
    --env-vars-file ../cloud-run.env

# Obter URL do serviço
SERVICE_URL=$(gcloud run services describe $SERVICE_NAME --platform managed --region $REGION --format 'value(status.url)')

log_success "Deploy concluído!"
echo ""
echo "🌐 URL do serviço: $SERVICE_URL"
echo "🔍 Health check: $SERVICE_URL/api/health"
echo "📊 Admin dashboard: $SERVICE_URL/admin"
echo ""

# Testar health check
log_info "Testando health check..."
if curl -f "$SERVICE_URL/api/health" > /dev/null 2>&1; then
    log_success "Health check passou!"
else
    log_warning "Health check falhou - verifique os logs"
fi

# Mostrar logs recentes
log_info "Logs recentes do serviço:"
gcloud run services logs read $SERVICE_NAME --platform managed --region $REGION --limit 10

echo ""
log_success "Deploy concluído com sucesso!"
echo ""
echo "📋 PRÓXIMOS PASSOS:"
echo "1. Configure o domínio customizado (se necessário)"
echo "2. Configure os webhooks do Stripe para: $SERVICE_URL/api/stripe/webhook"
echo "3. Atualize as variáveis de ambiente do frontend com a nova URL"
echo "4. Teste o fluxo completo de pagamento"
echo ""

cd ..