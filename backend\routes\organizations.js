import express from 'express';
import { body, validationResult } from 'express-validator';
import { authenticateToken, requireAdmin } from '../middleware/auth.js';
import Organization from '../models/Organization.js';
import { getPlanInfo } from '../middleware/planLimits.js';

const router = express.Router();

/**
 * Listar todas as organizações (apenas admins globais)
 */
router.get('/',
  authenticateToken,
  requireAdmin,
  async (req, res) => {
    try {
      const organization = new Organization();
      const organizations = await organization.getAll();

      res.json({
        success: true,
        data: organizations.map(org => ({
          ...org,
          // Não retornar informações sensíveis
          settings: {
            ...org.settings,
            // Manter apenas configurações públicas
          }
        }))
      });
    } catch (error) {
      console.error('Erro ao listar organizações:', error);
      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      });
    }
  }
);

/**
 * Obter organização específica
 */
router.get('/:id',
  authenticateToken,
  async (req, res) => {
    try {
      const { id } = req.params;
      const organization = new Organization();
      const org = await organization.getById(id);

      if (!org) {
        return res.status(404).json({
          success: false,
          message: 'Organização não encontrada'
        });
      }

      // Verificar permissões
      const isAdmin = req.user.role === 'ADMIN' && !req.user.organizationId;
      const isOrgUser = req.user.organizationId === id;

      if (!isAdmin && !isOrgUser) {
        return res.status(403).json({
          success: false,
          message: 'Acesso negado'
        });
      }

      res.json({
        success: true,
        data: org
      });
    } catch (error) {
      console.error('Erro ao obter organização:', error);
      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      });
    }
  }
);

/**
 * Criar nova organização (apenas admins globais)
 */
router.post('/',
  authenticateToken,
  requireAdmin,
  [
    body('name').notEmpty().withMessage('Nome é obrigatório'),
    body('slug').notEmpty().withMessage('Identificador é obrigatório')
      .matches(/^[a-z0-9-]+$/).withMessage('Identificador deve conter apenas letras minúsculas, números e hífens'),
    body('email').isEmail().withMessage('Email inválido'),
    body('planId').isIn(['basic', 'standard', 'professional']).withMessage('Plano inválido')
  ],
  async (req, res) => {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Dados inválidos',
          errors: errors.array()
        });
      }

      const organization = new Organization();
      const newOrg = await organization.create({
        ...req.body,
        createdBy: req.user.id
      });

      res.status(201).json({
        success: true,
        message: 'Organização criada com sucesso',
        data: newOrg
      });
    } catch (error) {
      console.error('Erro ao criar organização:', error);
      
      if (error.message.includes('já existe')) {
        return res.status(409).json({
          success: false,
          message: error.message
        });
      }

      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      });
    }
  }
);

/**
 * Atualizar organização
 */
router.put('/:id',
  authenticateToken,
  [
    body('name').optional().notEmpty().withMessage('Nome não pode estar vazio'),
    body('email').optional().isEmail().withMessage('Email inválido'),
    body('planId').optional().isIn(['basic', 'standard', 'professional']).withMessage('Plano inválido')
  ],
  async (req, res) => {
    try {
      const { id } = req.params;
      const errors = validationResult(req);
      
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Dados inválidos',
          errors: errors.array()
        });
      }

      // Verificar permissões
      const isAdmin = req.user.role === 'ADMIN' && !req.user.organizationId;
      const isOrgAdmin = req.user.organizationId === id && req.user.role === 'ADMIN';

      if (!isAdmin && !isOrgAdmin) {
        return res.status(403).json({
          success: false,
          message: 'Acesso negado'
        });
      }

      const organization = new Organization();
      const updatedOrg = await organization.update(id, req.body);

      res.json({
        success: true,
        message: 'Organização atualizada com sucesso',
        data: updatedOrg
      });
    } catch (error) {
      console.error('Erro ao atualizar organização:', error);
      
      if (error.message.includes('não encontrada')) {
        return res.status(404).json({
          success: false,
          message: error.message
        });
      }

      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      });
    }
  }
);

/**
 * Obter estatísticas de uso da organização
 */
router.get('/:id/usage',
  authenticateToken,
  getPlanInfo,
  async (req, res) => {
    try {
      const { id } = req.params;

      // Verificar permissões
      const isAdmin = req.user.role === 'ADMIN' && !req.user.organizationId;
      const isOrgUser = req.user.organizationId === id;

      if (!isAdmin && !isOrgUser) {
        return res.status(403).json({
          success: false,
          message: 'Acesso negado'
        });
      }

      const organization = new Organization();
      const org = await organization.getById(id);

      if (!org) {
        return res.status(404).json({
          success: false,
          message: 'Organização não encontrada'
        });
      }

      // Calcular percentuais de uso
      const usagePercentages = {};
      Object.keys(org.limits).forEach(key => {
        const limit = org.limits[key];
        const usage = org.usage[key] || 0;
        
        if (limit === -1) {
          usagePercentages[key] = 0; // Ilimitado
        } else {
          usagePercentages[key] = Math.round((usage / limit) * 100);
        }
      });

      res.json({
        success: true,
        data: {
          planId: org.planId,
          limits: org.limits,
          usage: org.usage,
          usagePercentages,
          features: org.features,
          status: org.status
        }
      });
    } catch (error) {
      console.error('Erro ao obter estatísticas de uso:', error);
      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      });
    }
  }
);

/**
 * Obter planos disponíveis
 */
router.get('/plans/available',
  authenticateToken,
  async (req, res) => {
    try {
      const plans = {
        basic: {
          id: 'basic',
          name: 'Básico',
          description: 'Plano básico para pequenas organizações',
          price: 'R$ 99/mês',
          limits: {
            users: 5,
            demands: 100,
            citizens: 500,
            storage: '10GB',
            apiCalls: 1000
          },
          features: [
            'Exportação de dados',
            'Suporte básico'
          ],
          aiFeatures: []
        },
        standard: {
          id: 'standard',
          name: 'Padrão',
          description: 'Plano intermediário com IA básica',
          price: 'R$ 299/mês',
          limits: {
            users: 15,
            demands: 500,
            citizens: 2000,
            storage: '50GB',
            apiCalls: 10000
          },
          features: [
            'Mensagens em massa',
            'Integração com redes sociais',
            'Relatórios avançados',
            'Exportação de dados',
            'Acesso à API',
            'Suporte prioritário'
          ],
          aiFeatures: [
            'Análise de Sentimento'
          ]
        },
        professional: {
          id: 'professional',
          name: 'Profissional',
          description: 'Plano completo com IA avançada',
          price: 'R$ 599/mês',
          limits: {
            users: 'Ilimitado',
            demands: 'Ilimitado',
            citizens: 'Ilimitado',
            storage: '200GB',
            apiCalls: 'Ilimitado'
          },
          features: [
            'Mensagens em massa',
            'Integração com redes sociais',
            'Notificações SMS',
            'Relatórios avançados',
            'Relatórios personalizados',
            'Exportação de dados',
            'Acesso à API',
            'Webhooks',
            'Suporte dedicado'
          ],
          aiFeatures: [
            'Análise de Sentimento',
            'Categorização Automática',
            'Análise Preditiva',
            'Respostas Inteligentes'
          ]
        }
      };

      res.json({
        success: true,
        data: plans
      });
    } catch (error) {
      console.error('Erro ao obter planos:', error);
      res.status(500).json({
        success: false,
        message: 'Erro interno do servidor'
      });
    }
  }
);

export default router;
