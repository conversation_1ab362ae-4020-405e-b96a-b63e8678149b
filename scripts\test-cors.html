<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste CORS - ProMandato</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Teste de CORS - ProMandato</h1>
        <p>Esta página testa se o CORS está configurado corretamente entre o frontend e backend.</p>
        
        <div class="test-result info">
            <strong>Origem atual:</strong> <span id="currentOrigin"></span>
        </div>
        
        <h2>Testes Disponíveis</h2>
        
        <button onclick="testHealthEndpoint()">Testar Health Check</button>
        <button onclick="testCorsDebug()">Testar CORS Debug</button>
        <button onclick="testAuthLogin()">Testar Login (CORS)</button>
        <button onclick="clearResults()">Limpar Resultados</button>
        
        <div id="results"></div>
    </div>

    <script>
        // URLs para teste
        const BACKEND_URLS = [
            'https://promandato-backend-517140455601.southamerica-east1.run.app',
            'http://localhost:3002'
        ];
        
        // Detectar URL do backend baseado no ambiente
        const getBackendUrl = () => {
            const isLocal = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
            return isLocal ? BACKEND_URLS[1] : BACKEND_URLS[0];
        };
        
        // Mostrar origem atual
        document.getElementById('currentOrigin').textContent = window.location.origin;
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testHealthEndpoint() {
            const backendUrl = getBackendUrl();
            addResult(`<strong>Testando Health Check:</strong> ${backendUrl}/api/health`, 'info');
            
            try {
                const response = await fetch(`${backendUrl}/api/health`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ Health Check OK: ${data.message}`, 'success');
                    addResult(`<pre>${JSON.stringify(data, null, 2)}</pre>`, 'info');
                } else {
                    addResult(`❌ Health Check falhou: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Erro no Health Check: ${error.message}`, 'error');
                if (error.message.includes('CORS')) {
                    addResult(`🔧 Erro de CORS detectado! Verifique a configuração do servidor.`, 'error');
                }
            }
        }
        
        async function testCorsDebug() {
            const backendUrl = getBackendUrl();
            addResult(`<strong>Testando CORS Debug:</strong> ${backendUrl}/api/cors-debug`, 'info');
            
            try {
                const response = await fetch(`${backendUrl}/api/cors-debug`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    addResult(`✅ CORS Debug OK`, 'success');
                    addResult(`<pre>${JSON.stringify(data, null, 2)}</pre>`, 'info');
                    
                    // Analisar resultado
                    if (data.data && data.data.isOriginAllowed) {
                        addResult(`✅ Sua origem está permitida no CORS`, 'success');
                    } else {
                        addResult(`❌ Sua origem NÃO está permitida no CORS`, 'error');
                    }
                } else {
                    addResult(`❌ CORS Debug falhou: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                addResult(`❌ Erro no CORS Debug: ${error.message}`, 'error');
            }
        }
        
        async function testAuthLogin() {
            const backendUrl = getBackendUrl();
            addResult(`<strong>Testando Login (CORS):</strong> ${backendUrl}/api/auth/login`, 'info');
            
            try {
                const response = await fetch(`${backendUrl}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'test123'
                    })
                });
                
                // Esperamos que falhe com 401 (credenciais inválidas), mas não com CORS
                if (response.status === 401) {
                    addResult(`✅ CORS OK para login (401 esperado - credenciais inválidas)`, 'success');
                } else if (response.ok) {
                    addResult(`✅ CORS OK para login (resposta inesperada mas sem erro de CORS)`, 'success');
                } else {
                    const text = await response.text();
                    addResult(`⚠️ Login retornou: ${response.status} ${response.statusText}`, 'info');
                    addResult(`<pre>${text}</pre>`, 'info');
                }
            } catch (error) {
                addResult(`❌ Erro no teste de login: ${error.message}`, 'error');
                if (error.message.includes('CORS')) {
                    addResult(`🔧 Erro de CORS detectado no endpoint de login!`, 'error');
                }
            }
        }
        
        // Executar teste inicial
        window.onload = function() {
            addResult(`🚀 Página carregada. Backend URL: ${getBackendUrl()}`, 'info');
        };
    </script>
</body>
</html>
