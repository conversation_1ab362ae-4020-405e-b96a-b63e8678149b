import NotificationService from '../services/notificationService.js';
import User from '../models/User.js';

/**
 * Script para criar notificações de exemplo
 * Execute com: node scripts/seedNotifications.js
 */

async function seedNotifications() {
  try {
    console.log('🌱 Iniciando seed de notificações...');

    // Buscar todos os usuários
    const users = await User.findAll();
    
    if (users.length === 0) {
      console.log('❌ Nenhum usuário encontrado. Crie usuários primeiro.');
      return;
    }

    console.log(`📋 Encontrados ${users.length} usuários`);

    // Criar notificações de exemplo para cada usuário
    for (const user of users) {
      console.log(`📢 Criando notificações para ${user.name} (${user.email})`);

      // Notificação de nova demanda
      await NotificationService.notifyUser(
        user.id,
        'Nova demanda recebida',
        '<PERSON> en<PERSON>u uma solicitação sobre iluminação pública na Rua das Flores',
        {
          type: 'info',
          category: 'demand',
          actionUrl: '/demands/1',
          actionLabel: 'Ver demanda',
          metadata: {
            demandId: '1',
            category: 'infraestrutura',
            priority: 'alta'
          }
        }
      );

      // Notificação de evento próximo
      await NotificationService.notifyUser(
        user.id,
        'Evento próximo',
        'Reunião pública agendada para amanhã às 14h no Centro Comunitário',
        {
          type: 'warning',
          category: 'event',
          actionUrl: '/agenda',
          actionLabel: 'Ver agenda',
          metadata: {
            eventId: '1',
            eventDate: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
            location: 'Centro Comunitário'
          }
        }
      );

      // Notificação de demanda resolvida
      await NotificationService.notifyUser(
        user.id,
        'Demanda resolvida',
        'A solicitação #123 sobre reparo de calçada foi marcada como concluída',
        {
          type: 'success',
          category: 'demand',
          actionUrl: '/demands/123',
          actionLabel: 'Ver detalhes',
          metadata: {
            demandId: '123',
            category: 'infraestrutura',
            resolvedBy: user.id
          }
        }
      );

      // Notificação de prazo vencendo
      await NotificationService.notifyUser(
        user.id,
        'Prazo vencendo',
        'A demanda #456 sobre limpeza urbana vence em 2 dias',
        {
          type: 'error',
          category: 'demand',
          actionUrl: '/demands/456',
          actionLabel: 'Ver demanda',
          metadata: {
            demandId: '456',
            daysRemaining: 2,
            deadline: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString()
          }
        }
      );

      // Notificação do sistema
      await NotificationService.notifyUser(
        user.id,
        'Atualização do sistema',
        'O sistema foi atualizado com novas funcionalidades de relatórios',
        {
          type: 'info',
          category: 'system',
          actionUrl: '/reports',
          actionLabel: 'Ver relatórios',
          metadata: {
            version: '2.1.0',
            updateType: 'feature'
          }
        }
      );

      // Notificação geral
      await NotificationService.notifyUser(
        user.id,
        'Bem-vindo ao ProMandato',
        'Sua conta foi configurada com sucesso. Explore as funcionalidades disponíveis.',
        {
          type: 'success',
          category: 'general',
          actionUrl: '/dashboard',
          actionLabel: 'Ir para dashboard',
          metadata: {
            welcomeMessage: true,
            userRole: user.role
          }
        }
      );

      console.log(`✅ 6 notificações criadas para ${user.name}`);
    }

    console.log('🎉 Seed de notificações concluído com sucesso!');

  } catch (error) {
    console.error('❌ Erro ao executar seed de notificações:', error);
    process.exit(1);
  }
}

// Executar apenas se chamado diretamente
if (import.meta.url === `file://${process.argv[1]}`) {
  seedNotifications()
    .then(() => {
      console.log('✨ Script finalizado');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Erro fatal:', error);
      process.exit(1);
    });
}

export default seedNotifications;
