/* Estilos para componentes de IA */

.ai-features {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.ai-features__header {
  margin-bottom: 32px;
  text-align: center;
}

.ai-features__header h2 {
  font-size: 2rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 8px;
}

.ai-features__header p {
  color: #718096;
  font-size: 1.1rem;
}

/* Métricas */
.ai-features__metrics {
  margin-bottom: 32px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.metric-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  text-align: center;
}

.metric-card h3 {
  font-size: 0.875rem;
  font-weight: 500;
  color: #718096;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.metric-value {
  font-size: 2rem;
  font-weight: 700;
  color: #2d3748;
}

/* Funcionalidades */
.ai-features__available {
  margin-bottom: 32px;
}

.ai-features__available h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.feature-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 2px solid #e2e8f0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.feature-card:hover {
  border-color: #4299e1;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.feature-card.selected {
  border-color: #3182ce;
  background: #f7fafc;
}

.feature-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.feature-header h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0;
}

.feature-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.feature-status.enabled {
  background: #c6f6d5;
  color: #22543d;
}

.feature-status.disabled {
  background: #fed7d7;
  color: #742a2a;
}

.feature-description {
  color: #718096;
  margin-bottom: 16px;
  line-height: 1.5;
}

.feature-usage {
  margin-bottom: 16px;
}

.usage-bar {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.usage-fill {
  height: 100%;
  background: linear-gradient(90deg, #48bb78, #38a169);
  transition: width 0.3s ease;
}

.usage-text {
  font-size: 0.875rem;
  color: #718096;
}

.feature-actions {
  display: flex;
  gap: 8px;
}

.btn-test {
  padding: 8px 16px;
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
}

.btn-test:hover:not(:disabled) {
  background: #3182ce;
}

.btn-test:disabled {
  background: #a0aec0;
  cursor: not-allowed;
}

/* Área de Teste */
.ai-features__test {
  margin-bottom: 32px;
}

.ai-features__test h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16px;
}

.test-area {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.test-input {
  width: 100%;
  padding: 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 1rem;
  line-height: 1.5;
  resize: vertical;
  margin-bottom: 16px;
  font-family: inherit;
}

.test-input:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.test-actions {
  display: flex;
  gap: 12px;
}

.btn-primary {
  padding: 12px 24px;
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.2s ease;
}

.btn-primary:hover:not(:disabled) {
  background: #3182ce;
}

.btn-primary:disabled {
  background: #a0aec0;
  cursor: not-allowed;
}

.btn-secondary {
  padding: 12px 24px;
  background: white;
  color: #4a5568;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary:hover {
  border-color: #cbd5e0;
  background: #f7fafc;
}

/* Resultado */
.ai-features__result {
  margin-bottom: 32px;
}

.ai-features__result h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16px;
}

.result-container {
  background: #1a202c;
  border-radius: 12px;
  padding: 24px;
  overflow-x: auto;
}

.result-json {
  color: #e2e8f0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  margin: 0;
  white-space: pre-wrap;
}

/* Mensagens */
.ai-features__error,
.ai-features__success {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.error-message,
.success-message {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  max-width: 400px;
}

.error-message {
  background: #fed7d7;
  color: #742a2a;
  border: 1px solid #feb2b2;
}

.success-message {
  background: #c6f6d5;
  color: #22543d;
  border: 1px solid #9ae6b4;
}

.error-close,
.success-close {
  background: none;
  border: none;
  font-size: 1.25rem;
  cursor: pointer;
  color: inherit;
  opacity: 0.7;
}

.error-close:hover,
.success-close:hover {
  opacity: 1;
}

/* Detalhes da Funcionalidade */
.ai-features__details {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
}

.ai-features__details h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 16px;
}

.feature-details h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
}

.feature-details p {
  color: #718096;
  margin-bottom: 20px;
  line-height: 1.6;
}

.details-section {
  margin-bottom: 20px;
}

.details-section h5 {
  font-size: 1rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
}

.details-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.details-section li {
  padding: 8px 0;
  padding-left: 20px;
  position: relative;
  color: #4a5568;
  line-height: 1.5;
}

.details-section li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #48bb78;
  font-weight: bold;
}

/* Insights */
.ai-insights {
  padding: 24px;
}

.insights-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.insights-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a202c;
}

.btn-refresh {
  padding: 8px 16px;
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
}

.btn-refresh:hover:not(:disabled) {
  background: #3182ce;
}

.btn-refresh:disabled {
  background: #a0aec0;
  cursor: not-allowed;
}

.insights-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.insight-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #e2e8f0;
}

.insight-card.trend {
  border-left-color: #4299e1;
}

.insight-card.anomaly {
  border-left-color: #f56565;
}

.insight-card.recommendation {
  border-left-color: #48bb78;
}

.insight-card.alert {
  border-left-color: #ed8936;
}

.insight-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.insight-type {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.insight-type.trend {
  background: #bee3f8;
  color: #2c5282;
}

.insight-type.anomaly {
  background: #fed7d7;
  color: #742a2a;
}

.insight-type.recommendation {
  background: #c6f6d5;
  color: #22543d;
}

.insight-type.alert {
  background: #fbd38d;
  color: #7b341e;
}

.insight-impact {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.insight-impact.low {
  background: #e6fffa;
  color: #234e52;
}

.insight-impact.medium {
  background: #fefcbf;
  color: #744210;
}

.insight-impact.high {
  background: #fed7d7;
  color: #742a2a;
}

.insight-card h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 8px;
}

.insight-card p {
  color: #718096;
  line-height: 1.5;
  margin-bottom: 12px;
}

.insight-confidence {
  font-size: 0.875rem;
  color: #4a5568;
  margin-bottom: 8px;
}

.insight-action {
  margin-top: 12px;
}

.action-required {
  padding: 6px 12px;
  background: #fed7d7;
  color: #742a2a;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
}

.upgrade-notice {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.upgrade-notice h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 8px;
}

.upgrade-notice p {
  color: #718096;
}

.no-insights {
  text-align: center;
  padding: 40px;
  color: #718096;
}

/* Responsividade */
@media (max-width: 768px) {
  .ai-features {
    padding: 16px;
  }
  
  .metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
  
  .features-grid {
    grid-template-columns: 1fr;
  }
  
  .insights-grid {
    grid-template-columns: 1fr;
  }
  
  .insights-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .test-actions {
    flex-direction: column;
  }
  
  .ai-features__error,
  .ai-features__success {
    position: relative;
    top: auto;
    right: auto;
    margin-bottom: 20px;
  }
}