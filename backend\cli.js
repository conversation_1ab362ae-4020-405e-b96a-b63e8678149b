#!/usr/bin/env node

const { program } = require('commander');
const User = require('./models/User');
const SystemInitializer = require('./utils/initializeSystem');
const AuthLogger = require('./utils/authLogger');
const config = require('./config');

program
  .name('promandato-cli')
  .description('CLI para gerenciar o sistema Promandato')
  .version('1.0.0');

// Comando para inicializar o sistema
program
  .command('init')
  .description('Inicializar o sistema com dados padrão')
  .action(async () => {
    try {
      console.log('🚀 Inicializando sistema...');
      await SystemInitializer.initialize();
      console.log('✅ Sistema inicializado com sucesso!');
    } catch (error) {
      console.error('❌ Erro ao inicializar sistema:', error.message);
      process.exit(1);
    }
  });

// Comando para resetar o sistema
program
  .command('reset')
  .description('Resetar o sistema (cria backup antes)')
  .option('-f, --force', 'Forçar reset sem confirmação')
  .action(async (options) => {
    try {
      if (!options.force) {
        const readline = require('readline');
        const rl = readline.createInterface({
          input: process.stdin,
          output: process.stdout
        });

        const answer = await new Promise(resolve => {
          rl.question('⚠️  Tem certeza que deseja resetar o sistema? (y/N): ', resolve);
        });
        rl.close();

        if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
          console.log('❌ Reset cancelado');
          return;
        }
      }

      console.log('🔄 Resetando sistema...');
      await SystemInitializer.resetSystem();
      console.log('✅ Sistema resetado com sucesso!');
    } catch (error) {
      console.error('❌ Erro ao resetar sistema:', error.message);
      process.exit(1);
    }
  });

// Comando para criar usuário
program
  .command('create-user')
  .description('Criar um novo usuário')
  .requiredOption('-e, --email <email>', 'Email do usuário')
  .requiredOption('-p, --password <password>', 'Senha do usuário')
  .requiredOption('-n, --name <name>', 'Nome do usuário')
  .option('-r, --role <role>', 'Role do usuário (USER, MANAGER, ADMIN)', 'USER')
  .action(async (options) => {
    try {
      const { email, password, name, role } = options;

      // Validar role
      if (!['USER', 'MANAGER', 'ADMIN'].includes(role)) {
        console.error('❌ Role inválida. Use: USER, MANAGER ou ADMIN');
        process.exit(1);
      }

      // Verificar se o email já existe
      const existingUser = await User.findByEmail(email);
      if (existingUser) {
        console.error('❌ Email já está em uso');
        process.exit(1);
      }

      // Criar usuário
      const userData = {
        email,
        password,
        name,
        role,
        isActive: true,
        emailVerified: true
      };

      const user = await User.create(userData);
      console.log('✅ Usuário criado com sucesso!');
      console.log('📧 Email:', user.email);
      console.log('👤 Nome:', user.name);
      console.log('🔑 Role:', user.role);
      console.log('🆔 ID:', user.id);

    } catch (error) {
      console.error('❌ Erro ao criar usuário:', error.message);
      process.exit(1);
    }
  });

// Comando para listar usuários
program
  .command('list-users')
  .description('Listar todos os usuários')
  .option('-r, --role <role>', 'Filtrar por role')
  .option('-a, --active', 'Mostrar apenas usuários ativos')
  .option('-i, --inactive', 'Mostrar apenas usuários inativos')
  .action(async (options) => {
    try {
      const filters = {};
      
      if (options.role) {
        filters.role = options.role;
      }
      
      if (options.active) {
        filters.isActive = true;
      } else if (options.inactive) {
        filters.isActive = false;
      }

      const users = await User.findAll(filters);
      
      if (users.length === 0) {
        console.log('📭 Nenhum usuário encontrado');
        return;
      }

      console.log(`👥 ${users.length} usuário(s) encontrado(s):\n`);
      
      users.forEach(user => {
        const status = user.isActive ? '✅' : '❌';
        const locked = user.isLocked() ? '🔒' : '';
        console.log(`${status} ${locked} ${user.name} (${user.email})`);
        console.log(`   🔑 Role: ${user.role}`);
        console.log(`   🆔 ID: ${user.id}`);
        console.log(`   📅 Criado: ${new Date(user.createdAt).toLocaleString()}`);
        if (user.lastLogin) {
          console.log(`   🕐 Último login: ${new Date(user.lastLogin).toLocaleString()}`);
        }
        console.log('');
      });

    } catch (error) {
      console.error('❌ Erro ao listar usuários:', error.message);
      process.exit(1);
    }
  });

// Comando para alterar senha
program
  .command('change-password')
  .description('Alterar senha de um usuário')
  .requiredOption('-e, --email <email>', 'Email do usuário')
  .requiredOption('-p, --password <password>', 'Nova senha')
  .action(async (options) => {
    try {
      const { email, password } = options;

      const user = await User.findByEmail(email);
      if (!user) {
        console.error('❌ Usuário não encontrado');
        process.exit(1);
      }

      await user.updatePassword(password);
      console.log('✅ Senha alterada com sucesso!');
      console.log('👤 Usuário:', user.name);
      console.log('📧 Email:', user.email);

    } catch (error) {
      console.error('❌ Erro ao alterar senha:', error.message);
      process.exit(1);
    }
  });

// Comando para ativar/desativar usuário
program
  .command('toggle-user')
  .description('Ativar ou desativar um usuário')
  .requiredOption('-e, --email <email>', 'Email do usuário')
  .action(async (options) => {
    try {
      const { email } = options;

      const user = await User.findByEmail(email);
      if (!user) {
        console.error('❌ Usuário não encontrado');
        process.exit(1);
      }

      user.isActive = !user.isActive;
      await user.save();

      const status = user.isActive ? 'ativado' : 'desativado';
      console.log(`✅ Usuário ${status} com sucesso!`);
      console.log('👤 Usuário:', user.name);
      console.log('📧 Email:', user.email);
      console.log('🔄 Status:', user.isActive ? 'Ativo' : 'Inativo');

    } catch (error) {
      console.error('❌ Erro ao alterar status do usuário:', error.message);
      process.exit(1);
    }
  });

// Comando para desbloquear usuário
program
  .command('unlock-user')
  .description('Desbloquear um usuário')
  .requiredOption('-e, --email <email>', 'Email do usuário')
  .action(async (options) => {
    try {
      const { email } = options;

      const user = await User.findByEmail(email);
      if (!user) {
        console.error('❌ Usuário não encontrado');
        process.exit(1);
      }

      if (!user.isLocked()) {
        console.log('ℹ️  Usuário não está bloqueado');
        return;
      }

      await user.resetLoginAttempts();
      console.log('✅ Usuário desbloqueado com sucesso!');
      console.log('👤 Usuário:', user.name);
      console.log('📧 Email:', user.email);

    } catch (error) {
      console.error('❌ Erro ao desbloquear usuário:', error.message);
      process.exit(1);
    }
  });

// Comando para ver logs de autenticação
program
  .command('auth-logs')
  .description('Ver logs de autenticação')
  .option('-e, --email <email>', 'Filtrar por email')
  .option('-t, --type <type>', 'Filtrar por tipo de evento')
  .option('-l, --limit <limit>', 'Limitar número de resultados', '20')
  .action(async (options) => {
    try {
      const filters = {
        limit: parseInt(options.limit)
      };

      if (options.email) {
        filters.email = options.email;
      }

      if (options.type) {
        filters.event = options.type;
      }

      const logs = await AuthLogger.getLogs(filters);

      if (logs.length === 0) {
        console.log('📭 Nenhum log encontrado');
        return;
      }

      console.log(`📋 ${logs.length} log(s) encontrado(s):\n`);

      logs.forEach(log => {
        const timestamp = new Date(log.timestamp).toLocaleString();
        const success = log.data.success !== undefined ? (log.data.success ? '✅' : '❌') : 'ℹ️';
        
        console.log(`${success} ${log.event} - ${timestamp}`);
        if (log.data.email) console.log(`   📧 Email: ${log.data.email}`);
        if (log.data.ip) console.log(`   🌐 IP: ${log.data.ip}`);
        if (log.data.reason) console.log(`   📝 Motivo: ${log.data.reason}`);
        console.log('');
      });

    } catch (error) {
      console.error('❌ Erro ao buscar logs:', error.message);
      process.exit(1);
    }
  });

// Comando para ver informações do sistema
program
  .command('system-info')
  .description('Ver informações do sistema')
  .action(async () => {
    try {
      const info = await SystemInitializer.getSystemInfo();
      const summary = await AuthLogger.getSecuritySummary();

      console.log('📊 Informações do Sistema\n');
      console.log('👥 Usuários:');
      console.log(`   Total: ${info.totalUsers}`);
      console.log(`   Ativos: ${info.activeUsers}`);
      console.log(`   Inativos: ${info.inactiveUsers}`);
      console.log(`   Administradores: ${info.adminUsers}`);
      console.log('');

      console.log('🔐 Segurança (últimas 24h):');
      console.log(`   Tentativas de login: ${summary.loginAttempts}`);
      console.log(`   Logins bem-sucedidos: ${summary.successfulLogins}`);
      console.log(`   Logins falharam: ${summary.failedLogins}`);
      console.log(`   Alterações de senha: ${summary.passwordChanges}`);
      console.log(`   IPs únicos: ${summary.uniqueIPs}`);
      console.log('');

      console.log('⚙️  Configurações:');
      console.log(`   Admin padrão existe: ${info.hasDefaultAdmin ? 'Sim' : 'Não'}`);
      console.log(`   Sistema inicializado: ${info.systemInitialized ? 'Sim' : 'Não'}`);

    } catch (error) {
      console.error('❌ Erro ao obter informações do sistema:', error.message);
      process.exit(1);
    }
  });

// Comando para criar usuários de teste
program
  .command('create-test-users')
  .description('Criar usuários de teste')
  .action(async () => {
    try {
      await SystemInitializer.createTestUsers();
    } catch (error) {
      console.error('❌ Erro ao criar usuários de teste:', error.message);
      process.exit(1);
    }
  });

program.parse();