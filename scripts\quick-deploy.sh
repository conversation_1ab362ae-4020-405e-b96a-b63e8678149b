#!/bin/bash

echo "========================================="
echo "    QUICK DEPLOY BACKEND - RATE LIMIT FIX"
echo "========================================="
echo

echo "[1/4] Navegando para backend..."
cd backend

echo "[2/4] Fazendo deploy rápido..."
gcloud run deploy promandato-backend \
  --source . \
  --platform managed \
  --region southamerica-east1 \
  --allow-unauthenticated \
  --port 8080 \
  --memory 2Gi \
  --cpu 2 \
  --min-instances 0 \
  --max-instances 10 \
  --timeout 300 \
  --concurrency 80 \
  --set-env-vars NODE_ENV=production,PORT=8080,DEBUG_CORS=true

if [ $? -eq 0 ]; then
    echo
    echo "========================================="
    echo "       DEPLOY CONCLUÍDO COM SUCESSO!"
    echo "========================================="
    echo
    echo "Testando endpoints..."
    echo
    echo "1. Health check:"
    curl -s https://promandato-backend-517140455601.southamerica-east1.run.app/health | jq .
    echo
    echo "2. Root endpoint:"
    curl -s https://promandato-backend-517140455601.southamerica-east1.run.app/ | jq .
    echo
else
    echo "ERRO: Deploy falhou!"
fi

echo
echo "Voltando ao diretório raiz..."
cd ..
