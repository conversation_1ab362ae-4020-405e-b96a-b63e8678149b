import React from 'react';
import { LoadingSpinner } from './LoadingSpinner';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  isLoading?: boolean;
  loadingText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

export const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  isLoading = false,
  loadingText,
  leftIcon,
  rightIcon,
  className = '',
  disabled,
  ...props
}) => {
  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2';
  
  const variants = {
    primary: `bg-primary hover:bg-primary-dark text-white dark:bg-primary-light dark:hover:bg-primary 
      focus:ring-primary dark:focus:ring-primary-light
      data-[contrast=high]:bg-black data-[contrast=high]:text-white data-[contrast=high]:border-2 data-[contrast=high]:border-white
      data-[contrast=high]:hover:bg-white data-[contrast=high]:hover:text-black`,
    secondary: `bg-neutral-DEFAULT hover:bg-neutral-dark text-white 
      focus:ring-neutral-dark dark:focus:ring-neutral-DEFAULT
      data-[contrast=high]:bg-white data-[contrast=high]:text-black data-[contrast=high]:border-2 data-[contrast=high]:border-black
      data-[contrast=high]:hover:bg-black data-[contrast=high]:hover:text-white`,
    outline: `border border-primary text-primary hover:bg-primary hover:text-white
      dark:border-primary-light dark:text-primary-light dark:hover:bg-primary-light dark:hover:text-neutral-darker
      focus:ring-primary dark:focus:ring-primary-light
      data-[contrast=high]:border-2 data-[contrast=high]:border-current`,
    ghost: `text-neutral-dark hover:bg-neutral-light dark:text-neutral-light dark:hover:bg-neutral-dark
      focus:ring-neutral-DEFAULT dark:focus:ring-neutral-light
      data-[contrast=high]:hover:bg-gray-200 data-[contrast=high]:text-current`
  } as const;

  const sizes = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  };

  return (
    <button
      className={`
        ${baseStyles}
        ${variants[variant]}
        ${sizes[size]}
        ${isLoading || disabled ? 'opacity-50 cursor-not-allowed' : ''}
        ${className}
      `}
      disabled={isLoading || disabled}
      {...props}
    >
      {isLoading ? (
        <>
          <LoadingSpinner size="sm" className="mr-2" />
          {loadingText || children}
        </>
      ) : leftIcon ? (
        <span className="mr-2">{leftIcon}</span>
      ) : null}
      {!isLoading && children}
      {rightIcon && <span className="ml-2">{rightIcon}</span>}
    </button>
  );
};

