import React from 'react';
import { FILTER_OPTIONS } from '../constants/paymentConstants';
import { FilterValue } from '../types/payment.types';

interface PaymentFiltersProps {
  dateFilter: FilterValue;
  statusFilter: FilterValue;
  planFilter: FilterValue;
  onDateFilterChange: (value: FilterValue) => void;
  onStatusFilterChange: (value: FilterValue) => void;
  onPlanFilterChange: (value: FilterValue) => void;
}

const PaymentFilters: React.FC<PaymentFiltersProps> = ({
  dateFilter,
  statusFilter,
  planFilter,
  onDateFilterChange,
  onStatusFilterChange,
  onPlanFilterChange
}) => {
  return (
    <div className="bg-white p-4 rounded-lg shadow mb-6">
      <div className="flex flex-col sm:flex-row gap-4">
        <select
          aria-label="Filtrar por data"
          value={dateFilter}
          onChange={(e) => onDateFilterChange(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          {FILTER_OPTIONS.DATE.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        
        <select 
          aria-label="Filtrar por status"
          value={statusFilter}
          onChange={(e) => onStatusFilterChange(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          {FILTER_OPTIONS.STATUS.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        
        <select
          aria-label="Filtrar por plano"
          value={planFilter}
          onChange={(e) => onPlanFilterChange(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          {FILTER_OPTIONS.PLAN.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
};

export default PaymentFilters;
