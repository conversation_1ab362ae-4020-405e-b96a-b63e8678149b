/**
 * Serviço de notificações para teste (sem autenticação)
 */

export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  category: 'demand' | 'event' | 'system' | 'general';
  read: boolean;
  actionUrl?: string;
  actionLabel?: string;
  metadata: Record<string, any>;
  createdAt: string;
  readAt?: string;
  expiresAt?: string;
}

class NotificationServiceTest {
  private baseURL: string;

  constructor() {
    this.baseURL = 'http://localhost:3002/api';
  }

  /**
   * Fazer requisição HTTP simples (sem autenticação para teste)
   */
  private async makeRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    console.log('📡 Fazendo requisição para:', `${this.baseURL}${endpoint}`);
    
    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    const response = await fetch(`${this.baseURL}${endpoint}`, config);
    
    console.log('📊 Status da resposta:', response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ Erro na requisição:', response.status, errorText);
      throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
    }
    
    const data = await response.json();
    console.log('✅ Dados recebidos:', data);
    return data;
  }

  /**
   * Buscar notificações (versão de teste)
   */
  async getNotifications(): Promise<Notification[]> {
    try {
      // Retornar dados mock para teste
      const mockNotifications: Notification[] = [
        {
          id: '1',
          userId: 'admin',
          title: 'Teste de Notificação',
          message: 'Esta é uma notificação de teste do dashboard',
          type: 'info',
          category: 'system',
          read: false,
          actionUrl: '/test',
          actionLabel: 'Ver teste',
          metadata: {},
          createdAt: new Date().toISOString()
        },
        {
          id: '2',
          userId: 'admin',
          title: 'Nova demanda urgente',
          message: 'Problema crítico reportado na Rua Principal',
          type: 'error',
          category: 'demand',
          read: false,
          actionUrl: '/demands/urgent',
          actionLabel: 'Ver demanda',
          metadata: {},
          createdAt: new Date(Date.now() - 5 * 60 * 1000).toISOString()
        },
        {
          id: '3',
          userId: 'admin',
          title: 'Sistema atualizado',
          message: 'Dashboard foi atualizado com sucesso',
          type: 'success',
          category: 'system',
          read: true,
          actionUrl: '/changelog',
          actionLabel: 'Ver novidades',
          metadata: {},
          createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          readAt: new Date(Date.now() - 10 * 60 * 1000).toISOString()
        }
      ];

      console.log('📋 Retornando notificações mock:', mockNotifications.length);
      return mockNotifications;
    } catch (error) {
      console.error('❌ Erro ao buscar notificações:', error);
      return [];
    }
  }

  /**
   * Buscar contagem de notificações não lidas
   */
  async getUnreadCount(): Promise<number> {
    try {
      const notifications = await this.getNotifications();
      const unreadCount = notifications.filter(n => !n.read).length;
      console.log('🔔 Contagem não lidas:', unreadCount);
      return unreadCount;
    } catch (error) {
      console.error('❌ Erro ao buscar contagem de não lidas:', error);
      return 0;
    }
  }

  /**
   * Marcar notificação como lida (mock)
   */
  async markAsRead(id: string): Promise<boolean> {
    console.log('✅ Marcando como lida (mock):', id);
    return true;
  }

  /**
   * Marcar todas as notificações como lidas (mock)
   */
  async markAllAsRead(): Promise<boolean> {
    console.log('✅ Marcando todas como lidas (mock)');
    return true;
  }

  /**
   * Deletar notificação (mock)
   */
  async deleteNotification(id: string): Promise<boolean> {
    console.log('🗑️ Deletando notificação (mock):', id);
    return true;
  }

  /**
   * Formatar timestamp relativo
   */
  formatRelativeTime(timestamp: string): string {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Agora';
    if (diffInMinutes < 60) return `${diffInMinutes}m atrás`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h atrás`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d atrás`;
    
    return date.toLocaleDateString('pt-BR');
  }

  /**
   * Obter ícone baseado no tipo da notificação
   */
  getTypeIcon(type: string): string {
    const icons = {
      info: '📢',
      success: '✅',
      warning: '⚠️',
      error: '❌'
    };
    return icons[type as keyof typeof icons] || icons.info;
  }
}

// Exportar instância única
export const notificationServiceTest = new NotificationServiceTest();
export default notificationServiceTest;
