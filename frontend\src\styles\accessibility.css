/* Estilos de Acessibilidade */

/* Alto Contraste */
.high-contrast {
  --primary: #000000;
  --primary-dark: #000000;
  --primary-light: #333333;
  --neutral-dark: #000000;
  --neutral-darker: #000000;
  --neutral-darkest: #000000;
  --neutral-light: #ffffff;
  --neutral-lighter: #ffffff;
  --neutral-lightest: #ffffff;
  --neutral-medium: #666666;
  --neutral-DEFAULT: #ffffff;
}

.high-contrast * {
  border-color: #000000 !important;
}

.high-contrast .bg-white {
  background-color: #ffffff !important;
  color: #000000 !important;
}

.high-contrast .bg-gray-50,
.high-contrast .bg-gray-100 {
  background-color: #ffffff !important;
  color: #000000 !important;
}

.high-contrast .text-gray-500,
.high-contrast .text-gray-600,
.high-contrast .text-gray-700 {
  color: #000000 !important;
}

/* Texto Grande */
.large-text {
  font-size: 1.125rem; /* 18px base instead of 16px */
}

.large-text .text-xs {
  font-size: 0.875rem; /* 14px instead of 12px */
}

.large-text .text-sm {
  font-size: 1rem; /* 16px instead of 14px */
}

.large-text .text-base {
  font-size: 1.25rem; /* 20px instead of 16px */
}

.large-text .text-lg {
  font-size: 1.5rem; /* 24px instead of 18px */
}

.large-text .text-xl {
  font-size: 1.75rem; /* 28px instead of 20px */
}

.large-text .text-2xl {
  font-size: 2.25rem; /* 36px instead of 24px */
}

.large-text .text-3xl {
  font-size: 2.75rem; /* 44px instead of 30px */
}

/* Movimento Reduzido */
.reduced-motion *,
.reduced-motion *::before,
.reduced-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

.reduced-motion .animate-spin,
.reduced-motion .animate-pulse,
.reduced-motion .animate-bounce {
  animation: none !important;
}

/* Foco melhorado para acessibilidade */
.focus-visible:focus {
  outline: 2px solid #2563eb !important;
  outline-offset: 2px !important;
}

/* Melhor contraste para links */
.high-contrast a {
  color: #0000ff !important;
  text-decoration: underline !important;
}

.high-contrast a:visited {
  color: #800080 !important;
}

.high-contrast a:hover,
.high-contrast a:focus {
  color: #ff0000 !important;
  background-color: #ffff00 !important;
}

/* Botões com melhor contraste */
.high-contrast button,
.high-contrast .btn {
  border: 2px solid #000000 !important;
  background-color: #ffffff !important;
  color: #000000 !important;
}

.high-contrast button:hover,
.high-contrast .btn:hover {
  background-color: #000000 !important;
  color: #ffffff !important;
}

/* Formulários com melhor contraste */
.high-contrast input,
.high-contrast textarea,
.high-contrast select {
  border: 2px solid #000000 !important;
  background-color: #ffffff !important;
  color: #000000 !important;
}

.high-contrast input:focus,
.high-contrast textarea:focus,
.high-contrast select:focus {
  outline: 3px solid #ff0000 !important;
  outline-offset: 2px !important;
}

/* Melhor visibilidade para elementos interativos */
.high-contrast [role="button"],
.high-contrast [tabindex="0"] {
  border: 1px solid #000000 !important;
}

/* Indicadores de estado mais visíveis */
.high-contrast .bg-green-100,
.high-contrast .bg-green-500 {
  background-color: #00ff00 !important;
  color: #000000 !important;
}

.high-contrast .bg-red-100,
.high-contrast .bg-red-500 {
  background-color: #ff0000 !important;
  color: #ffffff !important;
}

.high-contrast .bg-yellow-100,
.high-contrast .bg-yellow-500 {
  background-color: #ffff00 !important;
  color: #000000 !important;
}

.high-contrast .bg-blue-100,
.high-contrast .bg-blue-500 {
  background-color: #0000ff !important;
  color: #ffffff !important;
}

/* Melhor visibilidade para sombras */
.high-contrast .shadow,
.high-contrast .shadow-sm,
.high-contrast .shadow-md,
.high-contrast .shadow-lg {
  box-shadow: 0 0 0 2px #000000 !important;
}

/* Remover gradientes em alto contraste */
.high-contrast .bg-gradient-to-r,
.high-contrast .bg-gradient-to-l,
.high-contrast .bg-gradient-to-t,
.high-contrast .bg-gradient-to-b {
  background-image: none !important;
}

/* Melhor visibilidade para bordas */
.high-contrast .border,
.high-contrast .border-t,
.high-contrast .border-r,
.high-contrast .border-b,
.high-contrast .border-l {
  border-color: #000000 !important;
  border-width: 2px !important;
}

/* Estilos para modo escuro com alto contraste */
.dark.high-contrast {
  --primary: #ffffff;
  --primary-dark: #ffffff;
  --primary-light: #cccccc;
  --neutral-dark: #ffffff;
  --neutral-darker: #ffffff;
  --neutral-darkest: #ffffff;
  --neutral-light: #000000;
  --neutral-lighter: #000000;
  --neutral-lightest: #000000;
  --neutral-medium: #999999;
  --neutral-DEFAULT: #000000;
}

.dark.high-contrast .bg-neutral-dark,
.dark.high-contrast .bg-neutral-darker {
  background-color: #000000 !important;
  color: #ffffff !important;
}

.dark.high-contrast .text-neutral-light {
  color: #ffffff !important;
}

.dark.high-contrast .text-neutral-DEFAULT {
  color: #ffffff !important;
}

.dark.high-contrast .border-neutral-medium {
  border-color: #ffffff !important;
}