@echo off
echo ========================================
echo    ProMandato Firebase Hosting Setup
echo ========================================
echo.

echo [1/5] Verificando Firebase CLI...
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Firebase CLI nao encontrado. Instalando...
    npm install -g firebase-tools
) else (
    echo Firebase CLI encontrado!
)

echo.
echo [2/5] Fazendo login no Firebase...
firebase login

echo.
echo [3/5] Configurando projeto Firebase...
firebase use --add

echo.
echo [4/5] Testando configuracao localmente...
echo Iniciando servidor local na porta 5000...
echo Acesse: http://localhost:5000
echo Pressione Ctrl+C para parar o servidor
firebase serve --only hosting

echo.
echo [5/5] Setup concluido!
echo.
echo Proximos passos:
echo 1. Configure os secrets do GitHub (veja FIREBASE_HOSTING_SETUP.md)
echo 2. Faca o primeiro deploy: firebase deploy --only hosting
echo 3. Configure dominio customizado (opcional)
echo.
pause
