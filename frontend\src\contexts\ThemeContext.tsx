import React, { createContext, useEffect, useState } from 'react';

type ThemeMode = 'light' | 'dark';
type ContrastMode = 'normal' | 'high';

interface ThemeContextType {
  theme: ThemeMode;
  contrast: ContrastMode;
  toggleTheme: () => void;
}

export const ThemeContext = createContext<ThemeContextType>({
  theme: 'light',
  contrast: 'normal',
  toggleTheme: () => {},
});

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [theme, setTheme] = useState<ThemeMode>('light');
  const [contrast, setContrast] = useState<ContrastMode>('normal');

  useEffect(() => {
    // Detecta preferência de tema
    const savedTheme = localStorage.getItem('theme') as ThemeMode;
    if (savedTheme) {
      setTheme(savedTheme);
    } else if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
      setTheme('dark');
    }

    // Detecta preferência de contraste
    if (window.matchMedia('(prefers-contrast: more)').matches) {
      setContrast('high');
      document.documentElement.setAttribute('data-contrast', 'high');
    }

    // Observa mudanças na preferência de contraste
    const contrastMediaQuery = window.matchMedia('(prefers-contrast: more)');
    const handleContrastChange = (e: MediaQueryListEvent) => {
      const newContrast = e.matches ? 'high' : 'normal';
      setContrast(newContrast);
      document.documentElement.setAttribute('data-contrast', newContrast);
    };

    contrastMediaQuery.addEventListener('change', handleContrastChange);
    return () => {
      contrastMediaQuery.removeEventListener('change', handleContrastChange);
    };
  }, []);

  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
    localStorage.setItem('theme', newTheme);
  };

  useEffect(() => {
    document.documentElement.classList.remove('light', 'dark');
    document.documentElement.classList.add(theme);
  }, [theme]);

  return (
    <ThemeContext.Provider value={{ theme, contrast, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};