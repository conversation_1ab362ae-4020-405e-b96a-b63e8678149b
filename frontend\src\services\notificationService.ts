import { apiService, ApiResponse } from './apiService';

export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  category: 'demand' | 'event' | 'system' | 'general';
  read: boolean;
  actionUrl?: string;
  actionLabel?: string;
  metadata: Record<string, any>;
  createdAt: string;
  readAt?: string;
  expiresAt?: string;
}

export interface NotificationFilters {
  read?: boolean;
  category?: string;
  type?: string;
  limit?: number;
}

export interface CreateNotificationData {
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  category?: 'demand' | 'event' | 'system' | 'general';
  userId: string;
  actionUrl?: string;
  actionLabel?: string;
  metadata?: Record<string, any>;
  expiresAt?: string;
}

class NotificationService {
  private baseEndpoint = '/notifications';

  /**
   * Buscar notificações do usuário atual
   */
  async getNotifications(filters?: NotificationFilters): Promise<ApiResponse<Notification[]>> {
    try {
      const queryParams = new URLSearchParams();
      
      if (filters?.read !== undefined) {
        queryParams.append('read', filters.read.toString());
      }
      if (filters?.category) {
        queryParams.append('category', filters.category);
      }
      if (filters?.type) {
        queryParams.append('type', filters.type);
      }
      if (filters?.limit) {
        queryParams.append('limit', filters.limit.toString());
      }

      const endpoint = queryParams.toString() 
        ? `${this.baseEndpoint}?${queryParams.toString()}`
        : this.baseEndpoint;

      return await apiService.get<Notification[]>(endpoint);
    } catch (error) {
      console.error('Erro ao buscar notificações:', error);
      return {
        success: false,
        error: 'Erro ao buscar notificações'
      };
    }
  }

  /**
   * Buscar contagem de notificações não lidas
   */
  async getUnreadCount(): Promise<ApiResponse<{ count: number }>> {
    try {
      return await apiService.get<{ count: number }>(`${this.baseEndpoint}/unread-count`);
    } catch (error) {
      console.error('Erro ao buscar contagem de não lidas:', error);
      return {
        success: false,
        error: 'Erro ao buscar contagem de notificações não lidas'
      };
    }
  }

  /**
   * Buscar notificação específica
   */
  async getNotification(id: string): Promise<ApiResponse<Notification>> {
    try {
      return await apiService.get<Notification>(`${this.baseEndpoint}/${id}`);
    } catch (error) {
      console.error('Erro ao buscar notificação:', error);
      return {
        success: false,
        error: 'Erro ao buscar notificação'
      };
    }
  }

  /**
   * Criar nova notificação (apenas admins)
   */
  async createNotification(data: CreateNotificationData): Promise<ApiResponse<Notification>> {
    try {
      return await apiService.post<Notification>(this.baseEndpoint, data);
    } catch (error) {
      console.error('Erro ao criar notificação:', error);
      return {
        success: false,
        error: 'Erro ao criar notificação'
      };
    }
  }

  /**
   * Marcar notificação como lida
   */
  async markAsRead(id: string): Promise<ApiResponse<Notification>> {
    try {
      return await apiService.put<Notification>(`${this.baseEndpoint}/${id}/read`);
    } catch (error) {
      console.error('Erro ao marcar como lida:', error);
      return {
        success: false,
        error: 'Erro ao marcar notificação como lida'
      };
    }
  }

  /**
   * Marcar notificação como não lida
   */
  async markAsUnread(id: string): Promise<ApiResponse<Notification>> {
    try {
      return await apiService.put<Notification>(`${this.baseEndpoint}/${id}/unread`);
    } catch (error) {
      console.error('Erro ao marcar como não lida:', error);
      return {
        success: false,
        error: 'Erro ao marcar notificação como não lida'
      };
    }
  }

  /**
   * Marcar todas as notificações como lidas
   */
  async markAllAsRead(): Promise<ApiResponse<void>> {
    try {
      return await apiService.put<void>(`${this.baseEndpoint}/mark-all-read`);
    } catch (error) {
      console.error('Erro ao marcar todas como lidas:', error);
      return {
        success: false,
        error: 'Erro ao marcar todas as notificações como lidas'
      };
    }
  }

  /**
   * Deletar notificação
   */
  async deleteNotification(id: string): Promise<ApiResponse<void>> {
    try {
      return await apiService.delete<void>(`${this.baseEndpoint}/${id}`);
    } catch (error) {
      console.error('Erro ao deletar notificação:', error);
      return {
        success: false,
        error: 'Erro ao deletar notificação'
      };
    }
  }

  /**
   * Buscar apenas notificações não lidas
   */
  async getUnreadNotifications(limit?: number): Promise<ApiResponse<Notification[]>> {
    return this.getNotifications({ read: false, limit });
  }

  /**
   * Buscar notificações por categoria
   */
  async getNotificationsByCategory(category: string, limit?: number): Promise<ApiResponse<Notification[]>> {
    return this.getNotifications({ category, limit });
  }

  /**
   * Buscar notificações por tipo
   */
  async getNotificationsByType(type: string, limit?: number): Promise<ApiResponse<Notification[]>> {
    return this.getNotifications({ type, limit });
  }

  /**
   * Verificar se há novas notificações (polling)
   */
  async checkForNewNotifications(lastCheckTime: string): Promise<ApiResponse<Notification[]>> {
    try {
      // Buscar notificações criadas após o último check
      const response = await this.getNotifications({ limit: 50 });
      
      if (response.success && response.data) {
        const newNotifications = response.data.filter(
          notification => new Date(notification.createdAt) > new Date(lastCheckTime)
        );
        
        return {
          success: true,
          data: newNotifications
        };
      }
      
      return response;
    } catch (error) {
      console.error('Erro ao verificar novas notificações:', error);
      return {
        success: false,
        error: 'Erro ao verificar novas notificações'
      };
    }
  }

  /**
   * Formatar timestamp relativo
   */
  formatRelativeTime(timestamp: string): string {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Agora';
    if (diffInMinutes < 60) return `${diffInMinutes}m atrás`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h atrás`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d atrás`;
    
    return date.toLocaleDateString('pt-BR');
  }

  /**
   * Obter ícone baseado no tipo da notificação
   */
  getTypeIcon(type: string): string {
    const icons = {
      info: '📢',
      success: '✅',
      warning: '⚠️',
      error: '❌'
    };
    return icons[type as keyof typeof icons] || icons.info;
  }

  /**
   * Obter cor baseada no tipo da notificação
   */
  getTypeColor(type: string): string {
    const colors = {
      info: 'blue',
      success: 'green',
      warning: 'yellow',
      error: 'red'
    };
    return colors[type as keyof typeof colors] || colors.info;
  }
}

// Exportar instância única
export const notificationService = new NotificationService();
export default notificationService;
