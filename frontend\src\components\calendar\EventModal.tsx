import React, { useState, useEffect } from 'react';
import { Modal } from '../ui/Modal';
import { Button } from '../ui/Button';
import { Input, Textarea } from '../ui/Input';
import { AgendaEvent } from '../../types';
import { validateEvent, formatDateTimeLocal } from '../../utils/calendarHelpers';

interface EventModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (event: Partial<AgendaEvent>) => Promise<void>;
  onDelete?: (eventId: string) => Promise<void>;
  event: Partial<AgendaEvent> | null;
  isLoading?: boolean;
}

export const EventModal: React.FC<EventModalProps> = ({
  isOpen,
  onClose,
  onSave,
  onDelete,
  event,
  isLoading = false,
}) => {
  const [formData, setFormData] = useState<Partial<AgendaEvent>>({
    title: '',
    start: '',
    end: '',
    description: '',
    location: '',
    isAllDay: false,
    attendees: [],
  });
  const [error, setError] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (event) {
      setFormData({
        ...event,
        start: event.start ? formatDateTimeLocal(event.start) : '',
        end: event.end ? formatDateTimeLocal(event.end) : '',
        attendees: event.attendees || [],
      });
    } else {
      // Reset form for new event
      const now = new Date();
      const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000);
      
      setFormData({
        title: '',
        start: formatDateTimeLocal(now),
        end: formatDateTimeLocal(oneHourLater),
        description: '',
        location: '',
        isAllDay: false,
        attendees: [],
      });
    }
    setError(null);
  }, [event, isOpen]);

  const handleInputChange = (field: keyof AgendaEvent, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setError(null);
  };

  const handleAttendeesChange = (value: string) => {
    const attendees = value.split(',').map(name => name.trim()).filter(name => name.length > 0);
    handleInputChange('attendees', attendees);
  };

  const handleSave = async () => {
    const validationError = validateEvent(formData);
    if (validationError) {
      setError(validationError);
      return;
    }

    setIsSaving(true);
    setError(null);

    try {
      const eventToSave = {
        ...formData,
        start: new Date(formData.start!).toISOString(),
        end: new Date(formData.end!).toISOString(),
      };

      await onSave(eventToSave);
      onClose();
    } catch (err) {
      console.error('Error saving event:', err);
      setError('Erro ao salvar evento. Tente novamente.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!event?.id || !onDelete) return;

    if (window.confirm('Tem certeza que deseja excluir este evento?')) {
      setIsSaving(true);
      try {
        await onDelete(event.id);
        onClose();
      } catch (err) {
        console.error('Error deleting event:', err);
        setError('Erro ao excluir evento. Tente novamente.');
      } finally {
        setIsSaving(false);
      }
    }
  };

  const handleClose = () => {
    if (!isSaving) {
      onClose();
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={event?.id ? 'Editar Evento' : 'Novo Evento'}
      size="lg"
    >
      <div className="space-y-4">
        {error && (
          <div className="text-red-500 dark:text-red-400 bg-red-100 dark:bg-red-900 dark:bg-opacity-30 p-2 rounded-md text-sm">
            {error}
          </div>
        )}

        <Input
          label="Título do Evento"
          value={formData.title || ''}
          onChange={(e) => handleInputChange('title', e.target.value)}
          required
          disabled={isSaving}
          placeholder="Digite o título do evento"
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Input
            label="Data/Hora de Início"
            type="datetime-local"
            value={formData.start || ''}
            onChange={(e) => handleInputChange('start', e.target.value)}
            required
            disabled={isSaving}
          />
          <Input
            label="Data/Hora de Término"
            type="datetime-local"
            value={formData.end || ''}
            onChange={(e) => handleInputChange('end', e.target.value)}
            required
            disabled={isSaving}
          />
        </div>

        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            id="isAllDay"
            checked={formData.isAllDay || false}
            onChange={(e) => handleInputChange('isAllDay', e.target.checked)}
            disabled={isSaving}
            className="rounded border-gray-300 text-primary focus:ring-primary"
          />
          <label htmlFor="isAllDay" className="text-sm font-medium text-gray-700 dark:text-gray-300">
            Evento de dia inteiro
          </label>
        </div>

        <Input
          label="Local"
          value={formData.location || ''}
          onChange={(e) => handleInputChange('location', e.target.value)}
          disabled={isSaving}
          placeholder="Local do evento (opcional)"
        />

        <Textarea
          label="Descrição"
          value={formData.description || ''}
          onChange={(e) => handleInputChange('description', e.target.value)}
          disabled={isSaving}
          placeholder="Descrição do evento (opcional)"
          rows={3}
        />

        <Input
          label="Participantes"
          value={formData.attendees?.join(', ') || ''}
          onChange={(e) => handleAttendeesChange(e.target.value)}
          disabled={isSaving}
          placeholder="Nomes dos participantes separados por vírgula (opcional)"
        />

        <div className="flex justify-between pt-4">
          <div>
            {event?.id && onDelete && (
              <Button
                variant="outline"
                onClick={handleDelete}
                disabled={isSaving}
                className="text-red-600 border-red-600 hover:bg-red-50 dark:text-red-400 dark:border-red-400"
              >
                Excluir
              </Button>
            )}
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isSaving}
            >
              Cancelar
            </Button>
            <Button
              onClick={handleSave}
              isLoading={isSaving}
              disabled={isSaving}
            >
              {event?.id ? 'Salvar Alterações' : 'Criar Evento'}
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
};
