
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import { Input } from '../ui/Input';
import { Button } from '../ui/Button';
import { ROUTE_PATHS, APP_NAME, ICONS } from '../../constants';

export const LoginForm: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isSignUpMode, setIsSignUpMode] = useState(false); 

  const { signIn, signUp, loading } = useAuth();
  const navigate = useNavigate();

  const clearForm = () => {
    setEmail('');
    setPassword('');
    setFullName('');
    setConfirmPassword('');
    setError(null);
  }

  const toggleMode = () => {
    setIsSignUpMode(!isSignUpMode);
    clearForm();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (isSignUpMode) {
      if (password !== confirmPassword) {
        setError('As senhas não coincidem.');
        return;
      }
      if (password.length < 6) {
        setError('A senha deve ter pelo menos 6 caracteres.');
        return;
      }
      if (!fullName.trim()) {
        setError('Por favor, insira seu nome completo.');
        return;
      }
      try {
        await signUp(email, password, fullName);
        navigate(ROUTE_PATHS.DASHBOARD);
      } catch (err: any) {
        setError(err.message || 'Falha ao cadastrar. Verifique os dados e tente novamente.');
      }
    } else {
      // Login mode
      try {
        await signIn(email, password);
        navigate(ROUTE_PATHS.DASHBOARD);
      } catch (err: any) {
        setError(err.message || 'Falha ao entrar. Verifique suas credenciais.');
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-light via-primary to-primary-dark flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <span className="text-white text-5xl font-bold tracking-tight flex items-center justify-center">
             {React.cloneElement(ICONS.TEAM, { className: "w-12 h-12 text-white"})} 
             <span className="ml-3">{APP_NAME}</span>
          </span>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-white">
            {isSignUpMode ? 'Crie sua conta' : 'Acesse sua conta'}
          </h2>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white dark:bg-neutral-darker py-8 px-4 shadow-xl sm:rounded-lg sm:px-10">
          <form className="space-y-6" onSubmit={handleSubmit}>
            {isSignUpMode && (
              <Input
                label="Nome Completo"
                id="fullName"
                type="text"
                autoComplete="name"
                required
                value={fullName}
                onChange={(e) => setFullName(e.target.value)}
              />
            )}
            <Input
              label="Email"
              id="email"
              type="email"
              autoComplete="email"
              required
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
            <Input
              label="Senha"
              id="password"
              type="password"
              autoComplete={isSignUpMode ? "new-password" : "current-password"}
              required
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder={isSignUpMode ? "Mínimo 6 caracteres" : ""}
            />
            {isSignUpMode && (
              <Input
                label="Confirmar Senha"
                id="confirmPassword"
                type="password"
                autoComplete="new-password"
                required
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
              />
            )}
            {error && <p className="text-sm text-red-600 dark:text-red-400 text-center p-2 bg-red-50 dark:bg-red-900 dark:bg-opacity-30 rounded-md">{error}</p>}
            <div>
              <Button type="submit" variant="primary" className="w-full" isLoading={loading}>
                {isSignUpMode ? 'Cadastrar' : 'Entrar'}
              </Button>
            </div>
          </form>
          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t border-gray-300 dark:border-neutral-medium" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 bg-white dark:bg-neutral-darker text-gray-500 dark:text-neutral-DEFAULT">Ou</span>
              </div>
            </div>
            <div className="mt-6 text-center">
              <button
                type="button"
                onClick={toggleMode}
                className="font-medium text-primary hover:text-primary-dark dark:text-primary-light dark:hover:text-primary"
              >
                {isSignUpMode ? 'Já tem uma conta? Entre aqui.' : 'Não tem uma conta? Cadastre-se.'}
              </button>
            </div>
            {!isSignUpMode && (
                 <div className="mt-2 text-center">
                    <a href="#" className="font-medium text-primary hover:text-primary-dark dark:text-primary-light dark:hover:text-primary text-sm">
                        Esqueceu sua senha?
                    </a>
                </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};