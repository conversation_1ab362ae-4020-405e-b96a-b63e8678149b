import React from 'react';
import { InputProps, TextareaProps as CustomTextareaProps } from '../../types';

export const Input: React.FC<InputProps> = ({
  label,
  id,
  type = 'text',
  error,
  className = '',
  ...props
}) => {
  const inputId = id || (label ? label.toLowerCase().replace(/\s+/g, '-') : undefined);
  return (
    <div className="w-full">
      {label && (
        <label htmlFor={inputId} className="block text-sm font-medium text-neutral-dark dark:text-neutral-light mb-1 data-[contrast=high]:text-black dark:data-[contrast=high]:text-white">
          {label}
        </label>
      )}
      <input
        id={inputId}
        type={type}
        className={`
          block w-full px-3 py-2 border rounded-md shadow-sm 
          bg-white dark:bg-neutral-dark dark:text-neutral-light
          focus:outline-none focus:ring-primary focus:border-primary 
          dark:focus:ring-primary-light dark:focus:border-primary-light
          sm:text-sm
          placeholder-gray-400 dark:placeholder-neutral-medium
          data-[contrast=high]:border-2 data-[contrast=high]:border-black dark:data-[contrast=high]:border-white
          data-[contrast=high]:bg-white data-[contrast=high]:text-black
          dark:data-[contrast=high]:bg-black dark:data-[contrast=high]:text-white
          ${error ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-neutral-medium'}
          ${className}
        `}
        {...props}
      />
      {error && <p className="mt-1 text-xs text-red-600 dark:text-red-400 data-[contrast=high]:text-black dark:data-[contrast=high]:text-white">{error}</p>}
    </div>
  );
};

export const Textarea: React.FC<CustomTextareaProps> = ({
  label,
  id,
  error,
  className = '',
  ...props
}) => {
  const textareaId = id || (label ? label.toLowerCase().replace(/\s+/g, '-') : undefined);
  return (
    <div className="w-full">
      {label && (
        <label htmlFor={textareaId} className="block text-sm font-medium text-neutral-dark dark:text-neutral-light mb-1 data-[contrast=high]:text-black dark:data-[contrast=high]:text-white">
          {label}
        </label>
      )}
      <textarea
        id={textareaId}
        className={`
          block w-full px-3 py-2 border rounded-md shadow-sm 
          bg-white dark:bg-neutral-dark dark:text-neutral-light
          focus:outline-none focus:ring-primary focus:border-primary 
          dark:focus:ring-primary-light dark:focus:border-primary-light
          sm:text-sm
          placeholder-gray-400 dark:placeholder-neutral-medium
          data-[contrast=high]:border-2 data-[contrast=high]:border-black dark:data-[contrast=high]:border-white
          data-[contrast=high]:bg-white data-[contrast=high]:text-black
          dark:data-[contrast=high]:bg-black dark:data-[contrast=high]:text-white
          ${error ? 'border-red-500 dark:border-red-400' : 'border-gray-300 dark:border-neutral-medium'}
          ${className}
        `}
        {...props}
      />
      {error && <p className="mt-1 text-xs text-red-600 dark:text-red-400 data-[contrast=high]:text-black dark:data-[contrast=high]:text-white">{error}</p>}
    </div>
  );
};