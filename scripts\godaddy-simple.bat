@echo off
setlocal enabledelayedexpansion

echo ========================================
echo     ASSISTENTE GODADDY SIMPLIFICADO
echo ========================================
echo.

:MENU
echo ESCOLHA UMA OPCAO:
echo.
echo [1] Deploy e preparar Firebase
echo [2] Verificar DNS
echo [3] Mostrar valores para GoDaddy
echo [4] Testar dominio
echo [5] Abrir links uteis
echo [0] Sair
echo.
set /p CHOICE="Digite sua escolha (0-5): "

if "%CHOICE%"=="1" goto DEPLOY
if "%CHOICE%"=="2" goto CHECK
if "%CHOICE%"=="3" goto VALUES
if "%CHOICE%"=="4" goto TEST
if "%CHOICE%"=="5" goto LINKS
if "%CHOICE%"=="0" goto EXIT
goto MENU

:DEPLOY
echo.
echo Fazendo deploy...
call scripts\deploy-custom-domain.bat
echo.
echo Abrindo Firebase Console...
start https://console.firebase.google.com/project/promandato-9a4cf/hosting
echo.
echo INSTRUCOES:
echo 1. No Firebase Console, clique em "Adicionar dominio personalizado"
echo 2. Digite: www.promandato.com.br
echo 3. Anote os valores TXT e A fornecidos
echo 4. Volte aqui e escolha opcao [3]
echo.
pause
goto MENU

:CHECK
echo.
echo Verificando DNS...
call scripts\check-godaddy-dns.bat
goto MENU

:VALUES
echo.
echo ========================================
echo VALORES PARA CONFIGURAR NO GODADDY
echo ========================================
echo.
echo REGISTRO TXT (Verificacao):
echo Type: TXT
echo Name: www
echo Value: firebase-hosting-verification=[VALOR_DO_FIREBASE]
echo TTL: 1 Hour
echo.
echo REGISTROS A (Apos verificacao):
echo.
echo Primeiro registro A:
echo Type: A
echo Name: www
echo Value: *************
echo TTL: 1 Hour
echo.
echo Segundo registro A:
echo Type: A
echo Name: www
echo Value: **************
echo TTL: 1 Hour
echo.
echo IMPORTANTE: Use os valores EXATOS do Firebase!
echo Os IPs acima sao exemplos comuns.
echo.
echo PASSOS NO GODADDY:
echo 1. Acesse: https://dcc.godaddy.com/
echo 2. Va em DNS do dominio promandato.com.br
echo 3. Remova registros existentes para "www"
echo 4. Adicione os registros acima
echo.
set /p OPEN="Abrir GoDaddy agora? (s/n): "
if /i "%OPEN%"=="s" start https://dcc.godaddy.com/
pause
goto MENU

:TEST
echo.
echo Testando dominio...
call scripts\test-custom-domain.bat
goto MENU

:LINKS
echo.
echo Abrindo links uteis...
start https://dcc.godaddy.com/
start https://console.firebase.google.com/project/promandato-9a4cf/hosting
echo.
echo Links abertos no navegador:
echo - GoDaddy DNS Management
echo - Firebase Console
echo.
pause
goto MENU

:EXIT
echo.
echo Obrigado por usar o assistente!
echo.
echo Para mais ajuda, consulte:
echo - GODADDY_SETUP_GUIDE.md
echo - GODADDY_VISUAL_GUIDE.md
echo.
pause
exit /b 0
