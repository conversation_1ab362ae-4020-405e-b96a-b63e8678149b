interface EnvironmentConfig {
  API_BASE_URL: string;
  STRIPE_PUBLIC_KEY: string;
  FIREBASE_CONFIG: {
    apiKey: string;
    authDomain: string;
    projectId: string;
    storageBucket: string;
    messagingSenderId: string;
    appId: string;
    measurementId: string;
  };
  APP_CONFIG: {
    name: string;
    version: string;
    environment: string;
  };
  URLS: {
    success: string;
    cancel: string;
  };
  API_CONFIG: {
    timeout: number;
    retryAttempts: number;
  };
  DEBUG: boolean;
}

const getEnvironment = (): 'development' | 'staging' | 'production' => {
  const hostname = window.location.hostname;
  
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    return 'development';
  }
  
  if (hostname.includes('staging')) {
    return 'staging';
  }
  
  return 'production';
};

// Função para obter variável de ambiente com fallback
const getEnvVar = (key: string, fallback: string = ''): string => {
  return import.meta.env[key] || fallback;
};

const getEnvNumber = (key: string, fallback: number): number => {
  const value = import.meta.env[key];
  return value ? parseInt(value, 10) : fallback;
};

const getEnvBoolean = (key: string, fallback: boolean): boolean => {
  const value = import.meta.env[key];
  return value ? value === 'true' : fallback;
};

// Configuração baseada em variáveis de ambiente
const createConfig = (): EnvironmentConfig => {
  const environment = getEnvironment();
  
  return {
    API_BASE_URL: getEnvVar('VITE_API_URL', 
      environment === 'development' 
        ? 'http://localhost:3002/api'
        : 'https://promandato-backend-517140455601.southamerica-east1.run.app/api'
    ),
    
    STRIPE_PUBLIC_KEY: getEnvVar('VITE_STRIPE_PUBLIC_KEY', 
      environment === 'development'
        ? 'pk_test_51QUu2mClUIoqY19kQholKzLBzhCKuYnrCqGAQPtJL3vfvp3BcuyhGopNqirFP9DzOcp1GMMPnoHCibwMIOGBroQq00Frc8LxxN'
        : 'pk_live_51QUu2mClUIoqY19kQholKzLBzhCKuYnrCqGAQPtJL3vfvp3BcuyhGopNqirFP9DzOcp1GMMPnoHCibwMIOGBroQq00Frc8LxxN'
    ),
    
    FIREBASE_CONFIG: {
      apiKey: getEnvVar('VITE_FIREBASE_API_KEY', 'AIzaSyB04qDOcU5jdZAYy-yaYQRUrrYQ_l49vcY'),
      authDomain: getEnvVar('VITE_FIREBASE_AUTH_DOMAIN', 'promandato-9a4cf.firebaseapp.com'),
      projectId: getEnvVar('VITE_FIREBASE_PROJECT_ID', 'promandato-9a4cf'),
      storageBucket: getEnvVar('VITE_FIREBASE_STORAGE_BUCKET', 'promandato-9a4cf.firebasestorage.app'),
      messagingSenderId: getEnvVar('VITE_FIREBASE_MESSAGING_SENDER_ID', '517140455601'),
      appId: getEnvVar('VITE_FIREBASE_APP_ID', '1:517140455601:web:fa2eb0ec2f88b506594290'),
      measurementId: getEnvVar('VITE_FIREBASE_MEASUREMENT_ID', 'G-MXYM2GKJS5')
    },
    
    APP_CONFIG: {
      name: getEnvVar('VITE_APP_NAME', 'ProMandato'),
      version: getEnvVar('VITE_APP_VERSION', '1.0.0'),
      environment: getEnvVar('VITE_APP_ENVIRONMENT', environment)
    },
    
    URLS: {
      success: getEnvVar('VITE_SUCCESS_URL',
        environment === 'development'
          ? 'http://localhost:5174/success'
          : 'https://www.promandato.com.br/app/success'
      ),
      cancel: getEnvVar('VITE_CANCEL_URL',
        environment === 'development'
          ? 'http://localhost:5174/pricing'
          : 'https://www.promandato.com.br/landingpage/pricing'
      )
    },
    
    API_CONFIG: {
      timeout: getEnvNumber('VITE_API_TIMEOUT', 30000),
      retryAttempts: getEnvNumber('VITE_API_RETRY_ATTEMPTS', 3)
    },
    
    DEBUG: getEnvBoolean('VITE_DEBUG', environment === 'development')
  };
};

export const ENV_CONFIG = createConfig();

// Exportações para compatibilidade com código existente
export const API_CONFIG = {
  BASE_URL: ENV_CONFIG.API_BASE_URL,
  TIMEOUT: ENV_CONFIG.API_CONFIG.timeout,
  RETRY_ATTEMPTS: ENV_CONFIG.API_CONFIG.retryAttempts
};

export const FIREBASE_CONFIG = ENV_CONFIG.FIREBASE_CONFIG;

// Log da configuração em desenvolvimento
if (ENV_CONFIG.DEBUG) {
  console.log('🔧 Environment Configuration:', {
    environment: ENV_CONFIG.APP_CONFIG.environment,
    apiUrl: ENV_CONFIG.API_BASE_URL,
    stripeKey: ENV_CONFIG.STRIPE_PUBLIC_KEY.substring(0, 20) + '...',
    firebaseProject: ENV_CONFIG.FIREBASE_CONFIG.projectId,
    debug: ENV_CONFIG.DEBUG
  });
}

export default ENV_CONFIG;