import React, { useState, useEffect, useCallback } from 'react';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Input, Textarea } from '../components/ui/Input';
import { ICONS } from '../constants';
import { PoliticianProfile } from '../types';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';
import { useAuth } from '../hooks/useAuth';
import { 
  getPoliticianProfile, 
  savePoliticianProfile, 
  uploadFileToStorage,
  deleteFileFromStorage
} from '../services/firebaseService';
import { FIREBASE_CONFIG } from '../constants';

const DEFAULT_PROFILE_ID = 'main_profile';
const getPhotoStoragePath = (userId: string, fileExtension: string) => {
  const timestamp = new Date().getTime();
  const safeName = `profile_${timestamp}.${fileExtension}`;
  const fullPath = `politician_photos/${userId}/${safeName}`;
  console.log('Gerando caminho da foto:', {
    userId,
    fileExtension,
    safeName,
    fullPath: fullPath,
    storageBucket: FIREBASE_CONFIG.storageBucket
  });
  return fullPath;
};

const initialProfile: PoliticianProfile = {
  id: DEFAULT_PROFILE_ID,
  name: '',
  photoUrl: '',
  politicalParty: '',
  birthDate: '',
  bio: '',
  contact: { phone: '', email: '', officePhone: '' },
  address: { street: '', number: '', complement: '', neighborhood: '', city: '', state: '', zipCode: '' },
  socialMedia: { facebook: '', instagram: '', twitter: '', linkedin: '', tiktok: '', youtube: '', website: '' },
  updatedAt: new Date().toISOString(),
};

const PoliticianProfilePage: React.FC = () => {
  const { currentUser, loading: authLoading } = useAuth();
  const [profile, setProfile] = useState<PoliticianProfile>(initialProfile);
  const [selectedPhotoFile, setSelectedPhotoFile] = useState<File | null>(null);
  const [photoPreviewUrl, setPhotoPreviewUrl] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchProfile = useCallback(async () => {
    if (!currentUser) return;
    
    setIsLoading(true);
    setError(null);
    try {
      console.log('Buscando perfil para o usuário:', currentUser.id);
      const fetchedProfile = await getPoliticianProfile();
      console.log('Perfil carregado:', fetchedProfile);
      
      if (fetchedProfile) {
        const fullProfile = {
          ...initialProfile,
          ...fetchedProfile,
          contact: { ...initialProfile.contact, ...fetchedProfile.contact },
          address: { ...initialProfile.address, ...fetchedProfile.address },
          socialMedia: { ...initialProfile.socialMedia, ...fetchedProfile.socialMedia },
        };
        setProfile(fullProfile);
        if (fullProfile.photoUrl) {
          setPhotoPreviewUrl(fullProfile.photoUrl);
        }
      }
    } catch (err) {
      console.error('Erro ao carregar perfil:', err);
      setError(err instanceof Error ? err.message : 'Erro ao carregar o perfil do político');
    } finally {
      setIsLoading(false);
    }
  }, [currentUser]);

  useEffect(() => {
    if (!authLoading && currentUser) {
      fetchProfile();
    }
  }, [authLoading, currentUser, fetchProfile]);

  // Se ainda está carregando a autenticação, mostra um spinner
  if (authLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  // Se não há usuário autenticado, mostra mensagem de erro
  if (!currentUser) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] text-center">
        <p className="text-lg text-red-600 dark:text-red-400 mb-4">
          Você precisa estar autenticado para acessar esta página.
        </p>
        <Button variant="primary" onClick={() => window.location.href = '/login'}>
          Ir para o Login
        </Button>
      </div>
    );
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, section?: keyof PoliticianProfile) => {
    const { name, value } = e.target;
    if (section && (section === 'contact' || section === 'address' || section === 'socialMedia')) {
      setProfile(prev => ({
        ...prev,
        [section]: {
          ...(prev[section] as object), 
          [name]: value
        }
      }));
    } else {
      setProfile(prev => ({ ...prev, [name]: value }));
    }
  };
  
  const handlePhotoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      console.log("Arquivo selecionado:", {
        name: file.name,
        size: file.size,
        type: file.type
      });
      
      if (file.size > 2 * 1024 * 1024) { 
          alert("A foto é muito grande. Máximo 2MB.");
          return;
      }
      if (!['image/png', 'image/jpeg', 'image/webp'].includes(file.type)) {
          alert("Formato de arquivo inválido. Use PNG, JPG ou WEBP.");
          return;
      }
      setSelectedPhotoFile(file);
      if (photoPreviewUrl && photoPreviewUrl.startsWith('blob:')) {
        URL.revokeObjectURL(photoPreviewUrl);
      }
      setPhotoPreviewUrl(URL.createObjectURL(file));
    }
  };

  const handleRemovePhoto = () => {
    setSelectedPhotoFile(null);
    if (photoPreviewUrl && photoPreviewUrl.startsWith('blob:')) {
      URL.revokeObjectURL(photoPreviewUrl);
    }
    setPhotoPreviewUrl(null);
    const fileInput = document.getElementById('photoUrlInput') as HTMLInputElement;
    if (fileInput) fileInput.value = "";
  };

  const handleUploadPhoto = async () => {
    if (!selectedPhotoFile || !currentUser) {
      setError("Selecione uma foto e certifique-se de estar logado.");
      return;
    }

    setIsUploading(true);
    setError(null);

    try {
      // 1. Validação inicial
      const fileExtension = selectedPhotoFile.name.split('.').pop()?.toLowerCase() || 'png';
      const allowedTypes = ['png', 'jpg', 'jpeg', 'webp'];
      
      if (!allowedTypes.includes(fileExtension)) {
        throw new Error(`Formato de arquivo inválido. Use: ${allowedTypes.join(', ')}`);
      }

      if (selectedPhotoFile.size > 2 * 1024 * 1024) {
        throw new Error('A foto é muito grande. Máximo 2MB.');
      }

      // 2. Tenta excluir a foto antiga
      if (profile.photoUrl?.includes('firebasestorage.googleapis.com')) {
        try {
          const pathSegments = new URL(profile.photoUrl).pathname.split('/');
          const fullPath = decodeURIComponent(
            pathSegments.slice(pathSegments.indexOf('o') + 1).join('/').split('?')[0]
          );
          console.log('Excluindo foto antiga:', { fullPath });
          await deleteFileFromStorage(fullPath);
          console.log('Foto antiga excluída com sucesso');
        } catch (deleteError) {
          console.warn("Erro ao excluir foto antiga (continuando):", deleteError);
        }
      }

      // 3. Upload da nova foto
      const newPhotoPath = getPhotoStoragePath(currentUser.id, fileExtension);
      console.log('Fazendo upload da nova foto:', {
        path: newPhotoPath,
        userId: currentUser.id
      });

      const photoUrl = await uploadFileToStorage(selectedPhotoFile, newPhotoPath);
      console.log('Upload concluído:', {
        url: photoUrl
      });

      // 4. Atualiza o perfil com a nova foto
      const updatedProfile = {
        ...profile,
        photoUrl,
        updatedAt: new Date().toISOString()
      };

      console.log('Salvando perfil com nova foto');
      await savePoliticianProfile(updatedProfile);

      // 5. Atualiza estado local
      setProfile(updatedProfile);
      setPhotoPreviewUrl(photoUrl);
      setSelectedPhotoFile(null);

      // 6. Limpa input de arquivo
      const input = document.getElementById('photoUrlInput') as HTMLInputElement;
      if (input) input.value = '';

      alert("Foto atualizada com sucesso!");
    } catch (err) {
      console.error('Erro no processo de upload:', err);
      let errorMessage = 'Erro ao fazer upload da foto.';
      
      if (err instanceof Error) {
        if (err.message.includes('CORS')) {
          errorMessage = 'Erro de permissão ao enviar foto. Por favor, tente novamente.';
        } else if (err.message.includes('Storage')) {
          errorMessage = 'Erro no armazenamento. Verifique sua conexão e tente novamente.';
        } else {
          errorMessage = err.message;
        }
      }
      
      setError(errorMessage);
      alert(errorMessage);
    } finally {
      setIsUploading(false);
    }
  };

  const handleSaveChanges = async () => {
    if (!currentUser) {
      setError("Você precisa estar logado para salvar alterações.");
      return;
    }

    setIsSaving(true);
    setError(null);

    try {
      console.log('Iniciando salvamento do perfil');

      // 1. Prepara os dados para salvar
      const profileToSave = {
        ...profile,
        updatedAt: new Date().toISOString()
      };

      // 2. Limpa campos vazios do contato
      if (profileToSave.contact) {
        const cleanContact = Object.entries(profileToSave.contact).reduce((acc, [key, value]) => {
          if (value && value.trim()) {
            acc[key as keyof typeof profileToSave.contact] = value.trim();
          }
          return acc;
        }, {} as typeof profileToSave.contact);
        
        profileToSave.contact = Object.keys(cleanContact).length ? cleanContact : undefined;
      }

      // 3. Limpa campos vazios do endereço
      if (profileToSave.address) {
        const cleanAddress = Object.entries(profileToSave.address).reduce((acc, [key, value]) => {
          if (value && value.trim()) {
            acc[key as keyof typeof profileToSave.address] = value.trim();
          }
          return acc;
        }, {} as typeof profileToSave.address);
        
        profileToSave.address = Object.keys(cleanAddress).length ? cleanAddress : undefined;
      }

      // 4. Limpa campos vazios das redes sociais
      if (profileToSave.socialMedia) {
        const cleanSocialMedia = Object.entries(profileToSave.socialMedia).reduce((acc, [key, value]) => {
          if (value && value.trim()) {
            acc[key as keyof typeof profileToSave.socialMedia] = value.trim();
          }
          return acc;
        }, {} as typeof profileToSave.socialMedia);
        
        profileToSave.socialMedia = Object.keys(cleanSocialMedia).length ? cleanSocialMedia : undefined;
      }

      // 5. Salva o perfil
      console.log('Salvando perfil:', profileToSave);
      await savePoliticianProfile(profileToSave);

      // 6. Atualiza estado local
      setProfile(prev => ({
        ...prev,
        ...profileToSave,
        updatedAt: new Date().toISOString()
      }));

      console.log('Perfil salvo com sucesso');
      alert("Perfil salvo com sucesso!");
    } catch (err) {
      console.error("Erro ao salvar perfil:", err);
      let errorMessage = "Falha ao salvar o perfil. Verifique os dados e tente novamente.";
      
      if (err instanceof Error) {
        errorMessage = err.message;
      }
      
      setError(errorMessage);
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return <div className="flex items-center justify-center min-h-[calc(100vh-200px)]"><LoadingSpinner size="lg" /></div>;
  }

  return (
    <div className="space-y-6 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold text-neutral-dark dark:text-neutral-light">Perfil do Político</h1>
      <p className="text-sm text-gray-600 dark:text-neutral-DEFAULT">
        Gerencie as informações principais do político. Estes dados podem ser úteis para materiais de comunicação e referência interna.
      </p>

      {error && <div className="p-3 bg-red-100 dark:bg-red-900 dark:bg-opacity-30 text-red-700 dark:text-red-300 rounded-md">{error}</div>}

      <Card>
        <form onSubmit={(e) => { e.preventDefault(); handleSaveChanges(); }} className="space-y-8 p-2">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 items-start">
            <div className="md:col-span-1 flex flex-col items-center">
              <label htmlFor="photoUrlInput" className="block text-sm font-medium text-neutral-dark dark:text-neutral-light mb-2 self-start">
                Foto do Político (Opcional)
              </label>
              {photoPreviewUrl ? (
                <div className="relative group w-48 h-48 rounded-full overflow-hidden shadow-md mb-2">
                  <img src={photoPreviewUrl} alt="Prévia da Foto" className="w-full h-full object-cover" />
                   <Button 
                    type="button" 
                    onClick={handleRemovePhoto} 
                    variant="secondary" 
                    size="sm"
                    className="absolute top-1 right-1 !p-1 h-auto leading-none rounded-full shadow-md opacity-0 group-hover:opacity-100 transition-opacity"
                    aria-label="Remover foto"
                    disabled={isSaving}
                  >
                    {React.cloneElement(ICONS.TRASH, { className: "w-4 h-4" })}
                  </Button>
                </div>
              ) : (
                <div className="w-48 h-48 rounded-full bg-gray-200 dark:bg-neutral-dark flex items-center justify-center text-gray-400 dark:text-neutral-medium mb-2 text-center p-2">
                  {React.cloneElement(ICONS.USER_CIRCLE, { className: "w-16 h-16"})}
                  <span className="mt-1 text-xs">Sem foto</span>
                </div>
              )}
              <div className="flex flex-col gap-2 w-full">
                <Input
                  id="photoUrlInput"
                  name="photoFile"
                  type="file"
                  accept="image/png, image/jpeg, image/webp"
                  onChange={handlePhotoChange}
                  className="block w-full text-sm text-gray-500 dark:text-neutral-DEFAULT 
                           file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 
                           file:text-sm file:font-semibold 
                           file:bg-primary-light file:text-primary 
                           dark:file:bg-primary-dark dark:file:text-neutral-extralight
                           hover:file:bg-primary dark:hover:file:bg-primary-light
                           dark:bg-neutral-dark dark:border-neutral-medium"
                  disabled={isUploading}
                />
                <Button
                  type="button"
                  onClick={handleUploadPhoto}
                  isLoading={isUploading}
                  disabled={!selectedPhotoFile || isUploading}
                  size="sm"
                  className="w-full"
                >
                  {isUploading ? "Enviando..." : "Enviar Foto"}
                </Button>
              </div>
              <p className="text-xs text-gray-500 dark:text-neutral-DEFAULT mt-1">PNG, JPG, WEBP. Máx 2MB.</p>
            </div>

            <div className="md:col-span-2 space-y-4">
              <Input
                label="Nome Completo do Político"
                name="name"
                value={profile.name}
                onChange={handleInputChange}
                placeholder="Ex: João da Silva Sauro"
                required
                className="text-lg"
                disabled={isSaving}
              />
              <Input
                label="Partido Político"
                name="politicalParty"
                value={profile.politicalParty || ''}
                onChange={handleInputChange}
                placeholder="Ex: Partido Exemplo (PEX)"
                disabled={isSaving}
              />
              <Input
                label="Data de Nascimento"
                name="birthDate"
                type="date"
                value={profile.birthDate || ''}
                onChange={handleInputChange}
                disabled={isSaving}
              />
            </div>
          </div>
           <Textarea
            label="Biografia Curta / Mini Currículo"
            name="bio"
            value={profile.bio || ''}
            onChange={handleInputChange}
            rows={4}
            placeholder="Descreva brevemente a trajetória e principais bandeiras do político."
            disabled={isSaving}
          />

          <fieldset className="mt-6 border-t border-gray-200 dark:border-neutral-dark pt-6" disabled={isSaving}>
            <legend className="text-lg font-medium text-neutral-dark dark:text-neutral-light mb-3">Informações de Contato</legend>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input label="Telefone Pessoal/Direto" name="phone" value={profile.contact?.phone || ''} onChange={(e) => handleInputChange(e, 'contact')} placeholder="(XX) XXXXX-XXXX" />
              <Input label="Email Principal" name="email" type="email" value={profile.contact?.email || ''} onChange={(e) => handleInputChange(e, 'contact')} placeholder="<EMAIL>" />
              <Input label="Telefone do Gabinete" name="officePhone" value={profile.contact?.officePhone || ''} onChange={(e) => handleInputChange(e, 'contact')} placeholder="(XX) XXXX-XXXX" className="md:col-span-2"/>
            </div>
          </fieldset>
          
          <fieldset className="mt-6 border-t border-gray-200 dark:border-neutral-dark pt-6" disabled={isSaving}>
            <legend className="text-lg font-medium text-neutral-dark dark:text-neutral-light mb-3">Endereço Principal</legend>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input label="Rua/Avenida" name="street" value={profile.address?.street || ''} onChange={(e) => handleInputChange(e, 'address')} />
              <Input label="Número" name="number" value={profile.address?.number || ''} onChange={(e) => handleInputChange(e, 'address')} />
              <Input label="Complemento" name="complement" value={profile.address?.complement || ''} onChange={(e) => handleInputChange(e, 'address')} />
              <Input label="Bairro" name="neighborhood" value={profile.address?.neighborhood || ''} onChange={(e) => handleInputChange(e, 'address')} />
              <Input label="Cidade" name="city" value={profile.address?.city || ''} onChange={(e) => handleInputChange(e, 'address')} />
              <Input label="Estado (UF)" name="state" value={profile.address?.state || ''} onChange={(e) => handleInputChange(e, 'address')} maxLength={2} />
              <Input label="CEP" name="zipCode" value={profile.address?.zipCode || ''} onChange={(e) => handleInputChange(e, 'address')} placeholder="00000-000" />
            </div>
          </fieldset>

          <fieldset className="mt-6 border-t border-gray-200 dark:border-neutral-dark pt-6" disabled={isSaving}>
            <legend className="text-lg font-medium text-neutral-dark dark:text-neutral-light mb-3">Redes Sociais e Website</legend>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input label="Facebook (URL)" name="facebook" value={profile.socialMedia?.facebook || ''} onChange={(e) => handleInputChange(e, 'socialMedia')} placeholder="https://facebook.com/politico" />
              <Input label="Instagram (@usuário)" name="instagram" value={profile.socialMedia?.instagram || ''} onChange={(e) => handleInputChange(e, 'socialMedia')} placeholder="@politico" />
              <Input label="Twitter/X (@usuário)" name="twitter" value={profile.socialMedia?.twitter || ''} onChange={(e) => handleInputChange(e, 'socialMedia')} placeholder="@politico" />
              <Input label="LinkedIn (URL)" name="linkedin" value={profile.socialMedia?.linkedin || ''} onChange={(e) => handleInputChange(e, 'socialMedia')} placeholder="https://linkedin.com/in/politico" />
              <Input label="TikTok (@usuário)" name="tiktok" value={profile.socialMedia?.tiktok || ''} onChange={(e) => handleInputChange(e, 'socialMedia')} placeholder="@politico" />
              <Input label="YouTube (URL do Canal)" name="youtube" value={profile.socialMedia?.youtube || ''} onChange={(e) => handleInputChange(e, 'socialMedia')} placeholder="https://youtube.com/c/politico" />
              <Input label="Website Oficial (URL)" name="website" type="url" value={profile.socialMedia?.website || ''} onChange={(e) => handleInputChange(e, 'socialMedia')} placeholder="https://www.politico.com.br" className="md:col-span-2"/>
            </div>
          </fieldset>
          
          <div className="pt-8 flex justify-end">
            <Button type="submit" isLoading={isSaving} size="lg" disabled={isSaving || isLoading}>
              {isSaving ? "Salvando..." : "Salvar Perfil do Político"}
            </Button>
          </div>
          {profile.updatedAt && !isLoading && (
             <p className="text-xs text-gray-500 dark:text-neutral-DEFAULT text-right mt-1">
               Última atualização: {new Date(profile.updatedAt).toLocaleString('pt-BR')}
             </p>
           )}
        </form>
      </Card>
    </div>
  );
};

export default PoliticianProfilePage;