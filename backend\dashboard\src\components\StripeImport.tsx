import React, { useState, useRef } from 'react';
import { Upload, FileText, CheckCircle, XCircle, AlertCircle, Download } from 'lucide-react';

interface ImportStats {
  total: number;
  created: number;
  updated: number;
  errors: number;
}

interface ImportError {
  row: any;
  error: string;
}

interface ImportResult {
  success: boolean;
  stats: ImportStats;
  errors: ImportError[];
  message?: string;
}

const StripeImport: React.FC = () => {
  const [file, setFile] = useState<File | null>(null);
  const [importing, setImporting] = useState(false);
  const [result, setResult] = useState<ImportResult | null>(null);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (selectedFile: File) => {
    if (selectedFile.type !== 'text/csv' && !selectedFile.name.endsWith('.csv')) {
      alert('Por favor, selecione apenas arquivos CSV');
      return;
    }

    if (selectedFile.size > 10 * 1024 * 1024) { // 10MB
      alert('Arquivo muito grande. Máximo 10MB');
      return;
    }

    setFile(selectedFile);
    setResult(null);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    const droppedFile = e.dataTransfer.files[0];
    if (droppedFile) {
      handleFileSelect(droppedFile);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      handleFileSelect(selectedFile);
    }
  };

  const handleImport = async () => {
    if (!file) return;

    setImporting(true);
    setResult(null);

    try {
      const formData = new FormData();
      formData.append('csvFile', file);

      const response = await fetch('/api/stripe/import-csv', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
        },
        body: formData
      });

      const data = await response.json();
      setResult(data);

    } catch (error) {
      setResult({
        success: false,
        stats: { total: 0, created: 0, updated: 0, errors: 0 },
        errors: [],
        message: 'Erro ao conectar com o servidor'
      });
    } finally {
      setImporting(false);
    }
  };

  const resetImport = () => {
    setFile(null);
    setResult(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg">
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <Upload className="h-6 w-6 mr-2 text-blue-600" />
            Importar Dados do Stripe
          </h2>
          <p className="text-gray-600 mt-2">
            Importe clientes e assinaturas do Stripe usando um arquivo CSV
          </p>
        </div>

        <div className="p-6">
          {/* Instruções */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h3 className="font-semibold text-blue-900 mb-2">📋 Instruções:</h3>
            <ul className="text-blue-800 text-sm space-y-1">
              <li>• Exporte os dados do Stripe Dashboard em formato CSV</li>
              <li>• O arquivo deve conter: Customer Email, Customer Name, Customer ID, Subscription ID, Product Name, Status</li>
              <li>• Usuários serão criados automaticamente com senhas temporárias</li>
              <li>• Emails de boas-vindas serão enviados automaticamente</li>
            </ul>
          </div>

          {/* Upload Area */}
          {!file && (
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                dragOver
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
            >
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-lg font-medium text-gray-900 mb-2">
                Arraste o arquivo CSV aqui
              </p>
              <p className="text-gray-600 mb-4">ou</p>
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Selecionar Arquivo
              </button>
              <input
                ref={fileInputRef}
                type="file"
                accept=".csv"
                onChange={handleFileInputChange}
                className="hidden"
              />
              <p className="text-xs text-gray-500 mt-2">
                Máximo 10MB • Apenas arquivos CSV
              </p>
            </div>
          )}

          {/* File Selected */}
          {file && !result && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <FileText className="h-8 w-8 text-green-600 mr-3" />
                  <div>
                    <p className="font-medium text-gray-900">{file.name}</p>
                    <p className="text-sm text-gray-600">
                      {(file.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                </div>
                <button
                  type="button"
                  onClick={resetImport}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <XCircle className="h-6 w-6" />
                </button>
              </div>

              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={handleImport}
                  disabled={importing}
                  className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {importing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Importando...
                    </>
                  ) : (
                    <>
                      <Upload className="h-4 w-4 mr-2" />
                      Iniciar Importação
                    </>
                  )}
                </button>
                <button
                  type="button"
                  onClick={resetImport}
                  className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700"
                >
                  Cancelar
                </button>
              </div>
            </div>
          )}

          {/* Results */}
          {result && (
            <div className="space-y-6">
              {/* Status */}
              <div className={`border rounded-lg p-6 ${
                result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'
              }`}>
                <div className="flex items-center mb-4">
                  {result.success ? (
                    <CheckCircle className="h-8 w-8 text-green-600 mr-3" />
                  ) : (
                    <XCircle className="h-8 w-8 text-red-600 mr-3" />
                  )}
                  <div>
                    <h3 className={`text-lg font-semibold ${
                      result.success ? 'text-green-900' : 'text-red-900'
                    }`}>
                      {result.success ? 'Importação Concluída!' : 'Erro na Importação'}
                    </h3>
                    <p className={`${
                      result.success ? 'text-green-700' : 'text-red-700'
                    }`}>
                      {result.message || (result.success ? 'Dados importados com sucesso' : 'Ocorreu um erro durante a importação')}
                    </p>
                  </div>
                </div>

                {/* Stats */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">{result.stats.total}</div>
                    <div className="text-sm text-gray-600">Total</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">{result.stats.created}</div>
                    <div className="text-sm text-gray-600">Criados</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{result.stats.updated}</div>
                    <div className="text-sm text-gray-600">Atualizados</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">{result.stats.errors}</div>
                    <div className="text-sm text-gray-600">Erros</div>
                  </div>
                </div>
              </div>

              {/* Errors */}
              {result.errors && result.errors.length > 0 && (
                <div className="border border-yellow-200 bg-yellow-50 rounded-lg p-6">
                  <div className="flex items-center mb-4">
                    <AlertCircle className="h-6 w-6 text-yellow-600 mr-2" />
                    <h3 className="text-lg font-semibold text-yellow-900">
                      Erros Encontrados ({result.errors.length})
                    </h3>
                  </div>
                  <div className="max-h-60 overflow-y-auto">
                    {result.errors.map((error, index) => (
                      <div key={index} className="bg-white border border-yellow-200 rounded p-3 mb-2">
                        <p className="text-sm font-medium text-red-600">{error.error}</p>
                        <p className="text-xs text-gray-600 mt-1">
                          Linha: {JSON.stringify(error.row)}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={resetImport}
                  className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700"
                >
                  Nova Importação
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default StripeImport;
