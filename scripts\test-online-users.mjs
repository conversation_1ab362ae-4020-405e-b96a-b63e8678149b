import { initializeApp } from 'firebase/app';
import { getFirestore, collection, doc, setDoc, serverTimestamp, deleteDoc, getDocs } from 'firebase/firestore';
import { getAuth, signInWithEmailAndPassword } from 'firebase/auth';

// Configuração do Firebase
const FIREBASE_CONFIG = {
  apiKey: "AIzaSyB04qDOcU5jdZAYy-yaYQRUrrYQ_l49vcY",
  authDomain: "promandato-9a4cf.firebaseapp.com",
  projectId: "promandato-9a4cf",
  storageBucket: "promandato-9a4cf.firebasestorage.app",
  messagingSenderId: "517140455601",
  appId: "1:517140455601:web:fa2eb0ec2f88b506594290",
  measurementId: "G-MXYM2GKJS5"
};

// Inicializa o Firebase
const app = initializeApp(FIREBASE_CONFIG);
const db = getFirestore(app);
const auth = getAuth(app);

// Dados de usuários de teste para simular usuários online
const testUsers = [
  {
    email: '<EMAIL>',
    password: 'admin123',
    userData: {
      name: 'Administrador Principal',
      role: 'admin',
      device: 'Desktop',
      browser: 'Chrome',
      location: 'São Paulo, SP'
    }
  },
  {
    email: '<EMAIL>',
    password: 'staff123',
    userData: {
      name: 'Membro da Equipe',
      role: 'staff',
      device: 'Mobile',
      browser: 'Safari',
      location: 'Rio de Janeiro, RJ'
    }
  },
  {
    email: '<EMAIL>',
    password: 'master123',
    userData: {
      name: 'Usuário Master',
      role: 'master',
      device: 'Tablet',
      browser: 'Firefox',
      location: 'Belo Horizonte, MG'
    }
  }
];

// Função para simular usuários online
async function simulateOnlineUsers() {
  console.log('🚀 Iniciando simulação de usuários online...\n');

  try {
    // Limpar dados anteriores
    console.log('🧹 Limpando dados anteriores...');
    await clearOnlineUsers();

    // Simular login de usuários
    for (const testUser of testUsers) {
      try {
        console.log(`👤 Simulando login de: ${testUser.email}`);
        
        // Fazer login
        const userCredential = await signInWithEmailAndPassword(auth, testUser.email, testUser.password);
        const userId = userCredential.user.uid;
        
        // Criar dados de usuário online
        const now = new Date().toISOString();
        const onlineUserData = {
          id: userId,
          name: testUser.userData.name,
          email: testUser.email,
          role: testUser.userData.role,
          status: getRandomStatus(),
          lastActivity: now,
          sessionStart: now,
          sessionDuration: 0,
          device: testUser.userData.device,
          browser: testUser.userData.browser,
          location: testUser.userData.location,
          currentPage: getRandomPage(),
          actionsToday: Math.floor(Math.random() * 50),
          ipAddress: generateRandomIP()
        };

        // Salvar no Firestore
        await setDoc(doc(db, 'online_users', userId), onlineUserData);
        
        console.log(`✅ Usuário ${testUser.userData.name} adicionado como online`);
        
        // Simular sessão
        const sessionData = {
          id: `${userId}_${Date.now()}`,
          userId: userId,
          sessionStart: now,
          lastActivity: now,
          device: testUser.userData.device,
          browser: testUser.userData.browser,
          location: testUser.userData.location,
          currentPage: getRandomPage(),
          isActive: true
        };

        await setDoc(doc(db, 'user_sessions', sessionData.id), sessionData);
        console.log(`📊 Sessão criada para ${testUser.userData.name}`);
        
      } catch (error) {
        console.error(`❌ Erro ao simular usuário ${testUser.email}:`, error.message);
      }
    }

    console.log('\n🎉 Simulação concluída! Usuários online criados com sucesso.');
    console.log('\n📋 Resumo:');
    
    // Mostrar resumo
    const onlineUsersSnapshot = await getDocs(collection(db, 'online_users'));
    console.log(`👥 Total de usuários online: ${onlineUsersSnapshot.size}`);
    
    onlineUsersSnapshot.forEach((doc) => {
      const userData = doc.data();
      console.log(`   • ${userData.name} (${userData.role}) - ${userData.status} - ${userData.device}`);
    });

    console.log('\n💡 Agora você pode testar a página "Clientes Online" na aplicação!');
    console.log('🔗 Acesse: http://localhost:3000/#/online-clients');
    
  } catch (error) {
    console.error('❌ Erro na simulação:', error);
  }
}

// Função para limpar usuários online
async function clearOnlineUsers() {
  try {
    const onlineUsersSnapshot = await getDocs(collection(db, 'online_users'));
    const sessionsSnapshot = await getDocs(collection(db, 'user_sessions'));
    
    // Deletar usuários online
    for (const doc of onlineUsersSnapshot.docs) {
      await deleteDoc(doc.ref);
    }
    
    // Deletar sessões
    for (const doc of sessionsSnapshot.docs) {
      await deleteDoc(doc.ref);
    }
    
    console.log('✅ Dados anteriores limpos');
  } catch (error) {
    console.error('❌ Erro ao limpar dados:', error);
  }
}

// Funções auxiliares
function getRandomStatus() {
  const statuses = ['online', 'away', 'busy'];
  return statuses[Math.floor(Math.random() * statuses.length)];
}

function getRandomPage() {
  const pages = ['/dashboard', '/demands', '/citizens', '/agenda', '/documents', '/reports'];
  return pages[Math.floor(Math.random() * pages.length)];
}

function generateRandomIP() {
  return `192.168.1.${Math.floor(Math.random() * 255)}`;
}

// Função para limpar todos os dados (útil para reset)
async function clearAllOnlineData() {
  console.log('🧹 Limpando todos os dados de usuários online...');
  await clearOnlineUsers();
  console.log('✅ Todos os dados limpos!');
}

// Verificar argumentos da linha de comando
const args = process.argv.slice(2);

if (args.includes('--clear')) {
  clearAllOnlineData().then(() => process.exit(0));
} else {
  simulateOnlineUsers().then(() => process.exit(0));
}
