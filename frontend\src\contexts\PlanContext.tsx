import React, { createContext, useState, useEffect, ReactNode } from 'react';
import { PlanType, Plan, PLANS } from '../types/plans';
import { useAuth } from '../hooks/useAuth';

interface PlanContextType {
  currentPlan: Plan | null;
  planType: PlanType;
  isLoading: boolean;

  // Métodos para verificar recursos
  canUseFeature: (feature: string) => boolean;
  canUseAIFeature: (featureId: string) => boolean;
  getAIFeatureUsage: (featureId: string) => { used: number; limit: number } | null;
  getRemainingUsage: (featureId: string) => number;

  // Métodos para gerenciar plano
  upgradePlan: (newPlanType: PlanType) => Promise<void>;
  downgradePlan: (newPlanType: PlanType) => Promise<void>;
  updateUserPlan: (newPlanType: PlanType) => Promise<void>;

  // Métodos para IA
  incrementAIUsage: (featureId: string) => Promise<boolean>;
  resetAIUsage: (featureId: string) => Promise<void>;

  // Verificações de limites
  hasReachedLimit: (resource: string) => boolean;
  getResourceUsage: (resource: string) => { used: number; limit: number };
}

const PlanContext = createContext<PlanContextType | undefined>(undefined);

interface PlanProviderProps {
  children: ReactNode;
}

export const PlanProvider: React.FC<PlanProviderProps> = ({ children }) => {
  const { currentUser } = useAuth();
  const [currentPlan, setCurrentPlan] = useState<Plan | null>(null);
  const [planType, setPlanType] = useState<PlanType>(PlanType.BASIC);
  const [isLoading, setIsLoading] = useState(true);
  const [aiUsage, setAiUsage] = useState<Record<string, number>>({});
  const [resourceUsage, setResourceUsage] = useState<Record<string, number>>({
    users: 0,
    demands: 0,
    citizens: 0,
    documents: 0,
    storage: 0
  });

  // Carregar plano do usuário
  useEffect(() => {
    const loadUserPlan = async () => {
      if (!currentUser) {
        setIsLoading(false);
        return;
      }

      try {
        // Carregar plano do usuário baseado no planId
        const userPlanId = (currentUser as any).planId || 'BASIC';
        const plan = PLANS.find(p => p.id === userPlanId) || PLANS[0];
        
        setCurrentPlan(plan);
        setPlanType(userPlanId as PlanType);
        
        // Carregar uso de recursos e IA
        await loadUsageData();
      } catch (error) {
        console.error('Erro ao carregar plano do usuário:', error);
        // Fallback para plano básico
        setCurrentPlan(PLANS[0]);
        setPlanType(PlanType.BASIC);
      } finally {
        setIsLoading(false);
      }
    };

    loadUserPlan();
  }, [currentUser]);

  const loadUsageData = async () => {
    try {
      // Carregar dados de uso do Firebase
      // Por enquanto, dados mockados
      setAiUsage({
        demand_analysis: 45,
        response_suggestions: 23,
        sentiment_analysis: 156,
        content_generation: 12,
        predictive_analytics: 8,
        document_summarization: 34,
        citizen_insights: 67,
        automated_scheduling: 89
      });

      setResourceUsage({
        users: 2,
        demands: 234,
        citizens: 567,
        documents: 45,
        storage: 2.3
      });
    } catch (error) {
      console.error('Erro ao carregar dados de uso:', error);
    }
  };

  const canUseFeature = (feature: string): boolean => {
    if (!currentPlan) return false;

    switch (feature) {
      case 'bulkMessages':
        return currentPlan.features.bulkMessages;
      case 'socialMediaIntegration':
        return currentPlan.features.socialMediaIntegration;
      case 'advancedReports':
        return currentPlan.features.advancedReports;
      case 'customReports':
        return currentPlan.features.customReports;
      case 'apiAccess':
        return currentPlan.features.apiAccess;
      case 'webhooks':
        return currentPlan.features.webhooks;
      default:
        return true;
    }
  };

  const canUseAIFeature = (featureId: string): boolean => {
    if (!currentPlan) return false;
    
    const aiFeature = currentPlan.features.aiFeatures.find(f => f.id === featureId);
    if (!aiFeature || !aiFeature.enabled) return false;
    
    if (aiFeature.usageLimit) {
      const used = aiUsage[featureId] || 0;
      return used < aiFeature.usageLimit;
    }
    
    return true;
  };

  const getAIFeatureUsage = (featureId: string) => {
    if (!currentPlan) return null;
    
    const aiFeature = currentPlan.features.aiFeatures.find(f => f.id === featureId);
    if (!aiFeature) return null;
    
    return {
      used: aiUsage[featureId] || 0,
      limit: aiFeature.usageLimit || -1
    };
  };

  const getRemainingUsage = (featureId: string): number => {
    const usage = getAIFeatureUsage(featureId);
    if (!usage || usage.limit === -1) return -1;
    
    return Math.max(0, usage.limit - usage.used);
  };

  const incrementAIUsage = async (featureId: string): Promise<boolean> => {
    if (!canUseAIFeature(featureId)) return false;
    
    try {
      const newUsage = { ...aiUsage };
      newUsage[featureId] = (newUsage[featureId] || 0) + 1;
      setAiUsage(newUsage);
      
      // Salvar no Firebase
      // await updateAIUsage(user.uid, featureId, newUsage[featureId]);
      
      return true;
    } catch (error) {
      console.error('Erro ao incrementar uso de IA:', error);
      return false;
    }
  };

  const resetAIUsage = async (featureId: string): Promise<void> => {
    try {
      const newUsage = { ...aiUsage };
      newUsage[featureId] = 0;
      setAiUsage(newUsage);
      
      // Salvar no Firebase
      // await updateAIUsage(user.uid, featureId, 0);
    } catch (error) {
      console.error('Erro ao resetar uso de IA:', error);
    }
  };

  const hasReachedLimit = (resource: string): boolean => {
    if (!currentPlan) return true;
    
    const used = resourceUsage[resource] || 0;
    const limit = (currentPlan.features as any)[`max${resource.charAt(0).toUpperCase() + resource.slice(1)}`];
    
    if (limit === -1) return false; // Ilimitado
    return used >= limit;
  };

  const getResourceUsage = (resource: string) => {
    const used = resourceUsage[resource] || 0;
    const limit = currentPlan ? (currentPlan.features as any)[`max${resource.charAt(0).toUpperCase() + resource.slice(1)}`] : 0;
    
    return { used, limit };
  };

  const upgradePlan = async (newPlanType: PlanType): Promise<void> => {
    try {
      setIsLoading(true);
      
      // Aqui você implementaria a lógica de upgrade no Firebase
      // await updateUserPlan(user.uid, newPlanType);
      
      const newPlan = PLANS.find(p => p.id === newPlanType);
      if (newPlan) {
        setCurrentPlan(newPlan);
        setPlanType(newPlanType);
      }
    } catch (error) {
      console.error('Erro ao fazer upgrade do plano:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const downgradePlan = async (newPlanType: PlanType): Promise<void> => {
    try {
      setIsLoading(true);

      // Aqui você implementaria a lógica de downgrade no Firebase
      // await updateUserPlan(user.uid, newPlanType);

      const newPlan = PLANS.find(p => p.id === newPlanType);
      if (newPlan) {
        setCurrentPlan(newPlan);
        setPlanType(newPlanType);
      }
    } catch (error) {
      console.error('Erro ao fazer downgrade do plano:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateUserPlan = async (newPlanType: PlanType): Promise<void> => {
    try {
      setIsLoading(true);

      // Aqui você implementaria a lógica de atualização no Firebase
      // await updateUserPlanInFirebase(user.uid, newPlanType);

      const newPlan = PLANS.find(p => p.id === newPlanType);
      if (newPlan) {
        setCurrentPlan(newPlan);
        setPlanType(newPlanType);
      }
    } catch (error) {
      console.error('Erro ao atualizar plano do usuário:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const value: PlanContextType = {
    currentPlan,
    planType,
    isLoading,
    canUseFeature,
    canUseAIFeature,
    getAIFeatureUsage,
    getRemainingUsage,
    upgradePlan,
    downgradePlan,
    updateUserPlan,
    incrementAIUsage,
    resetAIUsage,
    hasReachedLimit,
    getResourceUsage
  };

  return (
    <PlanContext.Provider value={value}>
      {children}
    </PlanContext.Provider>
  );
};

// Hook para usar o contexto
export const usePlan = (): PlanContextType => {
  const context = React.useContext(PlanContext);
  if (context === undefined) {
    throw new Error('usePlan must be used within a PlanProvider');
  }
  return context;
};

export default PlanContext;