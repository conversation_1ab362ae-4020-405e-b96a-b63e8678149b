import React, { useState } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import { Elements, CardElement, useStripe, useElements } from '@stripe/react-stripe-js';
import { CreditCard, Lock, CheckCircle } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { usePlan } from '../../hooks/usePlan';
import { PlanType } from '../../types/plans';

// Configuração do Stripe
const stripePromise = loadStripe(process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY || 'pk_test_...');

interface StripeCheckoutProps {
  planId: string;
  planName: string;
  price: number;
  billingCycle: 'monthly' | 'yearly';
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

const CheckoutForm: React.FC<StripeCheckoutProps> = ({
  planId,
  planName,
  price,
  billingCycle,
  onSuccess,
  onError
}) => {
  const stripe = useStripe();
  const elements = useElements();
  const { currentUser } = useAuth();
  const { updateUserPlan } = usePlan();
  
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentError, setPaymentError] = useState<string | null>(null);
  const [paymentSuccess, setPaymentSuccess] = useState(false);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!stripe || !elements || !currentUser) {
      return;
    }

    setIsProcessing(true);
    setPaymentError(null);

    const cardElement = elements.getElement(CardElement);

    if (!cardElement) {
      setPaymentError('Erro ao carregar formulário de pagamento');
      setIsProcessing(false);
      return;
    }

    try {
      // Criar Payment Intent no backend
      const response = await fetch('/api/stripe/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          planId,
          billingCycle,
          userId: currentUser.id,
          userEmail: currentUser.email
        })
      });

      if (!response.ok) {
        throw new Error('Erro ao processar pagamento');
      }

      const { clientSecret, subscriptionId } = await response.json();

      // Confirmar pagamento com Stripe
      const { error, paymentIntent } = await stripe.confirmCardPayment(clientSecret, {
        payment_method: {
          card: cardElement,
          billing_details: {
            name: currentUser.name,
            email: currentUser.email,
          },
        },
      });

      if (error) {
        setPaymentError(error.message || 'Erro no pagamento');
        onError?.(error.message || 'Erro no pagamento');
      } else if (paymentIntent.status === 'succeeded') {
        setPaymentSuccess(true);
        
        // Atualizar plano do usuário
        await updateUserPlan(planId as PlanType);
        
        // Notificar sucesso
        onSuccess?.();
        
        // Redirecionar após sucesso
        setTimeout(() => {
          window.location.href = '/dashboard?payment=success';
        }, 2000);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      setPaymentError(errorMessage);
      onError?.(errorMessage);
    } finally {
      setIsProcessing(false);
    }
  };

  if (paymentSuccess) {
    return (
      <div className="text-center py-8">
        <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          Pagamento Realizado com Sucesso!
        </h3>
        <p className="text-gray-600 mb-4">
          Seu plano {planName} foi ativado. Redirecionando...
        </p>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Resumo do Plano */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="font-semibold text-gray-900 mb-2">Resumo do Pedido</h3>
        <div className="flex justify-between items-center">
          <span className="text-gray-600">Plano {planName}</span>
          <span className="font-semibold">
            R$ {price.toFixed(2)}/{billingCycle === 'monthly' ? 'mês' : 'ano'}
          </span>
        </div>
        {billingCycle === 'yearly' && (
          <p className="text-sm text-green-600 mt-1">
            ✓ Economia de 20% no plano anual
          </p>
        )}
      </div>

      {/* Formulário de Cartão */}
      <div className="space-y-4">
        <label className="block text-sm font-medium text-gray-700">
          <CreditCard className="h-4 w-4 inline mr-2" />
          Informações do Cartão
        </label>
        
        <div className="border border-gray-300 rounded-lg p-3 bg-white">
          <CardElement
            options={{
              style: {
                base: {
                  fontSize: '16px',
                  color: '#424770',
                  '::placeholder': {
                    color: '#aab7c4',
                  },
                },
                invalid: {
                  color: '#9e2146',
                },
              },
            }}
          />
        </div>
      </div>

      {/* Erro de Pagamento */}
      {paymentError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600 text-sm">{paymentError}</p>
        </div>
      )}

      {/* Informações de Segurança */}
      <div className="flex items-center text-sm text-gray-500">
        <Lock className="h-4 w-4 mr-2" />
        <span>Pagamento seguro processado pelo Stripe</span>
      </div>

      {/* Botão de Pagamento */}
      <button
        type="submit"
        disabled={!stripe || isProcessing}
        className={`w-full py-3 px-4 rounded-lg font-semibold text-white transition-colors ${
          isProcessing
            ? 'bg-gray-400 cursor-not-allowed'
            : 'bg-blue-600 hover:bg-blue-700'
        }`}
      >
        {isProcessing ? (
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
            Processando...
          </div>
        ) : (
          `Pagar R$ ${price.toFixed(2)}`
        )}
      </button>

      {/* Termos */}
      <p className="text-xs text-gray-500 text-center">
        Ao confirmar o pagamento, você concorda com nossos{' '}
        <a href="/terms" className="text-blue-600 hover:underline">
          Termos de Serviço
        </a>{' '}
        e{' '}
        <a href="/privacy" className="text-blue-600 hover:underline">
          Política de Privacidade
        </a>
      </p>
    </form>
  );
};

const StripeCheckout: React.FC<StripeCheckoutProps> = (props) => {
  return (
    <Elements stripe={stripePromise}>
      <CheckoutForm {...props} />
    </Elements>
  );
};

export default StripeCheckout;
