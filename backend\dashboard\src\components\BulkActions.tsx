import React, { useState } from 'react';
import { Plan, PlanUpdateRequest } from '../types/plans';

interface BulkActionsProps {
  plans: Plan[];
  onBulkUpdate: (updates: PlanUpdateRequest[]) => void;
  onReload: () => void;
}

const BulkActions: React.FC<BulkActionsProps> = ({ plans, onBulkUpdate, onReload }) => {
  const [showBulkPricing, setShowBulkPricing] = useState(false);
  const [bulkPriceIncrease, setBulkPriceIncrease] = useState(0);
  const [bulkPriceType, setBulkPriceType] = useState<'percentage' | 'fixed'>('percentage');

  const handleBulkPriceUpdate = () => {
    const updates: PlanUpdateRequest[] = plans.map(plan => {
      let newMonthly = plan.price.monthly;
      let newYearly = plan.price.yearly;

      if (bulkPriceType === 'percentage') {
        newMonthly = plan.price.monthly * (1 + bulkPriceIncrease / 100);
        newYearly = plan.price.yearly * (1 + bulkPriceIncrease / 100);
      } else {
        newMonthly = plan.price.monthly + bulkPriceIncrease;
        newYearly = plan.price.yearly + (bulkPriceIncrease * 12);
      }

      return {
        planId: plan.id,
        updates: {
          price: {
            monthly: Math.round(newMonthly * 100) / 100,
            yearly: Math.round(newYearly * 100) / 100
          },
          updatedAt: new Date().toISOString()
        }
      };
    });

    onBulkUpdate(updates);
    setShowBulkPricing(false);
    setBulkPriceIncrease(0);
  };

  const handleBulkToggle = (enabled: boolean) => {
    const updates: PlanUpdateRequest[] = plans.map(plan => ({
      planId: plan.id,
      updates: {
        enabled,
        updatedAt: new Date().toISOString()
      }
    }));

    onBulkUpdate(updates);
  };

  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const config = JSON.parse(e.target?.result as string);
        // Aqui você pode implementar a lógica de importação
        console.log('Configuração importada:', config);
        onReload();
      } catch (error) {
        alert('Erro ao importar arquivo: formato inválido');
      }
    };
    reader.readAsText(file);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-8">
      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Ações em Massa</h3>
      
      <div className="flex flex-wrap gap-4">
        {/* Bulk Pricing */}
        <button
          type="button"
          onClick={() => setShowBulkPricing(true)}
          className="inline-flex items-center px-4 py-2 border border-blue-300 dark:border-blue-600 rounded-md shadow-sm text-sm font-medium text-blue-700 dark:text-blue-300 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30"
        >
          💰 Ajustar Preços
        </button>

        {/* Bulk Enable */}
        <button
          type="button"
          onClick={() => handleBulkToggle(true)}
          className="inline-flex items-center px-4 py-2 border border-green-300 dark:border-green-600 rounded-md shadow-sm text-sm font-medium text-green-700 dark:text-green-300 bg-green-50 dark:bg-green-900/20 hover:bg-green-100 dark:hover:bg-green-900/30"
        >
          🟢 Ativar Todos
        </button>

        {/* Bulk Disable */}
        <button
          type="button"
          onClick={() => handleBulkToggle(false)}
          className="inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md shadow-sm text-sm font-medium text-red-700 dark:text-red-300 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30"
        >
          🔴 Desativar Todos
        </button>

        {/* Import Config */}
        <label className="inline-flex items-center px-4 py-2 border border-purple-300 dark:border-purple-600 rounded-md shadow-sm text-sm font-medium text-purple-700 dark:text-purple-300 bg-purple-50 dark:bg-purple-900/20 hover:bg-purple-100 dark:hover:bg-purple-900/30 cursor-pointer">
          📁 Importar Config
          <input
            type="file"
            accept=".json"
            onChange={handleFileImport}
            className="hidden"
          />
        </label>

        {/* Reload */}
        <button
          type="button"
          onClick={onReload}
          className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
        >
          🔄 Recarregar
        </button>
      </div>

      {/* Bulk Pricing Modal */}
      {showBulkPricing && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Ajustar Preços em Massa
              </h3>
            </div>

            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Tipo de Ajuste
                </label>
                <select
                  value={bulkPriceType}
                  onChange={(e) => setBulkPriceType(e.target.value as 'percentage' | 'fixed')}
                  title="Tipo de ajuste de preço"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="percentage">Percentual (%)</option>
                  <option value="fixed">Valor Fixo (R$)</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {bulkPriceType === 'percentage' ? 'Percentual de Aumento/Desconto' : 'Valor a Adicionar/Subtrair'}
                </label>
                <input
                  type="number"
                  value={bulkPriceIncrease}
                  onChange={(e) => setBulkPriceIncrease(parseFloat(e.target.value))}
                  step={bulkPriceType === 'percentage' ? '0.1' : '0.01'}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  placeholder={bulkPriceType === 'percentage' ? 'Ex: 10 para +10%' : 'Ex: 50 para +R$50'}
                />
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  {bulkPriceType === 'percentage' 
                    ? 'Use valores negativos para desconto (ex: -10 para -10%)'
                    : 'Use valores negativos para redução (ex: -50 para -R$50)'
                  }
                </p>
              </div>

              <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-md">
                <h4 className="text-sm font-medium text-blue-900 dark:text-blue-300 mb-2">Preview das Alterações</h4>
                <div className="space-y-1 text-xs text-blue-700 dark:text-blue-400">
                  {plans.map(plan => {
                    let newMonthly = plan.price.monthly;
                    if (bulkPriceType === 'percentage') {
                      newMonthly = plan.price.monthly * (1 + bulkPriceIncrease / 100);
                    } else {
                      newMonthly = plan.price.monthly + bulkPriceIncrease;
                    }
                    return (
                      <div key={plan.id}>
                        {plan.name}: R$ {plan.price.monthly.toFixed(2)} → R$ {Math.max(0, newMonthly).toFixed(2)}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>

            <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end space-x-3">
              <button
                type="button"
                onClick={() => setShowBulkPricing(false)}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 bg-white dark:bg-gray-800"
              >
                Cancelar
              </button>
              <button
                type="button"
                onClick={handleBulkPriceUpdate}
                className="px-4 py-2 bg-blue-600 dark:bg-blue-700 text-white rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-600"
              >
                Aplicar Alterações
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BulkActions;