name: Deploy ProMandato Landing

on:
  push:
    branches: [main]
    paths: ['landingpage/**']
  workflow_dispatch:

jobs:
  deploy:
    name: Deploy to Firebase
    runs-on: ubuntu-latest

    steps:
    - name: Checkout Repository
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'

    - name: Install Firebase CLI
      run: npm install -g firebase-tools

    - name: Verify Firebase Project
      run: firebase use promandato-9a4cf
      env:
        FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}

    - name: Deploy to Firebase Hosting
      run: firebase deploy --only hosting --project promandato-9a4cf
      env:
        FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
