# ===========================================
# CONFIGURAÇÕES DE AMBIENTE - PROMANDATO
# ===========================================
# Copie este arquivo para .env e configure as variáveis

# ===========================================
# AMBIENTE
# ===========================================
NODE_ENV=production
PORT=8080

# ===========================================
# SEGURANÇA
# ===========================================
# Gere uma chave JWT segura: openssl rand -base64 32
JWT_SECRET=sua_chave_jwt_super_secreta_aqui

# Configurações de segurança
BCRYPT_SALT_ROUNDS=12
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900000
SESSION_TIMEOUT=3600000
PASSWORD_MIN_LENGTH=8
REQUIRE_TWO_FACTOR=false

# ===========================================
# STRIPE
# ===========================================
# Chaves do Stripe (obter no Dashboard do Stripe)
STRIPE_SECRET_KEY=sk_live_sua_chave_secreta_stripe
STRIPE_WEBHOOK_SECRET=whsec_sua_chave_webhook_stripe

# IDs dos produtos/preços no Stripe
STRIPE_BASIC_MONTHLY_PRICE_ID=price_1234567890
STRIPE_BASIC_YEARLY_PRICE_ID=price_1234567891
STRIPE_STANDARD_MONTHLY_PRICE_ID=price_1234567892
STRIPE_STANDARD_YEARLY_PRICE_ID=price_1234567893
STRIPE_PROFESSIONAL_MONTHLY_PRICE_ID=price_1234567894
STRIPE_PROFESSIONAL_YEARLY_PRICE_ID=price_1234567895

# ===========================================
# URLs E DOMÍNIOS
# ===========================================
# URL do frontend para redirecionamentos
FRONTEND_URL=https://promandato-9a4cf.web.app

# URLs de redirecionamento do Stripe
STRIPE_SUCCESS_URL=https://promandato-9a4cf.web.app/success
STRIPE_CANCEL_URL=https://promandato-9a4cf.web.app/pricing

# Domínios permitidos para CORS
ALLOWED_ORIGINS=https://promandato-9a4cf.web.app,https://promandato.web.app

# ===========================================
# FIREBASE (se usado no backend)
# ===========================================
FIREBASE_PROJECT_ID=promandato-9a4cf
FIREBASE_PRIVATE_KEY_ID=sua_private_key_id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nsua_private_key_aqui\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=sua_client_id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token

# ===========================================
# EMAIL (se usado)
# ===========================================
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASS=sua_senha_app_gmail
ADMIN_EMAIL=<EMAIL>

# ===========================================
# RATE LIMITING
# ===========================================
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# ===========================================
# LOGGING
# ===========================================
LOG_LEVEL=info
LOG_FILE=logs/app.log

# ===========================================
# DESENVOLVIMENTO (apenas para dev/staging)
# ===========================================
# Descomente apenas em desenvolvimento
# DEBUG=true
# VERBOSE_LOGGING=true