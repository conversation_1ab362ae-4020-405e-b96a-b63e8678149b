import express from 'express';
import { body, query, validationResult } from 'express-validator';
import Notification from '../models/Notification.js';
import { authenticateToken, requirePermission } from '../middleware/auth.js';

const router = express.Router();

// Aplicar autenticação a todas as rotas
router.use(authenticateToken);

// Validações
const createNotificationValidation = [
  body('title').notEmpty().withMessage('Título é obrigatório'),
  body('message').notEmpty().withMessage('Mensagem é obrigatória'),
  body('type').isIn(['info', 'success', 'warning', 'error']).withMessage('Tipo inválido'),
  body('category').optional().isIn(['demand', 'event', 'system', 'general']).withMessage('Categoria inválida'),
  body('userId').notEmpty().withMessage('ID do usuário é obrigatório')
];

const queryValidation = [
  query('read').optional().isBoolean().withMessage('Parâmetro read deve ser boolean'),
  query('category').optional().isIn(['demand', 'event', 'system', 'general']).withMessage('Categoria inválida'),
  query('type').optional().isIn(['info', 'success', 'warning', 'error']).withMessage('Tipo inválido'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limite deve ser entre 1 e 100')
];

// GET /api/notifications - Listar notificações do usuário atual
router.get('/', queryValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Parâmetros inválidos',
        errors: errors.array()
      });
    }

    const userId = req.user.id;
    const { read, category, type, limit } = req.query;

    const options = {};
    if (read !== undefined) options.read = read === 'true';
    if (category) options.category = category;
    if (type) options.type = type;
    if (limit) options.limit = parseInt(limit);

    const notifications = await Notification.findByUserId(userId, options);

    res.json({
      success: true,
      data: notifications,
      count: notifications.length
    });

  } catch (error) {
    console.error('Erro ao buscar notificações:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// GET /api/notifications/unread-count - Contar notificações não lidas
router.get('/unread-count', async (req, res) => {
  try {
    const userId = req.user.id;
    const count = await Notification.getUnreadCountForUser(userId);

    res.json({
      success: true,
      data: { count }
    });

  } catch (error) {
    console.error('Erro ao contar notificações não lidas:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// GET /api/notifications/:id - Buscar notificação específica
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const notification = await Notification.findById(id);

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: 'Notificação não encontrada'
      });
    }

    // Verificar se a notificação pertence ao usuário
    if (notification.userId !== userId) {
      return res.status(403).json({
        success: false,
        message: 'Acesso negado'
      });
    }

    res.json({
      success: true,
      data: notification
    });

  } catch (error) {
    console.error('Erro ao buscar notificação:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// POST /api/notifications - Criar nova notificação (apenas admins)
router.post('/', requirePermission('notifications.create'), createNotificationValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Dados inválidos',
        errors: errors.array()
      });
    }

    const notificationData = req.body;
    const notification = await Notification.create(notificationData);

    res.status(201).json({
      success: true,
      data: notification,
      message: 'Notificação criada com sucesso'
    });

  } catch (error) {
    console.error('Erro ao criar notificação:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// PUT /api/notifications/:id/read - Marcar notificação como lida
router.put('/:id/read', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const notification = await Notification.findById(id);

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: 'Notificação não encontrada'
      });
    }

    // Verificar se a notificação pertence ao usuário
    if (notification.userId !== userId) {
      return res.status(403).json({
        success: false,
        message: 'Acesso negado'
      });
    }

    await notification.markAsRead();

    res.json({
      success: true,
      data: notification,
      message: 'Notificação marcada como lida'
    });

  } catch (error) {
    console.error('Erro ao marcar notificação como lida:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// PUT /api/notifications/:id/unread - Marcar notificação como não lida
router.put('/:id/unread', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const notification = await Notification.findById(id);

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: 'Notificação não encontrada'
      });
    }

    // Verificar se a notificação pertence ao usuário
    if (notification.userId !== userId) {
      return res.status(403).json({
        success: false,
        message: 'Acesso negado'
      });
    }

    await notification.markAsUnread();

    res.json({
      success: true,
      data: notification,
      message: 'Notificação marcada como não lida'
    });

  } catch (error) {
    console.error('Erro ao marcar notificação como não lida:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// PUT /api/notifications/mark-all-read - Marcar todas as notificações como lidas
router.put('/mark-all-read', async (req, res) => {
  try {
    const userId = req.user.id;
    const updated = await Notification.markAllAsReadForUser(userId);

    res.json({
      success: true,
      message: updated ? 'Todas as notificações foram marcadas como lidas' : 'Nenhuma notificação para atualizar'
    });

  } catch (error) {
    console.error('Erro ao marcar todas as notificações como lidas:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// DELETE /api/notifications/:id - Deletar notificação
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const notification = await Notification.findById(id);

    if (!notification) {
      return res.status(404).json({
        success: false,
        message: 'Notificação não encontrada'
      });
    }

    // Verificar se a notificação pertence ao usuário ou se é admin
    if (notification.userId !== userId && !req.user.permissions.includes('*')) {
      return res.status(403).json({
        success: false,
        message: 'Acesso negado'
      });
    }

    await notification.delete();

    res.json({
      success: true,
      message: 'Notificação deletada com sucesso'
    });

  } catch (error) {
    console.error('Erro ao deletar notificação:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

export default router;
