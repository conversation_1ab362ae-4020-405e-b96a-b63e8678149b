import React, { useState, useEffect } from 'react';
import { useAI, useAIDemandAnalysis } from '../hooks/useAI';
import { AI_FEATURES, AI_FEATURE_DESCRIPTIONS } from '../services/aiConfig';

interface AIFeaturesProps {
  className?: string;
}

const AIFeatures: React.FC<AIFeaturesProps> = ({ className = '' }) => {
  const ai = useAI();
  const demandAnalysis = useAIDemandAnalysis();
  
  const [selectedFeature, setSelectedFeature] = useState<string | null>(null);
  const [testText, setTestText] = useState('');
  const [analysisResult, setAnalysisResult] = useState<any>(null);

  const availableFeatures = ai.getAvailableFeatures();
  const usageStats = ai.getUsageStatistics();
  const usageLimits = ai.checkUsageLimits();

  const handleFeatureTest = async (featureId: string) => {
    if (!testText.trim()) {
      alert('Digite um texto para análise');
      return;
    }

    setAnalysisResult(null);
    
    try {
      let result;
      
      switch (featureId) {
        case AI_FEATURES.TEXT_ANALYSIS:
          result = await ai.analyzeText({ text: testText });
          break;
        case AI_FEATURES.AUTO_CATEGORIZATION:
          result = await ai.categorizeText({ text: testText });
          break;
        case AI_FEATURES.PREDICTIVE_ANALYTICS:
          result = await ai.predictAnalysis({ text: testText });
          break;
        case AI_FEATURES.SMART_RESPONSES:
          result = await ai.generateResponses({ text: testText });
          break;
        default:
          result = await demandAnalysis.analyzeDemand(testText);
      }
      
      setAnalysisResult(result);
    } catch (error) {
      console.error('Erro na análise:', error);
    }
  };

  const handleCompleteAnalysis = async () => {
    if (!testText.trim()) {
      alert('Digite um texto para análise completa');
      return;
    }

    const result = await demandAnalysis.analyzeDemand(testText);
    setAnalysisResult(result);
  };

  return (
    <div className={`ai-features ${className}`}>
      <div className="ai-features__header">
        <h2>Funcionalidades de IA</h2>
        <p>Teste e explore as capacidades de inteligência artificial disponíveis no seu plano</p>
      </div>

      {/* Status e Métricas */}
      <div className="ai-features__metrics">
        <div className="metrics-grid">
          <div className="metric-card">
            <h3>Requisições Totais</h3>
            <span className="metric-value">{usageStats.totalRequests}</span>
          </div>
          <div className="metric-card">
            <h3>Taxa de Sucesso</h3>
            <span className="metric-value">{usageStats.successRate}%</span>
          </div>
          <div className="metric-card">
            <h3>Tempo Médio</h3>
            <span className="metric-value">{usageStats.averageResponseTime}ms</span>
          </div>
          <div className="metric-card">
            <h3>Funcionalidade Mais Usada</h3>
            <span className="metric-value">{usageStats.mostUsedFeature}</span>
          </div>
        </div>
      </div>

      {/* Funcionalidades Disponíveis */}
      <div className="ai-features__available">
        <h3>Funcionalidades Disponíveis</h3>
        <div className="features-grid">
          {availableFeatures.map((feature) => {
            const description = AI_FEATURE_DESCRIPTIONS[feature.id as keyof typeof AI_FEATURE_DESCRIPTIONS];
            const usage = usageLimits[feature.id];
            
            return (
              <div 
                key={feature.id} 
                className={`feature-card ${selectedFeature === feature.id ? 'selected' : ''}`}
                onClick={() => setSelectedFeature(feature.id)}
              >
                <div className="feature-header">
                  <h4>{feature.name}</h4>
                  <span className={`feature-status ${feature.enabled ? 'enabled' : 'disabled'}`}>
                    {feature.enabled ? 'Ativo' : 'Inativo'}
                  </span>
                </div>
                
                <p className="feature-description">{description?.description}</p>
                
                {usage && (
                  <div className="feature-usage">
                    <div className="usage-bar">
                      <div 
                        className="usage-fill" 
                        style={{ width: `${Math.min(usage.percentage, 100)}%` }}
                      />
                    </div>
                    <span className="usage-text">
                      {usage.current} / {usage.limit === -1 ? '∞' : usage.limit}
                    </span>
                  </div>
                )}
                
                <div className="feature-actions">
                  <button 
                    type="button"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleFeatureTest(feature.id);
                    }}
                    disabled={!feature.enabled || ai.loading}
                    className="btn-test"
                  >
                    Testar
                  </button>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Área de Teste */}
      <div className="ai-features__test">
        <h3>Área de Teste</h3>
        <div className="test-area">
          <textarea
            value={testText}
            onChange={(e) => setTestText(e.target.value)}
            placeholder="Digite um texto de demanda para testar as funcionalidades de IA..."
            className="test-input"
            rows={4}
          />
          
          <div className="test-actions">
            <button 
              type="button"
              onClick={handleCompleteAnalysis}
              disabled={ai.loading || !testText.trim()}
              className="btn-primary"
            >
              {ai.loading ? 'Analisando...' : 'Análise Completa'}
            </button>
            
            <button 
              type="button"
              onClick={() => setTestText('')}
              className="btn-secondary"
            >
              Limpar
            </button>
          </div>
        </div>
      </div>

      {/* Resultado da Análise */}
      {analysisResult && (
        <div className="ai-features__result">
          <h3>Resultado da Análise</h3>
          <div className="result-container">
            <pre className="result-json">
              {JSON.stringify(analysisResult, null, 2)}
            </pre>
          </div>
        </div>
      )}

      {/* Mensagens de Status */}
      {ai.error && (
        <div className="ai-features__error">
          <div className="error-message">
            <span className="error-icon">⚠️</span>
            <span>{ai.error}</span>
            <button type="button" onClick={ai.clearError} className="error-close">×</button>
          </div>
        </div>
      )}

      {ai.success && (
        <div className="ai-features__success">
          <div className="success-message">
            <span className="success-icon">✅</span>
            <span>{ai.success}</span>
            <button type="button" onClick={ai.clearSuccess} className="success-close">×</button>
          </div>
        </div>
      )}

      {/* Detalhes da Funcionalidade Selecionada */}
      {selectedFeature && AI_FEATURE_DESCRIPTIONS[selectedFeature as keyof typeof AI_FEATURE_DESCRIPTIONS] && (
        <div className="ai-features__details">
          <h3>Detalhes da Funcionalidade</h3>
          <div className="feature-details">
            {(() => {
              const details = AI_FEATURE_DESCRIPTIONS[selectedFeature as keyof typeof AI_FEATURE_DESCRIPTIONS];
              return (
                <>
                  <h4>{details.name}</h4>
                  <p>{details.description}</p>
                  
                  <div className="details-section">
                    <h5>Benefícios:</h5>
                    <ul>
                      {details.benefits.map((benefit, index) => (
                        <li key={index}>{benefit}</li>
                      ))}
                    </ul>
                  </div>
                  
                  <div className="details-section">
                    <h5>Casos de Uso:</h5>
                    <ul>
                      {details.useCases.map((useCase, index) => (
                        <li key={index}>{useCase}</li>
                      ))}
                    </ul>
                  </div>
                </>
              );
            })()}
          </div>
        </div>
      )}
    </div>
  );
};

// Componente para exibir insights de IA
export const AIInsights: React.FC = () => {
  const [insights, setInsights] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const ai = useAI();

  const loadInsights = async () => {
    setLoading(true);
    try {
      const data = {
        demands: [], // Dados das demandas
        timeRange: {
          start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 dias atrás
          end: new Date()
        }
      };
      
      const result = await ai.generateInsights(data);
      if (result) {
        setInsights(result);
      }
    } catch (error) {
      console.error('Erro ao carregar insights:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (ai.checkFeatureAvailability(AI_FEATURES.PREDICTIVE_ANALYTICS)) {
      loadInsights();
    }
  }, []);

  if (!ai.checkFeatureAvailability(AI_FEATURES.PREDICTIVE_ANALYTICS)) {
    return (
      <div className="ai-insights">
        <div className="upgrade-notice">
          <h3>Insights de IA</h3>
          <p>Upgrade para o plano Profissional para acessar insights avançados de IA</p>
        </div>
      </div>
    );
  }

  return (
    <div className="ai-insights">
      <div className="insights-header">
        <h3>Insights de IA</h3>
        <button type="button" onClick={loadInsights} disabled={loading} className="btn-refresh">
          {loading ? 'Carregando...' : 'Atualizar'}
        </button>
      </div>
      
      <div className="insights-grid">
        {insights.map((insight, index) => (
          <div key={index} className={`insight-card ${insight.type}`}>
            <div className="insight-header">
              <span className={`insight-type ${insight.type}`}>{insight.type}</span>
              <span className={`insight-impact ${insight.impact}`}>{insight.impact}</span>
            </div>
            <h4>{insight.title}</h4>
            <p>{insight.description}</p>
            <div className="insight-confidence">
              Confiança: {Math.round(insight.confidence * 100)}%
            </div>
            {insight.actionRequired && (
              <div className="insight-action">
                <span className="action-required">Ação necessária</span>
              </div>
            )}
          </div>
        ))}
      </div>
      
      {insights.length === 0 && !loading && (
        <div className="no-insights">
          <p>Nenhum insight disponível no momento</p>
        </div>
      )}
    </div>
  );
};

export default AIFeatures;