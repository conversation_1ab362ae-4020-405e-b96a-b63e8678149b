import jsPDF from 'jspdf';
import 'jspdf-autotable';
import ExcelJS from 'exceljs';
// Define ReportData type locally if '../types' is missing
export type ReportData = {
  dateRange: { start: string | number | Date; end: string | number | Date };
  demandStatus: { status: string; count: number }[];
  teamPerformance: {
    userName: string;
    completedDemands: number;
    totalDemands: number;
    overdueTasks: number;
    averageResponseTime: number;
  }[];
  socialMedia: {
    platform: string;
    followers: number;
    engagement: number;
    reach: number;
    sentiment: { positive: number; neutral: number; negative: number };
  }[];
  politicianSocialMedia?: PoliticianSocialMedia[];
};

// Novo tipo para redes sociais do perfil do político
export type PoliticianSocialMedia = {
  platform: string;
  url: string;
  username?: string;
};

// Função auxiliar para formatar dados para exportação
const prepareDataForExport = (reportData: ReportData) => {
  const demandStatusRows = reportData.demandStatus.map(item => ({
    'Status': item.status,
    'Quantidade': item.count,
    'Porcentagem': `${(item.count / reportData.demandStatus.reduce((acc, cur) => acc + cur.count, 0) * 100).toFixed(1)}%`
  }));

  const teamPerformanceRows = reportData.teamPerformance.map(member => {
    const completionRate = member.totalDemands > 0 
      ? (member.completedDemands / member.totalDemands) * 100 
      : 0;
    
    // Calcular pontuação de eficiência
    const overdueRate = member.totalDemands > 0 
      ? member.overdueTasks / member.totalDemands 
      : 0;
    
    const efficiencyScore = Math.round(
      (completionRate * 0.6 + (1 - overdueRate) * 100 * 0.4)
    );
    
    return {
      'Membro': member.userName,
      'Demandas Concluídas': member.completedDemands,
      'Total de Demandas': member.totalDemands,
      'Taxa de Conclusão': `${completionRate.toFixed(1)}%`,
      'Tempo Médio (h)': member.averageResponseTime,
      'Tarefas Atrasadas': member.overdueTasks,
      'Eficiência': `${efficiencyScore}%`
    };
  });

  const socialMediaRows = reportData.socialMedia.map(platform => ({
    'Plataforma': platform.platform,
    'Seguidores': platform.followers,
    'Engajamento': `${platform.engagement.toFixed(1)}%`,
    'Alcance': platform.reach,
    'Sentimento Positivo': `${platform.sentiment.positive.toFixed(1)}%`,
    'Sentimento Neutro': `${platform.sentiment.neutral.toFixed(1)}%`,
    'Sentimento Negativo': `${platform.sentiment.negative.toFixed(1)}%`
  }));

  return {
    demandStatusRows,
    teamPerformanceRows,
    socialMediaRows
  };
};

// Função auxiliar para preparar dados de redes sociais do político
const preparePoliticianSocialMediaRows = (socialMedia: PoliticianSocialMedia[]) => {
  return socialMedia.map(platform => ({
    'Plataforma': platform.platform,
    'Usuário': platform.username || 'N/A',
    'URL': platform.url
  }));
};

// Exportar para PDF
export const exportToPDF = (reportData: ReportData) => {
  console.log('Iniciando exportação para PDF com dados:', reportData);
  
  try {
    const { demandStatusRows, teamPerformanceRows, socialMediaRows } = prepareDataForExport(reportData);
    const doc = new jsPDF();

    // Título
    doc.setFontSize(16);
    doc.text('Relatório ProMandato', 14, 15);
    doc.setFontSize(10);
    doc.text(`Período: ${new Date(reportData.dateRange.start).toLocaleDateString()} - ${new Date(reportData.dateRange.end).toLocaleDateString()}`, 14, 25);

    // Status das Demandas
    doc.setFontSize(12);
    doc.text('Status das Demandas', 14, 35);
    (doc as any).autoTable({
      startY: 40,
      head: [['Status', 'Quantidade', 'Porcentagem']],
      body: demandStatusRows.map(row => Object.values(row))
    });

    // Desempenho da Equipe
    doc.addPage();
    doc.setFontSize(12);
    doc.text('Desempenho da Equipe', 14, 15);
    (doc as any).autoTable({
      startY: 20,
      head: [['Membro', 'Demandas Concluídas', 'Total de Demandas', 'Tarefas Atrasadas', 'Taxa de Conclusão']],
      body: teamPerformanceRows.map(row => Object.values(row))
    });

    // Redes Sociais
    doc.addPage();
    doc.setFontSize(12);
    doc.text('Análise de Redes Sociais', 14, 15);
    (doc as any).autoTable({
      startY: 20,
      head: [['Plataforma', 'Seguidores', 'Engajamento', 'Alcance', 'Sentimento +', 'Sentimento =', 'Sentimento -']],
      body: socialMediaRows.map(row => Object.values(row))
    });

    // Redes Sociais do Político
    if (reportData.politicianSocialMedia && reportData.politicianSocialMedia.length > 0) {
      doc.addPage();
      doc.setFontSize(12);
      doc.text('Redes Sociais do Político', 14, 15);
      (doc as any).autoTable({
        startY: 20,
        head: [['Plataforma', 'Usuário', 'URL']],
        body: preparePoliticianSocialMediaRows(reportData.politicianSocialMedia).map(row => Object.values(row))
      });
    }

    // Salvar o PDF
    doc.save('relatorio-promandato.pdf');
    console.log('PDF exportado com sucesso');
  } catch (error) {
    console.error('Erro durante a exportação do PDF:', error);
    throw error; // Re-throw para tratamento no componente
  }
};

// Exportar para Excel (usando exceljs)
export const exportToExcel = async (reportData: ReportData) => {
  const { demandStatusRows, teamPerformanceRows, socialMediaRows } = prepareDataForExport(reportData);
  const workbook = new ExcelJS.Workbook();

  // Status das Demandas
  const wsStatus = workbook.addWorksheet('Status das Demandas');
  wsStatus.columns = Object.keys(demandStatusRows[0] || {}).map(key => ({ header: key, key }));
  demandStatusRows.forEach(row => wsStatus.addRow(row));

  // Desempenho da Equipe
  const wsTeam = workbook.addWorksheet('Desempenho da Equipe');
  wsTeam.columns = Object.keys(teamPerformanceRows[0] || {}).map(key => ({ header: key, key }));
  teamPerformanceRows.forEach(row => wsTeam.addRow(row));

  // Redes Sociais
  const wsSocial = workbook.addWorksheet('Redes Sociais');
  wsSocial.columns = Object.keys(socialMediaRows[0] || {}).map(key => ({ header: key, key }));
  socialMediaRows.forEach(row => wsSocial.addRow(row));

  // Redes Sociais do Político
  if (reportData.politicianSocialMedia && reportData.politicianSocialMedia.length > 0) {
    const wsPoliticianSocial = workbook.addWorksheet('Redes Sociais Político');
    const rows = preparePoliticianSocialMediaRows(reportData.politicianSocialMedia);
    wsPoliticianSocial.columns = Object.keys(rows[0] || {}).map(key => ({ header: key, key }));
    rows.forEach(row => wsPoliticianSocial.addRow(row));
  }

  // Gerar arquivo e baixar
  const buffer = await workbook.xlsx.writeBuffer();
  const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'relatorio-promandato.xlsx';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  window.URL.revokeObjectURL(url);
};

// Exemplo de função para salvar redes sociais no banco de dados (ajuste conforme seu backend)
export const savePoliticianSocialMedia = async (politicianId: string, socials: PoliticianSocialMedia[]) => {
  // Aqui você faria uma chamada para sua API/backend
  // Exemplo (ajuste para seu client HTTP):
  // await api.post(`/politicians/${politicianId}/social-media`, socials);
  // Por enquanto, apenas um placeholder:
  console.log('Salvar redes sociais no banco:', politicianId, socials);
};
