#!/bin/bash

# ===========================================
# DEPLOY FRONTEND PARA FIREBASE HOSTING
# ProMandato Frontend
# ===========================================

set -e

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# Configurações
PROJECT_ID="promandato-9a4cf"
SITE_ID="promandato-9a4cf"

echo "🚀 Deploy do ProMandato Frontend para Firebase Hosting"
echo "📋 Projeto: $PROJECT_ID"
echo "🌐 Site: $SITE_ID"
echo ""

# Verificar se firebase-tools está instalado
if ! command -v firebase &> /dev/null; then
    log_error "Firebase CLI não está instalado"
    echo "Instale com: npm install -g firebase-tools"
    exit 1
fi

# Verificar se está logado no Firebase
if ! firebase projects:list &> /dev/null; then
    log_error "Não está logado no Firebase"
    echo "Execute: firebase login"
    exit 1
fi

# Ir para o diretório do frontend
cd frontend

# Verificar se package.json existe
if [ ! -f "package.json" ]; then
    log_error "package.json não encontrado no diretório frontend"
    exit 1
fi

# Instalar dependências
log_info "Instalando dependências..."
npm ci

# Verificar se arquivo de ambiente de produção existe
if [ ! -f ".env.production" ]; then
    log_warning "Arquivo .env.production não encontrado"
    log_info "Criando arquivo de exemplo..."
    
    cat > .env.production << 'EOF'
VITE_API_URL=https://promandato-backend-517140455601.southamerica-east1.run.app/api
VITE_STRIPE_PUBLIC_KEY=pk_live_sua_chave_publica_stripe
VITE_FIREBASE_API_KEY=AIzaSyB04qDOcU5jdZAYy-yaYQRUrrYQ_l49vcY
VITE_FIREBASE_AUTH_DOMAIN=promandato-9a4cf.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=promandato-9a4cf
VITE_FIREBASE_STORAGE_BUCKET=promandato-9a4cf.firebasestorage.app
VITE_FIREBASE_MESSAGING_SENDER_ID=517140455601
VITE_FIREBASE_APP_ID=1:517140455601:web:fa2eb0ec2f88b506594290
VITE_FIREBASE_MEASUREMENT_ID=G-MXYM2GKJS5
VITE_APP_ENVIRONMENT=production
EOF
    
    log_error "Configure as variáveis em frontend/.env.production antes de continuar"
    exit 1
fi

# Build do projeto
log_info "Fazendo build do projeto..."
npm run build

# Verificar se build foi criado
if [ ! -d "dist" ]; then
    log_error "Diretório dist não foi criado - build falhou"
    exit 1
fi

# Configurar Firebase
log_info "Configurando Firebase..."
firebase use $PROJECT_ID

# Verificar se firebase.json existe
if [ ! -f "../firebase.json" ]; then
    log_info "Criando firebase.json..."
    
    cat > ../firebase.json << 'EOF'
{
  "hosting": {
    "public": "frontend/dist",
    "ignore": [
      "firebase.json",
      "**/.*",
      "**/node_modules/**"
    ],
    "rewrites": [
      {
        "source": "**",
        "destination": "/index.html"
      }
    ],
    "headers": [
      {
        "source": "**/*.@(js|css)",
        "headers": [
          {
            "key": "Cache-Control",
            "value": "max-age=31536000"
          }
        ]
      },
      {
        "source": "**",
        "headers": [
          {
            "key": "X-Frame-Options",
            "value": "DENY"
          },
          {
            "key": "X-Content-Type-Options",
            "value": "nosniff"
          },
          {
            "key": "X-XSS-Protection",
            "value": "1; mode=block"
          },
          {
            "key": "Strict-Transport-Security",
            "value": "max-age=31536000; includeSubDomains"
          }
        ]
      }
    ]
  }
}
EOF
fi

# Deploy
log_info "Fazendo deploy para Firebase Hosting..."
cd ..
firebase deploy --only hosting

# Obter URL do site
SITE_URL="https://${SITE_ID}.web.app"

log_success "Deploy concluído!"
echo ""
echo "🌐 URL do site: $SITE_URL"
echo "🔍 Teste o site: $SITE_URL"
echo ""

# Testar se o site está acessível
log_info "Testando se o site está acessível..."
if curl -f "$SITE_URL" > /dev/null 2>&1; then
    log_success "Site está acessível!"
else
    log_warning "Site pode não estar acessível ainda - aguarde alguns minutos"
fi

echo ""
log_success "Deploy do frontend concluído com sucesso!"
echo ""
echo "📋 PRÓXIMOS PASSOS:"
echo "1. Teste todas as funcionalidades do site"
echo "2. Verifique se a comunicação com o backend está funcionando"
echo "3. Teste o fluxo de pagamento com Stripe"
echo "4. Configure domínio customizado (se necessário)"
echo "5. Configure SSL/TLS (se usando domínio customizado)"
echo ""

# Mostrar informações do projeto
log_info "Informações do projeto:"
firebase projects:list | grep $PROJECT_ID || log_warning "Projeto não encontrado na lista"