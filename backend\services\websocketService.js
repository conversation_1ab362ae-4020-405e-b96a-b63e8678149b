/**
 * Serviço de WebSocket para notificações em tempo real
 */

import { Server } from 'socket.io';

class WebSocketService {
  constructor() {
    this.io = null;
    this.connectedUsers = new Map(); // userId -> socketId
    this.userSockets = new Map(); // socketId -> userId
  }

  /**
   * Inicializar WebSocket server
   */
  initialize(server) {
    this.io = new Server(server, {
      cors: {
        origin: [
          "http://localhost:3000",
          "http://localhost:3001", 
          "http://localhost:5173",
          "https://promandato.web.app",
          "https://promandato.firebaseapp.com",
          "https://app.promandato.com.br"
        ],
        methods: ["GET", "POST"],
        credentials: true
      },
      transports: ['websocket', 'polling']
    });

    this.setupEventHandlers();
    console.log('🔌 WebSocket service initialized');
  }

  /**
   * Configurar event handlers
   */
  setupEventHandlers() {
    this.io.on('connection', (socket) => {
      console.log(`🔗 Cliente conectado: ${socket.id}`);

      // Autenticação do usuário
      socket.on('authenticate', (data) => {
        const { userId, token } = data;
        
        // TODO: Validar token JWT aqui
        if (userId) {
          this.connectedUsers.set(userId, socket.id);
          this.userSockets.set(socket.id, userId);
          
          socket.join(`user_${userId}`);
          console.log(`✅ Usuário ${userId} autenticado no socket ${socket.id}`);
          
          socket.emit('authenticated', { success: true });
        } else {
          socket.emit('authentication_error', { error: 'User ID required' });
        }
      });

      // Marcar notificação como lida
      socket.on('mark_notification_read', (data) => {
        const { notificationId } = data;
        const userId = this.userSockets.get(socket.id);
        
        if (userId) {
          // Broadcast para outros dispositivos do mesmo usuário
          socket.to(`user_${userId}`).emit('notification_read', { notificationId });
        }
      });

      // Solicitar contagem de não lidas
      socket.on('request_unread_count', () => {
        const userId = this.userSockets.get(socket.id);
        if (userId) {
          // Aqui você pode buscar a contagem real do banco
          // Por enquanto, emitir um evento para que o cliente faça a requisição HTTP
          socket.emit('refresh_unread_count');
        }
      });

      // Desconexão
      socket.on('disconnect', () => {
        const userId = this.userSockets.get(socket.id);
        if (userId) {
          this.connectedUsers.delete(userId);
          this.userSockets.delete(socket.id);
          console.log(`❌ Usuário ${userId} desconectado (socket ${socket.id})`);
        } else {
          console.log(`❌ Cliente desconectado: ${socket.id}`);
        }
      });

      // Ping/Pong para manter conexão viva
      socket.on('ping', () => {
        socket.emit('pong');
      });
    });
  }

  /**
   * Enviar notificação em tempo real para um usuário
   */
  sendNotificationToUser(userId, notification) {
    if (!this.io) {
      console.warn('WebSocket não inicializado');
      return false;
    }

    const socketId = this.connectedUsers.get(userId);
    
    if (socketId) {
      // Enviar para o socket específico do usuário
      this.io.to(`user_${userId}`).emit('new_notification', {
        notification,
        timestamp: new Date().toISOString()
      });
      
      console.log(`📨 Notificação enviada via WebSocket para usuário ${userId}`);
      return true;
    } else {
      console.log(`📭 Usuário ${userId} não está conectado via WebSocket`);
      return false;
    }
  }

  /**
   * Enviar notificação para múltiplos usuários
   */
  sendNotificationToUsers(userIds, notification) {
    if (!this.io) {
      console.warn('WebSocket não inicializado');
      return;
    }

    let sentCount = 0;
    userIds.forEach(userId => {
      if (this.sendNotificationToUser(userId, notification)) {
        sentCount++;
      }
    });

    console.log(`📨 Notificação enviada via WebSocket para ${sentCount}/${userIds.length} usuários`);
    return sentCount;
  }

  /**
   * Broadcast para todos os usuários conectados
   */
  broadcastNotification(notification) {
    if (!this.io) {
      console.warn('WebSocket não inicializado');
      return;
    }

    this.io.emit('new_notification', {
      notification,
      timestamp: new Date().toISOString()
    });

    console.log(`📢 Notificação broadcast via WebSocket para todos os usuários conectados`);
  }

  /**
   * Enviar atualização de contagem de não lidas
   */
  sendUnreadCountUpdate(userId, count) {
    if (!this.io) {
      console.warn('WebSocket não inicializado');
      return;
    }

    const socketId = this.connectedUsers.get(userId);
    
    if (socketId) {
      this.io.to(`user_${userId}`).emit('unread_count_update', {
        count,
        timestamp: new Date().toISOString()
      });
      
      console.log(`🔢 Contagem de não lidas atualizada via WebSocket para usuário ${userId}: ${count}`);
    }
  }

  /**
   * Obter estatísticas de conexões
   */
  getStats() {
    return {
      connectedUsers: this.connectedUsers.size,
      totalSockets: this.userSockets.size,
      users: Array.from(this.connectedUsers.keys())
    };
  }

  /**
   * Verificar se um usuário está conectado
   */
  isUserConnected(userId) {
    return this.connectedUsers.has(userId);
  }

  /**
   * Desconectar um usuário específico
   */
  disconnectUser(userId) {
    const socketId = this.connectedUsers.get(userId);
    if (socketId) {
      const socket = this.io.sockets.sockets.get(socketId);
      if (socket) {
        socket.disconnect(true);
        console.log(`🚫 Usuário ${userId} desconectado forçadamente`);
      }
    }
  }
}

// Instância singleton
const websocketService = new WebSocketService();

export default websocketService;
