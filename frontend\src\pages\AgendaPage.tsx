
import React, { useState, useEffect, useCallback } from 'react';
import { AgendaEvent } from '../types';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';
import { useAuth } from '../hooks/useAuth';
import { getAgendaEvents, addAgendaEvent, updateAgendaEvent, deleteAgendaEvent } from '../services/firebaseService';
import { CalendarView } from '../components/calendar/CalendarView';
import '../styles/calendar.css';

const AgendaPage: React.FC = () => {
  const [events, setEvents] = useState<AgendaEvent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const { currentUser } = useAuth();

  const fetchEvents = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const fetchedEvents = await getAgendaEvents();
      setEvents(fetchedEvents);
    } catch (err) {
      console.error("Failed to fetch agenda events:", err);
      setError("Falha ao carregar eventos da agenda. Tente novamente.");
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchEvents();
  }, [fetchEvents]);

  const handleSaveEvent = useCallback(async (eventData: Partial<AgendaEvent>) => {
    if (!currentUser) {
      setError("Usuário não autenticado.");
      return;
    }

    setIsLoading(true);
    setError(null);
    try {
      if (eventData.id) {
        await updateAgendaEvent(eventData.id, eventData as Omit<AgendaEvent, 'id' | 'createdAt' | 'createdBy' | 'updatedAt'>);
      } else {
        await addAgendaEvent(eventData as Omit<AgendaEvent, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>, currentUser.id);
      }
      await fetchEvents();
    } catch (err) {
      console.error("Failed to save event:", err);
      setError("Falha ao salvar evento. Tente novamente.");
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [currentUser, fetchEvents]);
  
  const handleDeleteEvent = useCallback(async (eventId: string) => {
    setIsLoading(true);
    setError(null);
    try {
      await deleteAgendaEvent(eventId);
      await fetchEvents();
    } catch (err) {
      console.error("Failed to delete event:", err);
      setError("Falha ao excluir evento. Tente novamente.");
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [fetchEvents]);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-neutral-dark dark:text-neutral-light">Agenda</h1>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500 dark:text-gray-400">
            Visualização completa com calendário interativo
          </span>
        </div>
      </div>

      {error && (
        <div className="text-red-500 dark:text-red-400 bg-red-100 dark:bg-red-900 dark:bg-opacity-30 p-3 rounded-md">
          {error}
        </div>
      )}

      {isLoading ? (
        <div className="flex justify-center items-center py-20">
          <LoadingSpinner size="lg" />
        </div>
      ) : (
        <CalendarView
          events={events}
          onSaveEvent={handleSaveEvent}
          onDeleteEvent={handleDeleteEvent}
          isLoading={isLoading}
        />
      )}
    </div>
  );
};

export default AgendaPage;