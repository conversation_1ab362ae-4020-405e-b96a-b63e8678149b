<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Frontend Issues - ProMandato</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .fix-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
        .step {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Fix Frontend Issues - ProMandato</h1>
        <p>Esta página ajuda a resolver problemas comuns do frontend, incluindo Service Worker e conexões de API.</p>
        
        <div class="fix-result info">
            <strong>Ambiente detectado:</strong> <span id="environment"></span><br>
            <strong>URL atual:</strong> <span id="currentUrl"></span><br>
            <strong>API URL:</strong> <span id="apiUrl"></span>
        </div>
        
        <div class="step">
            <h2>🧹 Passo 1: Limpar Service Worker</h2>
            <p>Remove cache antigo e registros do Service Worker que podem estar causando problemas.</p>
            <button onclick="clearServiceWorker()" id="clearSwBtn">Limpar Service Worker</button>
        </div>
        
        <div class="step">
            <h2>🌐 Passo 2: Testar Conectividade da API</h2>
            <p>Verifica se a API está respondendo corretamente.</p>
            <button onclick="testApiConnectivity()" id="testApiBtn">Testar API</button>
        </div>
        
        <div class="step">
            <h2>🔄 Passo 3: Recarregar Aplicação</h2>
            <p>Força um reload completo da aplicação sem cache.</p>
            <button onclick="forceReload()" id="reloadBtn">Recarregar Aplicação</button>
        </div>
        
        <div class="step">
            <h2>📊 Passo 4: Verificar Status</h2>
            <p>Verifica o status geral da aplicação após as correções.</p>
            <button onclick="checkStatus()" id="statusBtn">Verificar Status</button>
        </div>
        
        <div id="results"></div>
        
        <div class="step">
            <h2>🚀 Executar Todas as Correções</h2>
            <button onclick="runAllFixes()" id="runAllBtn" style="background-color: #28a745;">Executar Tudo</button>
            <button onclick="clearResults()" style="background-color: #6c757d;">Limpar Resultados</button>
        </div>
    </div>

    <script>
        // Detectar ambiente e URLs
        const isLocal = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
        const environment = isLocal ? 'development' : 'production';
        
        const getApiUrl = () => {
            if (isLocal) {
                return 'http://localhost:3002';
            }
            
            if (window.location.hostname.includes('firebaseapp.com') ||
                window.location.hostname.includes('web.app') ||
                window.location.hostname.includes('promandato.com')) {
                return 'https://promandato-backend-517140455601.southamerica-east1.run.app';
            }
            
            return 'https://promandato-backend-517140455601.southamerica-east1.run.app';
        };
        
        // Atualizar informações da página
        document.getElementById('environment').textContent = environment;
        document.getElementById('currentUrl').textContent = window.location.href;
        document.getElementById('apiUrl').textContent = getApiUrl();
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `fix-result ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function clearServiceWorker() {
            addResult('🧹 Iniciando limpeza do Service Worker...', 'info');
            
            try {
                // 1. Desregistrar Service Workers
                if ('serviceWorker' in navigator) {
                    const registrations = await navigator.serviceWorker.getRegistrations();
                    
                    for (let registration of registrations) {
                        addResult(`🗑️ Desregistrando SW: ${registration.scope}`, 'info');
                        await registration.unregister();
                    }
                    
                    if (registrations.length > 0) {
                        addResult(`✅ ${registrations.length} Service Worker(s) removido(s)`, 'success');
                    } else {
                        addResult('ℹ️ Nenhum Service Worker encontrado', 'info');
                    }
                }
                
                // 2. Limpar todos os caches
                if ('caches' in window) {
                    const cacheNames = await caches.keys();
                    
                    for (const cacheName of cacheNames) {
                        addResult(`🗑️ Removendo cache: ${cacheName}`, 'info');
                        await caches.delete(cacheName);
                    }
                    
                    if (cacheNames.length > 0) {
                        addResult(`✅ ${cacheNames.length} cache(s) removido(s)`, 'success');
                    } else {
                        addResult('ℹ️ Nenhum cache encontrado', 'info');
                    }
                }
                
                // 3. Limpar localStorage e sessionStorage
                localStorage.clear();
                sessionStorage.clear();
                addResult('✅ Storage local limpo', 'success');
                
                addResult('🎉 Service Worker limpo com sucesso!', 'success');
                
            } catch (error) {
                addResult(`❌ Erro ao limpar Service Worker: ${error.message}`, 'error');
            }
        }
        
        async function testApiConnectivity() {
            const apiUrl = getApiUrl();
            addResult(`🌐 Testando conectividade com: ${apiUrl}`, 'info');
            
            try {
                // Teste 1: Health Check
                addResult('📡 Testando health check...', 'info');
                const healthResponse = await fetch(`${apiUrl}/api/health`, {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                if (healthResponse.ok) {
                    const healthData = await healthResponse.json();
                    addResult(`✅ Health check OK: ${healthData.message}`, 'success');
                } else {
                    addResult(`⚠️ Health check retornou: ${healthResponse.status}`, 'warning');
                }
                
                // Teste 2: CORS Debug
                addResult('🔍 Testando CORS debug...', 'info');
                const corsResponse = await fetch(`${apiUrl}/api/cors-debug`, {
                    method: 'GET',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                if (corsResponse.ok) {
                    const corsData = await corsResponse.json();
                    addResult('✅ CORS debug OK', 'success');
                    
                    if (corsData.data && corsData.data.isOriginAllowed) {
                        addResult('✅ Origem permitida no CORS', 'success');
                    } else {
                        addResult('❌ Origem NÃO permitida no CORS', 'error');
                        addResult(`<pre>${JSON.stringify(corsData.data, null, 2)}</pre>`, 'info');
                    }
                } else {
                    addResult(`⚠️ CORS debug retornou: ${corsResponse.status}`, 'warning');
                }
                
            } catch (error) {
                addResult(`❌ Erro de conectividade: ${error.message}`, 'error');
                
                if (error.message.includes('CORS')) {
                    addResult('🔧 Problema de CORS detectado! Verifique a configuração do servidor.', 'error');
                } else if (error.message.includes('Failed to fetch')) {
                    addResult('🔧 Servidor pode estar offline ou inacessível.', 'error');
                }
            }
        }
        
        function forceReload() {
            addResult('🔄 Forçando reload da aplicação...', 'info');
            
            // Aguardar um pouco para mostrar a mensagem
            setTimeout(() => {
                // Reload forçado sem cache
                window.location.reload(true);
            }, 1000);
        }
        
        async function checkStatus() {
            addResult('📊 Verificando status da aplicação...', 'info');
            
            // Verificar Service Worker
            if ('serviceWorker' in navigator) {
                const registrations = await navigator.serviceWorker.getRegistrations();
                if (registrations.length === 0) {
                    addResult('✅ Nenhum Service Worker ativo', 'success');
                } else {
                    addResult(`⚠️ ${registrations.length} Service Worker(s) ainda ativo(s)`, 'warning');
                }
            }
            
            // Verificar caches
            if ('caches' in window) {
                const cacheNames = await caches.keys();
                if (cacheNames.length === 0) {
                    addResult('✅ Nenhum cache ativo', 'success');
                } else {
                    addResult(`⚠️ ${cacheNames.length} cache(s) ainda ativo(s): ${cacheNames.join(', ')}`, 'warning');
                }
            }
            
            // Verificar localStorage
            const localStorageKeys = Object.keys(localStorage);
            if (localStorageKeys.length === 0) {
                addResult('✅ localStorage limpo', 'success');
            } else {
                addResult(`ℹ️ localStorage contém: ${localStorageKeys.join(', ')}`, 'info');
            }
            
            addResult('📊 Verificação de status concluída', 'info');
        }
        
        async function runAllFixes() {
            addResult('🚀 Executando todas as correções...', 'info');
            
            // Desabilitar botão durante execução
            const btn = document.getElementById('runAllBtn');
            btn.disabled = true;
            btn.textContent = 'Executando...';
            
            try {
                await clearServiceWorker();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                await testApiConnectivity();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                await checkStatus();
                
                addResult('🎉 Todas as correções executadas! Recarregando em 3 segundos...', 'success');
                
                setTimeout(() => {
                    forceReload();
                }, 3000);
                
            } catch (error) {
                addResult(`❌ Erro durante execução: ${error.message}`, 'error');
            } finally {
                btn.disabled = false;
                btn.textContent = 'Executar Tudo';
            }
        }
        
        // Executar verificação inicial
        window.onload = function() {
            addResult('🚀 Página de correções carregada', 'info');
            addResult(`🌍 Ambiente: ${environment}`, 'info');
            addResult(`🔗 API: ${getApiUrl()}`, 'info');
        };
    </script>
</body>
</html>
