/**
 * Serviço de WebSocket para notificações em tempo real
 */

import { io, Socket } from 'socket.io-client';
import { Notification } from './notificationService';

interface WebSocketEvents {
  new_notification: (data: { notification: Notification; timestamp: string }) => void;
  unread_count_update: (data: { count: number; timestamp: string }) => void;
  notification_read: (data: { notificationId: string }) => void;
  authenticated: (data: { success: boolean }) => void;
  authentication_error: (data: { error: string }) => void;
  refresh_unread_count: () => void;
  pong: () => void;
}

class WebSocketService {
  private socket: Socket | null = null;
  private isConnected = false;
  private isAuthenticated = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private pingInterval: NodeJS.Timeout | null = null;
  
  // Event listeners
  private onNewNotificationCallbacks: Array<(notification: Notification) => void> = [];
  private onUnreadCountUpdateCallbacks: Array<(count: number) => void> = [];
  private onNotificationReadCallbacks: Array<(notificationId: string) => void> = [];
  private onRefreshUnreadCountCallbacks: Array<() => void> = [];

  /**
   * Conectar ao WebSocket
   */
  connect(userId: string, token?: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      try {
        const serverUrl = this.getServerUrl();
        
        this.socket = io(serverUrl, {
          transports: ['websocket', 'polling'],
          timeout: 10000,
          forceNew: true
        });

        this.setupEventHandlers();

        this.socket.on('connect', () => {
          console.log('🔗 WebSocket conectado');
          this.isConnected = true;
          this.reconnectAttempts = 0;
          
          // Autenticar usuário
          this.authenticate(userId, token);
        });

        this.socket.on('authenticated', (data) => {
          if (data.success) {
            console.log('✅ WebSocket autenticado');
            this.isAuthenticated = true;
            this.startPingInterval();
            resolve(true);
          } else {
            console.error('❌ Falha na autenticação WebSocket');
            reject(new Error('Authentication failed'));
          }
        });

        this.socket.on('authentication_error', (data) => {
          console.error('❌ Erro de autenticação WebSocket:', data.error);
          reject(new Error(data.error));
        });

        this.socket.on('connect_error', (error) => {
          console.error('❌ Erro de conexão WebSocket:', error);
          this.handleReconnect();
          reject(error);
        });

      } catch (error) {
        console.error('❌ Erro ao conectar WebSocket:', error);
        reject(error);
      }
    });
  }

  /**
   * Obter URL do servidor baseado no ambiente
   */
  private getServerUrl(): string {
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
      return 'http://localhost:3002';
    }
    
    // Produção
    return 'https://promandato-backend-517140455601.southamerica-east1.run.app';
  }

  /**
   * Configurar event handlers
   */
  private setupEventHandlers() {
    if (!this.socket) return;

    this.socket.on('disconnect', (reason) => {
      console.log('❌ WebSocket desconectado:', reason);
      this.isConnected = false;
      this.isAuthenticated = false;
      this.stopPingInterval();
      
      if (reason === 'io server disconnect') {
        // Servidor desconectou, tentar reconectar
        this.handleReconnect();
      }
    });

    this.socket.on('new_notification', (data) => {
      console.log('📨 Nova notificação via WebSocket:', data.notification);
      this.onNewNotificationCallbacks.forEach(callback => {
        try {
          callback(data.notification);
        } catch (error) {
          console.error('Erro no callback de nova notificação:', error);
        }
      });
    });

    this.socket.on('unread_count_update', (data) => {
      console.log('🔢 Contagem de não lidas atualizada via WebSocket:', data.count);
      this.onUnreadCountUpdateCallbacks.forEach(callback => {
        try {
          callback(data.count);
        } catch (error) {
          console.error('Erro no callback de contagem de não lidas:', error);
        }
      });
    });

    this.socket.on('notification_read', (data) => {
      console.log('👁️ Notificação marcada como lida via WebSocket:', data.notificationId);
      this.onNotificationReadCallbacks.forEach(callback => {
        try {
          callback(data.notificationId);
        } catch (error) {
          console.error('Erro no callback de notificação lida:', error);
        }
      });
    });

    this.socket.on('refresh_unread_count', () => {
      console.log('🔄 Solicitação para atualizar contagem de não lidas');
      this.onRefreshUnreadCountCallbacks.forEach(callback => {
        try {
          callback();
        } catch (error) {
          console.error('Erro no callback de refresh de contagem:', error);
        }
      });
    });

    this.socket.on('pong', () => {
      // Conexão está viva
    });
  }

  /**
   * Autenticar usuário
   */
  private authenticate(userId: string, token?: string) {
    if (this.socket && this.isConnected) {
      this.socket.emit('authenticate', { userId, token });
    }
  }

  /**
   * Iniciar ping interval para manter conexão viva
   */
  private startPingInterval() {
    this.stopPingInterval();
    this.pingInterval = setInterval(() => {
      if (this.socket && this.isConnected) {
        this.socket.emit('ping');
      }
    }, 30000); // Ping a cada 30 segundos
  }

  /**
   * Parar ping interval
   */
  private stopPingInterval() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }

  /**
   * Lidar com reconexão
   */
  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
      
      console.log(`🔄 Tentando reconectar WebSocket em ${delay}ms (tentativa ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        if (this.socket) {
          this.socket.connect();
        }
      }, delay);
    } else {
      console.error('❌ Máximo de tentativas de reconexão atingido');
    }
  }

  /**
   * Marcar notificação como lida
   */
  markNotificationAsRead(notificationId: string) {
    if (this.socket && this.isAuthenticated) {
      this.socket.emit('mark_notification_read', { notificationId });
    }
  }

  /**
   * Solicitar contagem de não lidas
   */
  requestUnreadCount() {
    if (this.socket && this.isAuthenticated) {
      this.socket.emit('request_unread_count');
    }
  }

  /**
   * Registrar callback para novas notificações
   */
  onNewNotification(callback: (notification: Notification) => void) {
    this.onNewNotificationCallbacks.push(callback);
    
    // Retornar função para remover o callback
    return () => {
      const index = this.onNewNotificationCallbacks.indexOf(callback);
      if (index > -1) {
        this.onNewNotificationCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Registrar callback para atualização de contagem
   */
  onUnreadCountUpdate(callback: (count: number) => void) {
    this.onUnreadCountUpdateCallbacks.push(callback);
    
    return () => {
      const index = this.onUnreadCountUpdateCallbacks.indexOf(callback);
      if (index > -1) {
        this.onUnreadCountUpdateCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Registrar callback para notificação lida
   */
  onNotificationRead(callback: (notificationId: string) => void) {
    this.onNotificationReadCallbacks.push(callback);
    
    return () => {
      const index = this.onNotificationReadCallbacks.indexOf(callback);
      if (index > -1) {
        this.onNotificationReadCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Registrar callback para refresh de contagem
   */
  onRefreshUnreadCount(callback: () => void) {
    this.onRefreshUnreadCountCallbacks.push(callback);
    
    return () => {
      const index = this.onRefreshUnreadCountCallbacks.indexOf(callback);
      if (index > -1) {
        this.onRefreshUnreadCountCallbacks.splice(index, 1);
      }
    };
  }

  /**
   * Desconectar WebSocket
   */
  disconnect() {
    this.stopPingInterval();
    
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    
    this.isConnected = false;
    this.isAuthenticated = false;
    this.reconnectAttempts = 0;
    
    // Limpar callbacks
    this.onNewNotificationCallbacks = [];
    this.onUnreadCountUpdateCallbacks = [];
    this.onNotificationReadCallbacks = [];
    this.onRefreshUnreadCountCallbacks = [];
    
    console.log('🔌 WebSocket desconectado');
  }

  /**
   * Verificar se está conectado e autenticado
   */
  isReady(): boolean {
    return this.isConnected && this.isAuthenticated;
  }

  /**
   * Obter status da conexão
   */
  getStatus() {
    return {
      connected: this.isConnected,
      authenticated: this.isAuthenticated,
      reconnectAttempts: this.reconnectAttempts
    };
  }
}

// Instância singleton
const websocketService = new WebSocketService();

export default websocketService;
