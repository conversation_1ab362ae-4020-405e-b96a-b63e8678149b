// Bundle optimization utilities

/**
 * Preload critical resources
 */
export const preloadCriticalResources = () => {
  // Check if critical CSS exists before preloading
  fetch('/assets/critical.css', { method: 'HEAD' })
    .then(response => {
      if (response.ok) {
        const criticalCSS = document.createElement('link');
        criticalCSS.rel = 'preload';
        criticalCSS.as = 'style';
        criticalCSS.href = '/assets/critical.css';
        document.head.appendChild(criticalCSS);
      }
    })
    .catch(() => {
      // Critical CSS doesn't exist, skip preloading
      console.log('Critical CSS not found, skipping preload');
    });
};

/**
 * Lazy load non-critical resources
 */
export const lazyLoadResources = () => {
  // Lazy load non-critical CSS
  const nonCriticalCSS = document.createElement('link');
  nonCriticalCSS.rel = 'stylesheet';
  nonCriticalCSS.href = '/assets/non-critical.css';
  nonCriticalCSS.media = 'print';
  nonCriticalCSS.onload = () => {
    nonCriticalCSS.media = 'all';
  };
  document.head.appendChild(nonCriticalCSS);
};

/**
 * Optimize images with lazy loading
 */
export const optimizeImages = () => {
  const images = document.querySelectorAll('img[data-src]');
  
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          img.src = img.dataset.src || '';
          img.classList.remove('lazy');
          imageObserver.unobserve(img);
        }
      });
    });

    images.forEach((img) => imageObserver.observe(img));
  } else {
    // Fallback for browsers without IntersectionObserver
    images.forEach((img) => {
      const imgElement = img as HTMLImageElement;
      imgElement.src = imgElement.dataset.src || '';
    });
  }
};

/**
 * Service Worker registration for caching
 */
export const registerServiceWorker = async () => {
  if ('serviceWorker' in navigator) {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js');
      console.log('Service Worker registered successfully:', registration);
    } catch (error) {
      console.log('Service Worker registration failed:', error);
    }
  }
};

/**
 * Performance monitoring
 */
export const monitorPerformance = () => {
  if ('performance' in window) {
    window.addEventListener('load', () => {
      const perfData = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      console.log('Performance Metrics:', {
        'DNS Lookup': perfData.domainLookupEnd - perfData.domainLookupStart,
        'TCP Connection': perfData.connectEnd - perfData.connectStart,
        'Request': perfData.responseStart - perfData.requestStart,
        'Response': perfData.responseEnd - perfData.responseStart,
        'DOM Processing': perfData.domContentLoadedEventStart - perfData.responseEnd,
        'Total Load Time': perfData.loadEventEnd - perfData.navigationStart
      });
    });
  }
};
