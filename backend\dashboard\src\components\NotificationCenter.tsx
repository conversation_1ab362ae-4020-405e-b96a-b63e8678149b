import React, { useState, useEffect, useRef } from 'react';
import { Bell, X, Check, Trash2 } from 'lucide-react';

interface NotificationItem {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  read: boolean;
  createdAt: string;
  readAt?: string;
  actionUrl?: string;
  actionLabel?: string;
}

interface NotificationCenterProps {
  darkMode: boolean;
}

// Mock notification service
const notificationService = {
  getNotifications: async (): Promise<NotificationItem[]> => {
    // Simular delay de API
    await new Promise(resolve => setTimeout(resolve, 500));

    return [
      {
        id: '1',
        title: 'Nova demanda urgente',
        message: 'Problema crítico reportado no sistema',
        type: 'error' as const,
        read: false,
        createdAt: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 min atrás
        actionUrl: '#',
        actionLabel: 'Ver detalhes'
      },
      {
        id: '2',
        title: 'Sistema atualizado',
        message: 'Dashboard funcionando corretamente',
        type: 'success' as const,
        read: false,
        createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2h atrás
      },
      {
        id: '3',
        title: 'Reunião em 1 hora',
        message: 'Reunião com equipe administrativa',
        type: 'warning' as const,
        read: true,
        createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4h atrás
        readAt: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
      }
    ];
  },

  getUnreadCount: async (): Promise<number> => {
    const notifications = await notificationService.getNotifications();
    return notifications.filter(n => !n.read).length;
  },

  markAsRead: async (notificationId: string): Promise<boolean> => {
    console.log('Marcando como lida:', notificationId);
    await new Promise(resolve => setTimeout(resolve, 200));
    return true;
  },

  markAllAsRead: async (): Promise<boolean> => {
    console.log('Marcando todas como lidas');
    await new Promise(resolve => setTimeout(resolve, 300));
    return true;
  },

  deleteNotification: async (notificationId: string): Promise<boolean> => {
    console.log('Deletando notificação:', notificationId);
    await new Promise(resolve => setTimeout(resolve, 200));
    return true;
  },

  getTypeIcon: (type: string): string => {
    switch (type) {
      case 'error': return '🚨';
      case 'warning': return '⚠️';
      case 'success': return '✅';
      case 'info': return 'ℹ️';
      default: return '📢';
    }
  },

  formatRelativeTime: (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Agora';
    if (diffInMinutes < 60) return `${diffInMinutes}m atrás`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h atrás`;

    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d atrás`;
  }
};

const NotificationCenter: React.FC<NotificationCenterProps> = ({ darkMode }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState<NotificationItem[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Buscar notificações
  const fetchNotifications = async () => {
    console.log('🔄 Iniciando busca de notificações...');
    setLoading(true);
    try {
      const [notificationsData, unreadCountData] = await Promise.all([
        notificationService.getNotifications(),
        notificationService.getUnreadCount()
      ]);

      console.log('📋 Notificações recebidas:', notificationsData.length);
      console.log('🔔 Contagem não lidas:', unreadCountData);

      setNotifications(notificationsData);
      setUnreadCount(unreadCountData);
    } catch (error) {
      console.error('❌ Erro ao buscar notificações:', error);
    } finally {
      setLoading(false);
    }
  };

  // Carregar notificações ao montar o componente
  useEffect(() => {
    console.log('🚀 NotificationCenter montado!');
    fetchNotifications();

    // Atualizar a cada 30 segundos
    const interval = setInterval(fetchNotifications, 30000);
    return () => clearInterval(interval);
  }, []);

  // Fechar dropdown ao clicar fora
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Marcar como lida
  const handleMarkAsRead = async (notificationId: string) => {
    const success = await notificationService.markAsRead(notificationId);
    if (success) {
      setNotifications(prev =>
        prev.map(n =>
          n.id === notificationId ? { ...n, read: true, readAt: new Date().toISOString() } : n
        )
      );
      setUnreadCount(prev => Math.max(0, prev - 1));
    }
  };

  // Marcar todas como lidas
  const handleMarkAllAsRead = async () => {
    const success = await notificationService.markAllAsRead();
    if (success) {
      setNotifications(prev =>
        prev.map(n => ({ ...n, read: true, readAt: new Date().toISOString() }))
      );
      setUnreadCount(0);
    }
  };

  // Deletar notificação
  const handleDelete = async (notificationId: string) => {
    const success = await notificationService.deleteNotification(notificationId);
    if (success) {
      const notification = notifications.find(n => n.id === notificationId);
      setNotifications(prev => prev.filter(n => n.id !== notificationId));
      if (notification && !notification.read) {
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    }
  };

  // Clique na notificação
  const handleNotificationClick = (notification: NotificationItem) => {
    if (!notification.read) {
      handleMarkAsRead(notification.id);
    }

    if (notification.actionUrl) {
      window.open(notification.actionUrl, '_blank');
    }
  };

  return (
    <div ref={dropdownRef} className="relative">
      {/* Botão de notificações */}
      <button
        type="button"
        onClick={() => {
          console.log('🔔 Clique no sino! Estado atual:', isOpen);
          setIsOpen(!isOpen);
        }}
        className={`p-2 rounded-md relative ${
          darkMode
            ? 'text-gray-300 hover:text-white hover:bg-gray-700'
            : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
        }`}
      >
        <Bell className="h-5 w-5" />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
            {unreadCount > 9 ? '9+' : unreadCount}
          </span>
        )}
      </button>

      {/* Dropdown de notificações */}
      {isOpen && (
        <div className={`absolute right-0 mt-2 w-96 rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 z-50 ${
          darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        } border`}>
          {/* Header */}
          <div className={`px-4 py-3 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
            <div className="flex items-center justify-between">
              <h3 className={`text-lg font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Notificações
              </h3>
              <div className="flex items-center space-x-2">
                {unreadCount > 0 && (
                  <button
                    type="button"
                    onClick={handleMarkAllAsRead}
                    className={`text-sm px-2 py-1 rounded ${
                      darkMode
                        ? 'text-blue-400 hover:text-blue-300 hover:bg-gray-700'
                        : 'text-blue-600 hover:text-blue-800 hover:bg-blue-50'
                    }`}
                  >
                    Marcar todas como lidas
                  </button>
                )}
                <button
                  type="button"
                  onClick={() => setIsOpen(false)}
                  className={`p-1 rounded ${
                    darkMode
                      ? 'text-gray-400 hover:text-white hover:bg-gray-700'
                      : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>

          {/* Lista de notificações */}
          <div className="max-h-96 overflow-y-auto">
            {loading ? (
              <div className="px-4 py-6 text-center">
                <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  Carregando notificações...
                </div>
              </div>
            ) : notifications.length === 0 ? (
              <div className="px-4 py-6 text-center">
                <Bell className={`mx-auto h-12 w-12 ${darkMode ? 'text-gray-600' : 'text-gray-400'}`} />
                <p className={`mt-2 text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  Nenhuma notificação
                </p>
              </div>
            ) : (
              notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`px-4 py-3 border-b cursor-pointer transition-colors ${
                    darkMode ? 'border-gray-700 hover:bg-gray-700' : 'border-gray-200 hover:bg-gray-50'
                  } ${!notification.read ? (darkMode ? 'bg-blue-900/20' : 'bg-blue-50') : ''}`}
                  onClick={() => handleNotificationClick(notification)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3 flex-1">
                      <div className="text-lg">
                        {notificationService.getTypeIcon(notification.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className={`text-sm font-medium ${
                            !notification.read
                              ? (darkMode ? 'text-white' : 'text-gray-900')
                              : (darkMode ? 'text-gray-300' : 'text-gray-700')
                          }`}>
                            {notification.title}
                          </p>
                          {!notification.read && (
                            <div className="w-2 h-2 bg-blue-600 rounded-full flex-shrink-0"></div>
                          )}
                        </div>
                        <p className={`text-sm mt-1 ${
                          darkMode ? 'text-gray-400' : 'text-gray-600'
                        }`}>
                          {notification.message}
                        </p>
                        <div className="flex items-center justify-between mt-2">
                          <span className={`text-xs ${
                            darkMode ? 'text-gray-500' : 'text-gray-400'
                          }`}>
                            {notificationService.formatRelativeTime(notification.createdAt)}
                          </span>
                          {notification.actionLabel && (
                            <span className={`text-xs ${
                              darkMode ? 'text-blue-400' : 'text-blue-600'
                            }`}>
                              {notification.actionLabel}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1 ml-2">
                      {!notification.read && (
                        <button
                          type="button"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleMarkAsRead(notification.id);
                          }}
                          className={`p-1 rounded ${
                            darkMode
                              ? 'text-gray-400 hover:text-white hover:bg-gray-600'
                              : 'text-gray-500 hover:text-gray-700 hover:bg-gray-200'
                          }`}
                          title="Marcar como lida"
                        >
                          <Check className="h-3 w-3" />
                        </button>
                      )}
                      <button
                        type="button"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDelete(notification.id);
                        }}
                        className={`p-1 rounded ${
                          darkMode
                            ? 'text-gray-400 hover:text-red-400 hover:bg-gray-600'
                            : 'text-gray-500 hover:text-red-600 hover:bg-gray-200'
                        }`}
                        title="Deletar notificação"
                      >
                        <Trash2 className="h-3 w-3" />
                      </button>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Footer */}
          {notifications.length > 0 && (
            <div className={`px-4 py-3 border-t ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
              <button
                type="button"
                onClick={() => {
                  setIsOpen(false);
                  // Aqui você pode navegar para uma página de notificações completa
                }}
                className={`w-full text-center text-sm ${
                  darkMode
                    ? 'text-blue-400 hover:text-blue-300'
                    : 'text-blue-600 hover:text-blue-800'
                }`}
              >
                Ver todas as notificações
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default NotificationCenter;

