
import React, { useState } from 'react';
import { Outlet } from 'react-router-dom';
import { Sidebar } from './Sidebar';
import { Header } from './Header';
import { useOnlineTracking } from '../../hooks/useOnlineTracking';

export const Layout: React.FC = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  // Inicializar rastreamento de atividade online
  useOnlineTracking();

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  return (
    <div className="flex h-screen bg-neutral-light dark:bg-neutral-darkest overflow-hidden">
      <Sidebar isSidebarOpen={isSidebarOpen} toggleSidebar={toggleSidebar} />
      <div className="flex-1 flex flex-col overflow-hidden">
        <Header toggleSidebar={toggleSidebar} />
        <main className="flex-1 overflow-x-hidden overflow-y-auto bg-neutral-light dark:bg-neutral-darkest p-4 sm:p-6 lg:p-8">
          <Outlet /> {/* This is where routed components will be rendered */}
        </main>
      </div>
    </div>
  );
};