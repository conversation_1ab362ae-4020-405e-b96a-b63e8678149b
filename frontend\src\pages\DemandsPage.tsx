import React, { useState, useEffect, useCallback } from 'react';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Modal } from '../components/ui/Modal';
import { Input, Textarea } from '../components/ui/Input';
import { ICONS } from '../constants';
import { Demand, TeamMember } from '../types';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';
import { useAuth } from '../hooks/useAuth';
import { 
  getDemands, 
  addDemand, 
  updateDemand, 
  deleteDemand, 
  getTeamMembers 
} from '../services/firebaseService';

const getMemberName = (userId?: string, currentUser?: any, teamMembers: any[] = []): string => {
  if (!userId) return "Não atribuída";
  
  // Verificar se é o usuário atual
  if (currentUser && userId === currentUser.id) {
    return currentUser.name || currentUser.email?.split('@')[0] || 'Você';
  }
  
  // Buscar nos membros da equipe
  const member = teamMembers.find(m => m.userId === userId || m.id === userId);
  if (member && member.name) {
    return member.name;
  }
  
  // Se não encontrou, tentar buscar pelo ID
  return `Usuário ${userId.substring(0, 6)}`;
};

const DemandRow: React.FC<{ 
  demand: Demand; 
  onEdit: (demand: Demand) => void; 
  onDelete: (id: string) => void;
  currentUser?: any;
  teamMembers: any[];
}> = ({ demand, onEdit, onDelete, currentUser, teamMembers }) => {
  const statusClasses = {
    pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-700 dark:text-yellow-100',
    in_progress: 'bg-blue-100 text-blue-800 dark:bg-blue-700 dark:text-blue-100',
    completed: 'bg-green-100 text-green-800 dark:bg-green-700 dark:text-green-100',
    cancelled: 'bg-red-100 text-red-800 dark:bg-red-700 dark:text-red-100',
  };
  const priorityClasses = {
    low: 'text-gray-500 dark:text-neutral-DEFAULT',
    medium: 'text-yellow-600 dark:text-yellow-400',
    high: 'text-red-600 dark:text-red-400 font-semibold',
  }

  const handleEditClick = () => {
    console.log("Botão editar clicado para demanda:", demand);
    onEdit(demand);
  };

  return (
    <tr className="hover:bg-gray-50 dark:hover:bg-neutral-dark">
      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-neutral-dark dark:text-neutral-light">{demand.title}</td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-neutral-DEFAULT">
        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClasses[demand.status]}`}>
          {demand.status.replace('_', ' ')}
        </span>
      </td>
      <td className={`px-6 py-4 whitespace-nowrap text-sm ${priorityClasses[demand.priority]}`}>{demand.priority}</td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-neutral-DEFAULT">{demand.deadline ? new Date(demand.deadline).toLocaleDateString() : '-'}</td>
      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-neutral-DEFAULT">
        {getMemberName(demand.assignedTo, currentUser, teamMembers)}
      </td>
      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={handleEditClick} 
          className="text-primary hover:text-primary-dark dark:text-primary-light dark:hover:text-primary mr-2"
        >
          {React.cloneElement(ICONS.PENCIL, { className: "w-5 h-5"})}
        </Button>
        <Button variant="ghost" size="sm" onClick={() => onDelete(demand.id)} className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-500">
          {React.cloneElement(ICONS.TRASH, { className: "w-5 h-5"})}
        </Button>
      </td>
    </tr>
  );
};

const DemandsPage: React.FC = () => {
  const [demands, setDemands] = useState<Demand[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [currentDemand, setCurrentDemand] = useState<Partial<Demand> | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);

  const { currentUser } = useAuth();

  const fetchDemands = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const fetchedDemands = await getDemands();
      setDemands(fetchedDemands);
    } catch (err) {
      console.error("Failed to fetch demands:", err);
      setError("Falha ao carregar demandas. Tente novamente.");
    } finally {
      setIsLoading(false);
    }
  }, []);

  const fetchTeamMembers = async () => {
    try {
      const members = await getTeamMembers();
      console.log("Membros da equipe carregados:", members);
      setTeamMembers(members);
    } catch (error) {
      console.error("Erro ao carregar membros da equipe:", error);
      setError("Falha ao carregar membros da equipe.");
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        await fetchDemands();
        await fetchTeamMembers(); // Carregar membros da equipe
      } catch (err) {
        console.error("Failed to load data:", err);
        setError("Falha ao carregar dados. Tente novamente.");
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  const handleOpenModal = useCallback((demand?: Demand) => {
    console.log("handleOpenModal chamado com demanda:", demand);
    
    // Se estiver editando uma demanda existente
    if (demand) {
      try {
        // Verificar e formatar a data corretamente
        let formattedDeadline = '';
        if (demand.deadline) {
          // Verificar se deadline é uma string ou um objeto Date
          if (typeof demand.deadline === 'string') {
            // Se for uma string ISO, converter para o formato YYYY-MM-DD
            formattedDeadline = demand.deadline.split('T')[0];
          } else if (demand.deadline && typeof demand.deadline === 'object' && 'toISOString' in demand.deadline) {
            // Se for um objeto Date, converter para string no formato YYYY-MM-DD
            formattedDeadline = (demand.deadline as Date).toISOString().split('T')[0];
          }
        }
        
        // Garantir que todos os campos necessários estejam presentes
        const demandToEdit = {
          ...demand,
          deadline: formattedDeadline,
          // Garantir que outros campos obrigatórios estejam presentes
          status: demand.status || 'pending',
          priority: demand.priority || 'medium',
          description: demand.description || '',
          title: demand.title || ''
        };
        
        console.log("Demanda formatada para edição:", demandToEdit);
        setCurrentDemand(demandToEdit);
      } catch (error) {
        console.error("Erro ao formatar demanda para edição:", error);
        setError("Erro ao carregar demanda para edição.");
      }
    } else {
      // Criando nova demanda
      setCurrentDemand({ 
        title: '', 
        description: '', 
        priority: 'medium', 
        status: 'pending' 
      });
    }
    
    setIsModalOpen(true);
  }, []);

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setCurrentDemand(null);
    setError(null); // Clear modal-specific errors
  };

  const handleSaveDemand = async () => {
    if (!currentDemand || !currentUser) {
      setError("Dados da demanda ou usuário inválidos.");
      return;
    }
    if (!currentDemand.title || !currentDemand.description) {
      setError("Título e Descrição são obrigatórios.");
      return;
    }

    setIsSaving(true);
    setError(null);
    try {
      console.log("Salvando demanda:", currentDemand);
      
      // Formatar a data corretamente
      let formattedDeadline = undefined;
      if (currentDemand.deadline) {
        // Garantir que a data esteja no formato ISO
        if (typeof currentDemand.deadline === 'string' && currentDemand.deadline.trim() !== '') {
          // Se for apenas uma data (YYYY-MM-DD), adicionar o horário
          if (currentDemand.deadline.length === 10) {
            formattedDeadline = `${currentDemand.deadline}T00:00:00.000Z`;
          } else {
            formattedDeadline = new Date(currentDemand.deadline).toISOString();
          }
        }
      }
      
      if (currentDemand.id) { // Editando
        console.log("Atualizando demanda existente:", currentDemand.id);
        const { id, createdBy, createdAt, ...updateData } = currentDemand;
        await updateDemand(id, {
          ...updateData,
          deadline: formattedDeadline
        } as Omit<Demand, 'id' | 'createdAt' | 'createdBy'>);
      } else { // Criando
        console.log("Criando nova demanda");
        const newDemandData = {
          title: currentDemand.title!,
          description: currentDemand.description!,
          status: currentDemand.status || 'pending',
          priority: currentDemand.priority || 'medium',
          deadline: formattedDeadline,
          assignedTo: currentDemand.assignedTo || undefined,
        }
        await addDemand(newDemandData as Omit<Demand, 'id' | 'createdAt' | 'updatedAt' | 'createdBy'>, currentUser.id);
      }
      
      console.log("Demanda salva com sucesso, recarregando demandas...");
      await fetchDemands(); 
      handleCloseModal();
    } catch (err) {
      console.error("Failed to save demand:", err);
      setError("Falha ao salvar demanda. Tente novamente.");
    } finally {
      setIsSaving(false);
    }
  };
  
  const handleDeleteDemand = async (id: string) => {
    if (window.confirm("Tem certeza que deseja excluir esta demanda?")) {
      setIsLoading(true); 
      setError(null);
      try {
        await deleteDemand(id);
        await fetchDemands(); 
      } catch (err) {
        console.error("Failed to delete demand:", err);
        setError("Falha ao excluir demanda. Tente novamente.");
      } finally {
        setIsLoading(false);
      }
    }
  };

  const filteredDemands = demands
    .filter(demand => demand.title.toLowerCase().includes(searchTerm.toLowerCase()))
    .filter(demand => statusFilter ? demand.status === statusFilter : true);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-neutral-dark dark:text-neutral-light">Gerenciamento de Demandas</h1>
        <Button onClick={() => handleOpenModal()} leftIcon={ICONS.PLUS}>
          Nova Demanda
        </Button>
      </div>

      {error && !isModalOpen && <p className="text-red-500 dark:text-red-400 bg-red-100 dark:bg-red-900 dark:bg-opacity-30 p-3 rounded-md">{error}</p>}

      <Card>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4 p-4 bg-gray-50 dark:bg-neutral-dark rounded-md">
           <Input 
            placeholder="Buscar por título..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
           />
           <select 
            className="block w-full px-3 py-2 border border-gray-300 dark:border-neutral-medium rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm bg-white dark:bg-neutral-dark dark:text-neutral-light"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            aria-label="Filtrar por status" // Adicionado para acessibilidade
           >
             <option value="">Todos os Status</option>
             <option value="pending">Pendente</option>
             <option value="in_progress">Em Progresso</option>
             <option value="completed">Concluída</option>
             <option value="cancelled">Cancelada</option>
           </select>
        </div>
        {isLoading && !demands.length ? (
          <div className="flex justify-center items-center py-10">
            <LoadingSpinner size="lg" />
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-neutral-dark">
              <thead className="bg-gray-50 dark:bg-neutral-darker">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-neutral-DEFAULT uppercase tracking-wider">Título</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-neutral-DEFAULT uppercase tracking-wider">Status</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-neutral-DEFAULT uppercase tracking-wider">Prioridade</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-neutral-DEFAULT uppercase tracking-wider">Prazo</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-neutral-DEFAULT uppercase tracking-wider">Atribuída a</th>
                  <th scope="col" className="relative px-6 py-3"><span className="sr-only">Ações</span></th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-neutral-darker divide-y divide-gray-200 dark:divide-neutral-dark">
                {filteredDemands.length > 0 ? (
                  filteredDemands.map((demand) => (
                    <DemandRow 
                      key={demand.id} 
                      demand={demand} 
                      onEdit={handleOpenModal} 
                      onDelete={handleDeleteDemand}
                      currentUser={currentUser}
                      teamMembers={teamMembers}
                    />
                  ))
                ) : (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500 dark:text-neutral-DEFAULT">
                      {searchTerm || statusFilter ? 'Nenhuma demanda encontrada com os filtros aplicados.' : 'Nenhuma demanda cadastrada.'}
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        )}
      </Card>

      <Modal isOpen={isModalOpen} onClose={handleCloseModal} title={currentDemand?.id ? "Editar Demanda" : "Nova Demanda"}>
        <div className="space-y-4">
          {error && <p className="text-red-500 dark:text-red-400 bg-red-100 dark:bg-red-900 dark:bg-opacity-30 p-2 rounded-md text-sm">{error}</p>}
          <Input
            label="Título da Demanda"
            value={currentDemand?.title || ''}
            onChange={(e) => setCurrentDemand(prev => ({ ...prev, title: e.target.value }))}
            required
            disabled={isSaving}
          />
          <Textarea
            label="Descrição Detalhada"
            value={currentDemand?.description || ''}
            onChange={(e) => setCurrentDemand(prev => ({ ...prev, description: e.target.value }))}
            required
            disabled={isSaving}
          />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="status" className="block text-sm font-medium text-neutral-dark dark:text-neutral-light mb-1">Status</label>
              <select
                id="status"
                value={currentDemand?.status || 'pending'}
                onChange={(e) => setCurrentDemand(prev => ({ ...prev, status: e.target.value as Demand['status'] }))}
                className="block w-full px-3 py-2 border border-gray-300 dark:border-neutral-medium rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm bg-white dark:bg-neutral-dark dark:text-neutral-light"
                disabled={isSaving}
              >
                <option value="pending">Pendente</option>
                <option value="in_progress">Em Progresso</option>
                <option value="completed">Concluída</option>
                <option value="cancelled">Cancelada</option>
              </select>
            </div>
            <div>
              <label htmlFor="priority" className="block text-sm font-medium text-neutral-dark dark:text-neutral-light mb-1">Prioridade</label>
              <select
                id="priority"
                value={currentDemand?.priority || 'medium'}
                onChange={(e) => setCurrentDemand(prev => ({ ...prev, priority: e.target.value as Demand['priority'] }))}
                className="block w-full px-3 py-2 border border-gray-300 dark:border-neutral-medium rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm bg-white dark:bg-neutral-dark dark:text-neutral-light"
                disabled={isSaving}
              >
                <option value="low">Baixa</option>
                <option value="medium">Média</option>
                <option value="high">Alta</option>
              </select>
            </div>
          </div>
          
          {/* Adicionar seletor de membro da equipe */}
          <div className="mb-4">
            <label htmlFor="assignedTo" className="block text-sm font-medium text-gray-700 dark:text-neutral-light mb-1">
              Atribuir para
            </label>
            <select
              id="assignedTo"
              value={currentDemand?.assignedTo || ''}
              onChange={(e) => setCurrentDemand(prev => ({ ...prev, assignedTo: e.target.value || undefined }))}
              className="w-full p-2 border border-gray-300 dark:border-neutral-dark rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-neutral-darker dark:text-neutral-light"
            >
              <option value="">Não atribuída</option>
              {currentUser && (
                <option value={currentUser.id}>Eu ({currentUser.name || currentUser.email || 'Usuário atual'})</option>
              )}
              {teamMembers
                .filter(member => currentUser && member.userId !== currentUser.id)
                .map(member => (
                  <option key={member.userId} value={member.userId}>
                    {member.name || `Membro ID: ${member.userId.substring(0, 8)}`}
                  </option>
                ))
              }
            </select>
            <p className="mt-1 text-sm text-gray-500 dark:text-neutral-DEFAULT">
              Atribuir demandas aos membros da equipe permite acompanhar o desempenho individual.
            </p>
          </div>
          
          <Input
            label="Prazo (Opcional)"
            type="date"
            value={currentDemand?.deadline || ''}
            onChange={(e) => setCurrentDemand(prev => ({ ...prev, deadline: e.target.value }))}
            disabled={isSaving}
          />
          
          <div className="flex justify-end space-x-3 mt-6">
            <Button variant="secondary" onClick={handleCloseModal} disabled={isSaving}>
              Cancelar
            </Button>
            <Button onClick={handleSaveDemand} isLoading={isSaving}>
              {currentDemand?.id ? "Atualizar" : "Salvar"}
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default DemandsPage;
