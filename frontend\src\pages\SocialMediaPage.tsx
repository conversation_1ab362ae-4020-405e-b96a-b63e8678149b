import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';
import { PoliticianSocialMedia, SocialMediaAnalytics } from '../types';
import { 
  getPoliticianProfile, 
  savePoliticianSocialMediaAccounts, 
  getPoliticianSocialMediaAccounts,
  getSocialMediaAnalytics
} from '../services/firebaseService';

const MAX_MEDIA_FILES = 4;
const ACCEPTED_MEDIA_TYPES = "image/jpeg,image/png,image/gif,image/svg+xml,image/webp,video/mp4,video/webm,video/ogg";

const CloseIcon: React.FC<{className?: string}> = ({className}) => (
  <svg className={className || "w-4 h-4"} fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd"></path>
  </svg>
);

const socialIcons = {
  Facebook: <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
    <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
  </svg>,
  Instagram: <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-pink-500" fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
  </svg>,
  Twitter: <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-blue-400" fill="currentColor" viewBox="0 0 24 24">
    <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
  </svg>,
  TikTok: <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
    <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
  </svg>
};

const SocialMediaPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [socialAccounts, setSocialAccounts] = useState<PoliticianSocialMedia[]>([]);
  const [socialAnalytics, setSocialAnalytics] = useState<SocialMediaAnalytics[]>([]);
  const [postContent, setPostContent] = useState('');
  const [selectedMedia, setSelectedMedia] = useState<File[]>([]);
  const [mediaPreviews, setMediaPreviews] = useState<string[]>([]);
  const [selectedPlatforms, setSelectedPlatforms] = useState<Set<string>>(new Set());
  const fileInputRef = useRef<HTMLInputElement>(null);
  const mediaPreviewsRef = useRef<string[]>([]);

  // Atualiza o ref quando os previews mudam
  useEffect(() => {
    mediaPreviewsRef.current = mediaPreviews;
  }, [mediaPreviews]);

  // Limpa as URLs dos previews quando o componente é desmontado ou a página é fechada
  useEffect(() => {
    const cleanupPreviews = () => {
      mediaPreviewsRef.current.forEach(url => {
        try {
          URL.revokeObjectURL(url);
        } catch (err) {
          console.warn('Erro ao limpar URL de preview:', err);
        }
      });
    };

    window.addEventListener('beforeunload', cleanupPreviews);
    
    return () => {
      window.removeEventListener('beforeunload', cleanupPreviews);
      cleanupPreviews();
    };
  }, []);

  const loadSocialData = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const [profile, accounts, analytics] = await Promise.all([
        getPoliticianProfile(),
        getPoliticianSocialMediaAccounts(),
        getSocialMediaAnalytics()
      ]);

      // Se temos perfil mas não temos contas conectadas, vamos criar as contas com os dados do perfil
      if (profile?.socialMedia && (!accounts || accounts.length === 0)) {
        const newAccounts: PoliticianSocialMedia[] = [];
        
        if (profile.socialMedia.facebook) {
          newAccounts.push({
            platform: 'Facebook',
            username: profile.socialMedia.facebook,
            url: profile.socialMedia.facebook.startsWith('http') ? profile.socialMedia.facebook : `https://facebook.com/${profile.socialMedia.facebook}`,
            connected: false,
          });
        }
        
        if (profile.socialMedia.instagram) {
          newAccounts.push({
            platform: 'Instagram',
            username: profile.socialMedia.instagram,
            url: `https://instagram.com/${profile.socialMedia.instagram.replace('@', '')}`,
            connected: false,
          });
        }
        
        if (profile.socialMedia.twitter) {
          newAccounts.push({
            platform: 'Twitter',
            username: profile.socialMedia.twitter,
            url: `https://twitter.com/${profile.socialMedia.twitter.replace('@', '')}`,
            connected: false,
          });
        }
        
        if (profile.socialMedia.tiktok) {
          newAccounts.push({
            platform: 'TikTok',
            username: profile.socialMedia.tiktok,
            url: `https://tiktok.com/@${profile.socialMedia.tiktok.replace('@', '')}`,
            connected: false,
          });
        }

        if (newAccounts.length > 0) {
          await savePoliticianSocialMediaAccounts(newAccounts);
          setSocialAccounts(newAccounts);
        }
      } else {
        setSocialAccounts(accounts || []);
      }
      
      setSocialAnalytics(analytics);
    } catch (err) {
      console.error('Erro ao carregar dados de redes sociais:', err);
      setError('Falha ao carregar dados das redes sociais. Tente novamente.');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    loadSocialData();
  }, [loadSocialData]);

  const handleConnect = async (platform: string) => {
    try {
      setIsLoading(true);
      
      // Busca o perfil do político para validar os dados
      const profile = await getPoliticianProfile();
      if (!profile?.socialMedia) {
        throw new Error('Perfil do político não tem dados de redes sociais configurados.');
      }

      let username = '';
      let url = '';
      
      switch (platform) {
        case 'Facebook':
          username = profile.socialMedia.facebook || '';
          if (!username) throw new Error('URL do Facebook não configurada no perfil do político.');
          url = username.startsWith('http') ? username : `https://facebook.com/${username}`;
          break;
        
        case 'Instagram':
          username = profile.socialMedia.instagram || '';
          if (!username) throw new Error('Usuário do Instagram não configurado no perfil do político.');
          username = username.replace('@', '');
          url = `https://instagram.com/${username}`;
          break;
        
        case 'Twitter':
          username = profile.socialMedia.twitter || '';
          if (!username) throw new Error('Usuário do Twitter não configurado no perfil do político.');
          username = username.replace('@', '');
          url = `https://twitter.com/${username}`;
          break;
        
        case 'TikTok':
          username = profile.socialMedia.tiktok || '';
          if (!username) throw new Error('Usuário do TikTok não configurado no perfil do político.');
          username = username.replace('@', '');
          url = `https://tiktok.com/@${username}`;
          break;
        
        default:
          throw new Error('Plataforma não suportada.');
      }

      // Em produção, aqui faríamos a autenticação OAuth com a plataforma
      const mockConnection: PoliticianSocialMedia = {
        platform,
        username,
        url,
        connected: true,
        lastSync: new Date().toISOString()
      };

      const updatedAccounts = [
        ...socialAccounts.filter(acc => acc.platform !== platform),
        mockConnection
      ];

      await savePoliticianSocialMediaAccounts(updatedAccounts);
      setSocialAccounts(updatedAccounts);
      alert(`Conta ${platform} conectada com sucesso!`);
    } catch (err) {
      console.error('Erro ao conectar rede social:', err);
      setError(err instanceof Error ? err.message : 'Falha ao conectar rede social. Tente novamente.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDisconnect = async (platform: string) => {
    if (window.confirm(`Tem certeza que deseja desconectar ${platform}?`)) {
      try {
        setIsLoading(true);
        const updatedAccounts = socialAccounts.filter(acc => acc.platform !== platform);
        await savePoliticianSocialMediaAccounts(updatedAccounts);
        setSocialAccounts(updatedAccounts);
        alert(`Conta ${platform} desconectada com sucesso!`);
      } catch (err) {
        console.error('Erro ao desconectar rede social:', err);
        setError('Falha ao desconectar rede social. Tente novamente.');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handlePost = async () => {
    if (!postContent.trim() && selectedMedia.length === 0) {
      setError('É necessário incluir texto ou mídia para fazer uma publicação.');
      return;
    }

    if (selectedPlatforms.size === 0) {
      setError('Selecione pelo menos uma rede social para publicar.');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Em produção, aqui faríamos o upload das mídias e publicação em cada rede
      const connectedAccounts = socialAccounts.filter(acc => 
        acc.connected && selectedPlatforms.has(acc.platform)
      );

      if (connectedAccounts.length === 0) {
        throw new Error('Nenhuma das redes sociais selecionadas está conectada.');
      }

      // Simula o tempo de upload e publicação
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Limpa o formulário após sucesso
      setPostContent('');
      setSelectedMedia([]);
      mediaPreviewsRef.current.forEach(url => URL.revokeObjectURL(url));
      setMediaPreviews([]);
      if (fileInputRef.current) fileInputRef.current.value = '';
      
      alert(`Publicação realizada com sucesso em ${connectedAccounts.length} rede(s) social(is)!`);
    } catch (err) {
      console.error('Erro ao fazer publicação:', err);
      setError(err instanceof Error ? err.message : 'Falha ao fazer a publicação. Tente novamente.');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <div className="p-4 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded-md">
          {error}
        </div>
      )}

      <Card title="Contas Conectadas">
        <ul className="divide-y divide-gray-200 dark:divide-neutral-dark">
          {socialAccounts.map(account => (
            <li key={account.platform} className="py-4 flex items-center justify-between">
              <div className="flex items-center">
                {socialIcons[account.platform as keyof typeof socialIcons]}
                <div className="ml-3">
                  <p className="text-sm font-medium text-neutral-dark dark:text-neutral-light">{account.platform}</p>
                  <p className="text-sm text-gray-500 dark:text-neutral-DEFAULT">{account.username}</p>
                </div>
              </div>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => handleDisconnect(account.platform)}
                className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-500"
              >
                Desconectar
              </Button>
            </li>
          ))}
        </ul>

        {socialAccounts.length === 0 && (
          <p className="text-center py-4 text-gray-500 dark:text-neutral-DEFAULT">
            Nenhuma rede social conectada.
          </p>
        )}

        <div className="mt-4 flex flex-wrap gap-2">
          <Button 
            onClick={() => handleConnect('Instagram')} 
            disabled={socialAccounts.some(acc => acc.platform === 'Instagram')}
            leftIcon={socialIcons.Instagram}
          >
            Conectar Instagram
          </Button>
          <Button 
            onClick={() => handleConnect('Facebook')} 
            disabled={socialAccounts.some(acc => acc.platform === 'Facebook')}
            leftIcon={socialIcons.Facebook}
          >
            Conectar Facebook
          </Button>
          <Button 
            onClick={() => handleConnect('Twitter')} 
            disabled={socialAccounts.some(acc => acc.platform === 'Twitter')}
            leftIcon={socialIcons.Twitter}
          >
            Conectar Twitter/X
          </Button>
          <Button 
            onClick={() => handleConnect('TikTok')} 
            disabled={socialAccounts.some(acc => acc.platform === 'TikTok')}
            leftIcon={socialIcons.TikTok}
          >
            Conectar TikTok
          </Button>
        </div>
      </Card>

      {socialAccounts.length > 0 && (
        <>
          <Card title="Análise de Redes Sociais">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {socialAnalytics.map(analytics => (
                <div 
                  key={analytics.platform} 
                  className="p-4 border dark:border-neutral-medium rounded-lg"
                >
                  <h3 className="font-semibold text-neutral-dark dark:text-neutral-light mb-2">
                    {analytics.platform}
                  </h3>
                  <div className="space-y-1">
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-neutral-DEFAULT">Seguidores:</span>
                      <span className="font-medium text-neutral-dark dark:text-neutral-light">
                        {analytics.followers.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-neutral-DEFAULT">Engajamento:</span>
                      <span className="font-medium text-neutral-dark dark:text-neutral-light">
                        {analytics.engagement.toFixed(1)}%
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500 dark:text-neutral-DEFAULT">Alcance:</span>
                      <span className="font-medium text-neutral-dark dark:text-neutral-light">
                        {analytics.reach.toLocaleString()}
                      </span>
                    </div>
                    <div className="mt-2 pt-2 border-t dark:border-neutral-medium">
                      <div className="flex gap-2">
                        <div className="flex-1 bg-green-100 dark:bg-green-900/30 rounded px-2 py-1">
                          <span className="text-xs text-green-600 dark:text-green-400">
                            {analytics.sentiment.positive.toFixed(1)}% positivo
                          </span>
                        </div>
                        <div className="flex-1 bg-yellow-100 dark:bg-yellow-900/30 rounded px-2 py-1">
                          <span className="text-xs text-yellow-600 dark:text-yellow-400">
                            {analytics.sentiment.neutral.toFixed(1)}% neutro
                          </span>
                        </div>
                        <div className="flex-1 bg-red-100 dark:bg-red-900/30 rounded px-2 py-1">
                          <span className="text-xs text-red-600 dark:text-red-400">
                            {analytics.sentiment.negative.toFixed(1)}% negativo
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          <Card title="Publicar Conteúdo">
            <div className="space-y-4">
              <textarea
                id="postContent"
                name="postContent"
                rows={5}
                className="mt-1 block w-full shadow-sm sm:text-sm border-gray-300 dark:border-neutral-medium rounded-md 
                         focus:ring-primary focus:border-primary dark:focus:ring-primary-light dark:focus:border-primary-light 
                         p-2 bg-white dark:bg-neutral-dark dark:text-neutral-light 
                         placeholder-gray-400 dark:placeholder-neutral-medium"
                placeholder="Escreva sua postagem aqui..."
                value={postContent}
                onChange={e => setPostContent(e.target.value)}
              />

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-neutral-light mb-2">
                  Mídia (Opcional)
                </label>
                <input
                  type="file"
                  accept={ACCEPTED_MEDIA_TYPES}
                  title="Selecione arquivos de mídia para anexar à postagem"
                  placeholder="Selecione arquivos de mídia"
                  onChange={(e) => {
                    const files = Array.from(e.target.files || []);
                    if (files.length + selectedMedia.length > MAX_MEDIA_FILES) {
                      alert(`Máximo de ${MAX_MEDIA_FILES} arquivos permitidos.`);
                      return;
                    }
                    setSelectedMedia(prev => [...prev, ...files]);
                    const previews = files.map(file => URL.createObjectURL(file));
                    setMediaPreviews(prev => [...prev, ...previews]);
                  }}
                  className="block w-full text-sm text-gray-500 dark:text-neutral-DEFAULT 
                           file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 
                           file:text-sm file:font-semibold 
                           file:bg-primary-light file:text-primary 
                           dark:file:bg-primary-dark dark:file:text-neutral-extralight
                           hover:file:bg-primary dark:hover:file:bg-primary-light
                           dark:bg-neutral-dark dark:border-neutral-medium"
                  multiple
                  ref={fileInputRef}
                />
                <p className="mt-1 text-sm text-gray-500 dark:text-neutral-DEFAULT">
                  Máximo de {MAX_MEDIA_FILES} arquivos. Suporta imagens (JPG, PNG, GIF, SVG, WebP) e vídeos (MP4, WebM, OGG).
                </p>
              </div>

              {selectedMedia.length > 0 && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                  {mediaPreviews.map((previewUrl, index) => (
                    <div key={index} className="relative aspect-square">
                      {selectedMedia[index]?.type.startsWith('image/') ? (
                        <img 
                          src={previewUrl} 
                          alt={`Prévia ${index + 1}`} 
                          className="w-full h-full object-cover rounded-md shadow" 
                        />
                      ) : selectedMedia[index]?.type.startsWith('video/') ? (
                        <video 
                          src={previewUrl} 
                          className="w-full h-full object-cover rounded-md shadow bg-black" 
                          preload="metadata" 
                          muted 
                          loop 
                          playsInline
                        >
                          <source src={previewUrl} type={selectedMedia[index].type} />
                          Seu navegador não suporta a tag de vídeo.
                        </video>
                      ) : (
                        <div className="w-full h-full flex flex-col items-center justify-center bg-gray-200 dark:bg-neutral-medium rounded-md text-xs text-gray-500 dark:text-neutral-light p-1">
                          <span className="truncate max-w-full">{selectedMedia[index]?.name}</span>
                          <span>Tipo não suportado</span>
                        </div>
                      )}
                      <button
                        type="button"
                        onClick={() => {
                          URL.revokeObjectURL(mediaPreviews[index]);
                          setSelectedMedia(prev => prev.filter((_, i) => i !== index));
                          setMediaPreviews(prev => prev.filter((_, i) => i !== index));
                        }}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 shadow-md hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700"
                        title={`Remover ${selectedMedia[index]?.name || `mídia ${index + 1}`}`}
                        aria-label={`Remover ${selectedMedia[index]?.name || `mídia ${index + 1}`}`}
                      >
                        <CloseIcon className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>
              )}

              <div className="mt-4">
                <fieldset className="space-y-2">
                  <legend className="text-sm font-medium text-gray-700 dark:text-neutral-light">Publicar em:</legend>
                  <div className="flex flex-wrap gap-2">
                    {socialAccounts.map(account => (
                      <label key={account.platform} className="inline-flex items-center">
                        <input
                          type="checkbox"
                          title={`Publicar no ${account.platform}`}
                          aria-label={`Publicar no ${account.platform}`}
                          className="form-checkbox h-4 w-4 text-primary border-gray-300 rounded 
                                   dark:border-neutral-medium dark:bg-neutral-dark 
                                   focus:ring-primary dark:focus:ring-primary-light"
                          checked={selectedPlatforms.has(account.platform)}
                          onChange={(e) => {
                            const newPlatforms = new Set(selectedPlatforms);
                            if (e.target.checked) {
                              newPlatforms.add(account.platform);
                            } else {
                              newPlatforms.delete(account.platform);
                            }
                            setSelectedPlatforms(newPlatforms);
                          }}
                          disabled={!account.connected}
                        />
                        <span className="ml-2 text-sm text-gray-700 dark:text-neutral-light">
                          {account.platform}
                          {!account.connected && (
                            <span className="ml-1 text-xs text-red-500">(desconectado)</span>
                          )}
                        </span>
                      </label>
                    ))}
                  </div>
                </fieldset>
              </div>

              <div className="flex justify-end gap-4">
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setPostContent('');
                    setSelectedMedia([]);
                    mediaPreviewsRef.current.forEach(url => URL.revokeObjectURL(url));
                    setMediaPreviews([]);
                    setSelectedPlatforms(new Set());
                    if (fileInputRef.current) fileInputRef.current.value = '';
                  }}
                  disabled={isLoading}
                >
                  Limpar
                </Button>
                <Button 
                  variant="primary" 
                  disabled={
                    isLoading || 
                    (!postContent.trim() && selectedMedia.length === 0) || 
                    socialAccounts.length === 0 ||
                    selectedPlatforms.size === 0
                  }
                  isLoading={isLoading}
                  onClick={handlePost}
                >
                  {isLoading ? "Publicando..." : "Publicar"}
                </Button>
              </div>
            </div>
          </Card>
        </>
      )}
    </div>
  );
};

export default SocialMediaPage;