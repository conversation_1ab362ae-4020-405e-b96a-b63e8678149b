[{"origin": ["http://localhost:5173", "http://127.0.0.1:5173", "*"], "responseHeader": ["Content-Type", "Authorization", "Content-Length", "User-Agent", "Range", "Accept", "Origin", "x-goog-resumable", "x-goog-meta-*", "x-requested-with", "x-goog-upload-protocol", "x-goog-upload-command", "x-goog-upload-status", "x-goog-upload-url", "x-goog-upload-control-url", "x-goog-upload-chunk-granularity", "x-goog-upload-offset", "x-goog-upload-content-type", "x-goog-upload-content-length", "x-goog-hash", "x-goog-storage-class", "Access-Control-Allow-Origin", "Access-Control-Allow-Methods", "Access-Control-Allow-Headers", "Access-Control-Expose-Headers", "Access-Control-Max-Age"], "method": ["GET", "HEAD", "PUT", "POST", "DELETE", "OPTIONS"], "maxAgeSeconds": 3600}]