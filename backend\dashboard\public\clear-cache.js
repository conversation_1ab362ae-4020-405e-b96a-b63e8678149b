// Script para limpar cache do Service Worker
// Execute este script no console do navegador para limpar o cache

(async function clearServiceWorkerCache() {
  try {
    console.log('🧹 Iniciando limpeza do cache...');
    
    // 1. Limpar todos os caches
    const cacheNames = await caches.keys();
    console.log('📦 Caches encontrados:', cacheNames);
    
    await Promise.all(
      cacheNames.map(cacheName => {
        console.log('🗑️ Removendo cache:', cacheName);
        return caches.delete(cacheName);
      })
    );
    
    // 2. Desregistrar Service Workers
    if ('serviceWorker' in navigator) {
      const registrations = await navigator.serviceWorker.getRegistrations();
      console.log('🔧 Service Workers encontrados:', registrations.length);
      
      await Promise.all(
        registrations.map(registration => {
          console.log('🗑️ Desregistrando SW:', registration.scope);
          return registration.unregister();
        })
      );
    }
    
    // 3. Limpar localStorage e sessionStorage
    localStorage.clear();
    sessionStorage.clear();
    console.log('🧹 Storage limpo');
    
    console.log('✅ Cache limpo com sucesso!');
    console.log('🔄 Recarregue a página para aplicar as mudanças');
    
    // Opcional: recarregar automaticamente
    // window.location.reload(true);
    
  } catch (error) {
    console.error('❌ Erro ao limpar cache:', error);
  }
})();
