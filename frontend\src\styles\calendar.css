/* Estilos customizados para React Big Calendar */

/* Container principal */
.calendar-container {
  font-family: inherit;
}

/* Toolbar (navegação) */
.rbc-toolbar {
  @apply flex flex-wrap items-center justify-between mb-4 p-2 bg-gray-50 dark:bg-gray-700 rounded-lg;
}

.rbc-toolbar button {
  @apply px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-600 border border-gray-300 dark:border-gray-500 rounded-md hover:bg-gray-50 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors;
}

.rbc-toolbar button:active,
.rbc-toolbar button.rbc-active {
  @apply bg-blue-600 text-white border-blue-600 dark:bg-blue-500 dark:border-blue-500;
}

.rbc-toolbar-label {
  @apply text-lg font-semibold text-gray-900 dark:text-gray-100 mx-4;
}

/* Header do calendário */
.rbc-header {
  @apply p-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 border-b border-gray-200 dark:border-gray-500;
}

.rbc-header + .rbc-header {
  @apply border-l border-gray-200 dark:border-gray-500;
}

/* Células do calendário */
.rbc-month-view,
.rbc-time-view {
  @apply border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden;
}

.rbc-date-cell {
  @apply text-right p-1;
}

.rbc-date-cell > a {
  @apply text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400;
}

.rbc-date-cell.rbc-off-range {
  @apply text-gray-400 dark:text-gray-500;
}

.rbc-date-cell.rbc-now {
  @apply font-bold text-blue-600 dark:text-blue-400;
}

.rbc-date-cell.rbc-current {
  @apply bg-blue-50 dark:bg-blue-900 dark:bg-opacity-30;
}

/* Eventos */
.rbc-event {
  @apply text-xs px-2 py-1 rounded text-white cursor-pointer;
  background-color: #3174ad !important;
  border: none !important;
}

.rbc-event:hover {
  @apply opacity-90;
}

.rbc-event.rbc-selected {
  @apply ring-2 ring-blue-300 dark:ring-blue-600;
}

.rbc-event-label {
  @apply font-medium;
}

.rbc-event-content {
  @apply truncate;
}

/* Slots de tempo */
.rbc-time-slot {
  @apply border-b border-gray-100 dark:border-gray-600;
}

.rbc-time-gutter {
  @apply bg-gray-50 dark:bg-gray-700 border-r border-gray-200 dark:border-gray-600;
}

.rbc-time-gutter .rbc-timeslot-group {
  @apply border-b border-gray-200 dark:border-gray-600;
}

.rbc-time-header {
  @apply border-b border-gray-200 dark:border-gray-600;
}

.rbc-time-header-content {
  @apply border-l border-gray-200 dark:border-gray-600;
}

/* Visualização de agenda */
.rbc-agenda-view {
  @apply border border-gray-200 dark:border-gray-600 rounded-lg;
}

.rbc-agenda-view table {
  @apply w-full;
}

.rbc-agenda-view .rbc-agenda-date-cell {
  @apply p-3 border-b border-gray-200 dark:border-gray-600 bg-gray-50 dark:bg-gray-700 font-medium text-gray-900 dark:text-gray-100;
}

.rbc-agenda-view .rbc-agenda-time-cell {
  @apply p-3 border-b border-gray-200 dark:border-gray-600 text-sm text-gray-600 dark:text-gray-400;
}

.rbc-agenda-view .rbc-agenda-event-cell {
  @apply p-3 border-b border-gray-200 dark:border-gray-600;
}

/* Popup de eventos */
.rbc-overlay {
  @apply bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg shadow-lg p-2 z-50;
}

.rbc-overlay-header {
  @apply font-medium text-gray-900 dark:text-gray-100 border-b border-gray-200 dark:border-gray-600 pb-2 mb-2;
}

/* Seleção de slots */
.rbc-slot-selection {
  @apply bg-blue-200 dark:bg-blue-800 bg-opacity-50 dark:bg-opacity-50;
}

/* Today highlight */
.rbc-today {
  @apply bg-blue-50 dark:bg-blue-900 dark:bg-opacity-20;
}

/* Responsividade */
@media (max-width: 768px) {
  .rbc-toolbar {
    @apply flex-col space-y-2;
  }
  
  .rbc-toolbar .rbc-btn-group {
    @apply flex flex-wrap gap-1;
  }
  
  .rbc-toolbar button {
    @apply px-2 py-1 text-xs;
  }
  
  .rbc-toolbar-label {
    @apply text-base mx-0;
  }
  
  .rbc-header {
    @apply text-xs p-1;
  }
  
  .rbc-event {
    @apply text-xs px-1 py-0.5;
  }
}

/* Estados de loading */
.calendar-loading {
  @apply opacity-50 pointer-events-none;
}

/* Customização para tema escuro */
.dark .rbc-calendar {
  @apply bg-gray-800 text-gray-100;
}

.dark .rbc-month-view,
.dark .rbc-time-view {
  @apply bg-gray-800;
}

.dark .rbc-day-bg {
  @apply bg-gray-800 border-gray-600;
}

.dark .rbc-day-bg.rbc-off-range-bg {
  @apply bg-gray-900;
}

.dark .rbc-time-content {
  @apply border-gray-600;
}

.dark .rbc-time-content > * + * > * {
  @apply border-gray-600;
}

/* Animações suaves */
.rbc-event {
  transition: all 0.2s ease-in-out;
}

.rbc-event:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Melhorias de acessibilidade */
.rbc-event:focus {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}

.rbc-toolbar button:focus {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}

/* Indicadores visuais */
.rbc-event.event-past {
  @apply opacity-60;
}

.rbc-event.event-current {
  @apply ring-2 ring-green-400;
}

.rbc-event.event-upcoming {
  @apply ring-2 ring-yellow-400;
}
