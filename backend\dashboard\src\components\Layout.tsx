import React, { useState, ReactNode } from 'react';
import Header from './Header';
import { useAuth } from '../contexts/AuthContext';
import {
  LayoutDashboard,
  Brain,
  BarChart3,
  Settings,
  Zap,
  Users,
  CreditCard,
  Upload
} from 'lucide-react';

interface LayoutProps {
  children: ReactNode;
  currentPage: string;
  onPageChange: (page: string) => void;
}

const Layout: React.FC<LayoutProps> = ({ children, currentPage, onPageChange }) => {
  const { user } = useAuth();
  const [darkMode, setDarkMode] = useState(() => {
    const saved = localStorage.getItem('darkMode');
    return saved ? JSON.parse(saved) : false;
  });
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const toggleDarkMode = () => {
    const newDarkMode = !darkMode;
    setDarkMode(newDarkMode);
    localStorage.setItem('darkMode', JSON.stringify(newDarkMode));
    
    // Aplicar classe dark no documento
    if (newDarkMode) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  // Aplicar dark mode no carregamento
  React.useEffect(() => {
    console.log('🌙 Aplicando modo escuro:', darkMode);
    if (darkMode) {
      document.documentElement.classList.add('dark');
      document.body.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
      document.body.classList.remove('dark');
    }
  }, [darkMode]);

  // Aplicar dark mode na inicialização
  React.useEffect(() => {
    const savedDarkMode = localStorage.getItem('darkMode');
    if (savedDarkMode) {
      const isDark = JSON.parse(savedDarkMode);
      console.log('🌙 Modo escuro salvo:', isDark);
      if (isDark) {
        document.documentElement.classList.add('dark');
        document.body.classList.add('dark');
      }
    }
  }, []);

  // Verificar se o usuário tem acesso às funcionalidades de IA
  const hasAIFeatures = user?.plan?.features?.aiFeatures && user.plan.features.aiFeatures.length > 0;

  // Itens de navegação
  const navigationItems = [
    {
      id: 'dashboard',
      name: 'Dashboard',
      icon: LayoutDashboard,
      description: 'Visão geral dos planos'
    },
    ...(hasAIFeatures ? [
      {
        id: 'ai-features',
        name: 'Funcionalidades IA',
        icon: Brain,
        description: 'Recursos de Inteligência Artificial',
        badge: 'IA'
      },
      {
        id: 'ai-insights',
        name: 'Insights IA',
        icon: BarChart3,
        description: 'Análises e métricas de IA',
        badge: 'PRO'
      }
    ] : []),
    {
      id: 'users',
      name: 'Clientes Online',
      icon: Users,
      description: 'Monitorar clientes conectados'
    },
    {
      id: 'payments',
      name: 'Monitor de Pagamentos',
      icon: CreditCard,
      description: 'Acompanhar receitas e transações'
    },
    {
      id: 'stripe-import',
      name: 'Importar Stripe',
      icon: Upload,
      description: 'Importar dados do CSV do Stripe'
    },
    {
      id: 'settings',
      name: 'Configurações',
      icon: Settings,
      description: 'Configurações da conta'
    }
  ];

  if (!user) {
    return null; // Ou um loading spinner
  }

  return (
    <div className={`min-h-screen ${darkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
      {/* Header */}
      <Header
        onToggleSidebar={toggleSidebar}
        darkMode={darkMode}
        onToggleDarkMode={toggleDarkMode}
      />

      <div className="flex">
        {/* Sidebar Desktop */}
        <div className={`hidden lg:flex lg:flex-shrink-0 lg:w-64 ${
          darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        } border-r`}>
          <div className="flex flex-col w-full">
            {/* Logo/Título */}
            <div className="flex items-center h-16 px-4 border-b border-gray-200 dark:border-gray-700">
              <Brain className="w-8 h-8 text-blue-600" />
              <span className={`ml-2 text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Promandato
              </span>
            </div>

            {/* Navegação */}
            <nav className="flex-1 px-2 py-4 space-y-1">
              {navigationItems.map((item) => {
                const Icon = item.icon;
                const isActive = currentPage === item.id;
                
                return (
                  <button
                    type="button"
                    key={item.id}
                    onClick={() => onPageChange(item.id)}
                    className={`w-full group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${
                      isActive
                        ? darkMode
                          ? 'bg-gray-700 text-white'
                          : 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                        : darkMode
                          ? 'text-gray-300 hover:bg-gray-700 hover:text-white'
                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                  >
                    <Icon className={`mr-3 h-5 w-5 ${
                      isActive 
                        ? darkMode ? 'text-white' : 'text-blue-700'
                        : darkMode ? 'text-gray-400' : 'text-gray-400'
                    }`} />
                    <span className="flex-1 text-left">{item.name}</span>
                    {item.badge && (
                      <span className={`ml-2 px-2 py-1 text-xs font-medium rounded-full ${
                        item.badge === 'IA' 
                          ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
                          : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      }`}>
                        {item.badge}
                      </span>
                    )}
                  </button>
                );
              })}
            </nav>

            {/* Informações do Plano */}
            {user.plan && (
              <div className={`p-4 border-t ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
                <div className={`text-xs font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'} uppercase tracking-wide`}>
                  Plano Atual
                </div>
                <div className={`mt-1 text-sm font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  {user.plan.name}
                </div>
                {hasAIFeatures && (
                  <div className="mt-2 flex items-center">
                    <Zap className="w-4 h-4 text-yellow-500 mr-1" />
                    <span className="text-xs text-yellow-600 dark:text-yellow-400 font-medium">
                      IA Ativada
                    </span>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Sidebar Mobile */}
        {sidebarOpen && (
          <div className="fixed inset-0 z-40 lg:hidden">
            {/* Overlay */}
            <div 
              className="fixed inset-0 bg-black bg-opacity-50"
              onClick={() => setSidebarOpen(false)}
            />
            
            {/* Sidebar */}
            <div className={`fixed inset-y-0 left-0 w-64 ${
              darkMode ? 'bg-gray-800' : 'bg-white'
            } shadow-lg transform transition-transform duration-300 ease-in-out`}>
              <div className="flex flex-col h-full">
                {/* Logo/Título */}
                <div className="flex items-center h-16 px-4 border-b border-gray-200 dark:border-gray-700">
                  <Brain className="w-8 h-8 text-blue-600" />
                  <span className={`ml-2 text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                    Promandato
                  </span>
                </div>

                {/* Navegação Mobile */}
                <nav className="flex-1 px-2 py-4 space-y-1">
                  {navigationItems.map((item) => {
                    const Icon = item.icon;
                    const isActive = currentPage === item.id;
                    
                    return (
                      <button
                        type="button"
                        key={item.id}
                        onClick={() => {
                          onPageChange(item.id);
                          setSidebarOpen(false);
                        }}
                        className={`w-full group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${
                          isActive
                            ? darkMode
                              ? 'bg-gray-700 text-white'
                              : 'bg-blue-50 text-blue-700'
                            : darkMode
                              ? 'text-gray-300 hover:bg-gray-700 hover:text-white'
                              : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                        }`}
                      >
                        <Icon className={`mr-3 h-5 w-5 ${
                          isActive 
                            ? darkMode ? 'text-white' : 'text-blue-700'
                            : darkMode ? 'text-gray-400' : 'text-gray-400'
                        }`} />
                        <span className="flex-1 text-left">{item.name}</span>
                        {item.badge && (
                          <span className={`ml-2 px-2 py-1 text-xs font-medium rounded-full ${
                            item.badge === 'IA' 
                              ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
                              : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                          }`}>
                            {item.badge}
                          </span>
                        )}
                      </button>
                    );
                  })}
                </nav>
              </div>
            </div>
          </div>
        )}

        {/* Conteúdo Principal */}
        <main className={`flex-1 min-w-0 ${darkMode ? 'bg-gray-900' : 'bg-gray-50'}`}>
          <div className="py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              {children}
            </div>
          </div>
        </main>
      </div>

      {/* Footer */}
      <footer className={`${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-t`}>
        <div className="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center">
            <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
              © 2024 Sistema de Gestão Política. Todos os direitos reservados.
            </div>
            <div className="flex space-x-4">
              <a 
                href="#" 
                className={`text-sm hover:underline ${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-700'}`}
              >
                Termos de Uso
              </a>
              <a 
                href="#" 
                className={`text-sm hover:underline ${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-700'}`}
              >
                Política de Privacidade
              </a>
              <a 
                href="#" 
                className={`text-sm hover:underline ${darkMode ? 'text-gray-400 hover:text-white' : 'text-gray-500 hover:text-gray-700'}`}
              >
                Suporte
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Layout;