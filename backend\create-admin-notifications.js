import NotificationService from './services/notificationService.js';
import User from './models/User.js';

async function createAdminNotifications() {
  try {
    console.log('🔧 Criando notificações para administradores...');
    
    // Buscar usuários administradores
    const users = await User.findAll();
    const adminUsers = users.filter(user => user.role === 'ADMIN');
    
    if (adminUsers.length === 0) {
      console.log('❌ Nenhum usuário administrador encontrado');
      return;
    }
    
    console.log(`👑 Encontrados ${adminUsers.length} administradores`);
    
    for (const admin of adminUsers) {
      console.log(`📢 Criando notificações para admin: ${admin.name}`);
      
      // Notificação de nova demanda urgente
      await NotificationService.notifyUser(
        admin.id,
        'Nova demanda URGENTE recebida',
        'Cidadão reportou problema grave de saneamento na Rua Principal, 123',
        {
          type: 'error',
          category: 'demand',
          actionUrl: '/demands/urgent-001',
          actionLabel: 'Ver demanda urgente',
          metadata: {
            priority: 'urgent',
            category: 'saneamento',
            location: 'Rua Principal, 123'
          }
        }
      );
      
      // Notificação de sistema
      await NotificationService.notifyUser(
        admin.id,
        'Sistema atualizado com sucesso',
        'Dashboard administrativo foi atualizado para versão 2.1.0 com novas funcionalidades',
        {
          type: 'success',
          category: 'system',
          actionUrl: '/changelog',
          actionLabel: 'Ver novidades',
          metadata: {
            version: '2.1.0',
            features: ['notificações', 'relatórios', 'dashboard']
          }
        }
      );
      
      // Notificação de evento próximo
      await NotificationService.notifyUser(
        admin.id,
        'Reunião de equipe em 1 hora',
        'Reunião semanal da equipe administrativa agendada para 14:00',
        {
          type: 'warning',
          category: 'event',
          actionUrl: '/calendar/meeting-001',
          actionLabel: 'Ver agenda',
          metadata: {
            eventTime: '14:00',
            participants: ['Admin', 'Gerentes'],
            location: 'Sala de reuniões'
          }
        }
      );
      
      // Notificação de relatório
      await NotificationService.notifyUser(
        admin.id,
        'Relatório mensal disponível',
        'Relatório de demandas do mês de dezembro está pronto para análise',
        {
          type: 'info',
          category: 'general',
          actionUrl: '/reports/monthly/december',
          actionLabel: 'Ver relatório',
          metadata: {
            reportType: 'monthly',
            period: 'dezembro',
            totalDemands: 156,
            resolved: 142
          }
        }
      );
      
      // Notificação de backup
      await NotificationService.notifyUser(
        admin.id,
        'Backup automático concluído',
        'Backup diário dos dados foi realizado com sucesso às 02:00',
        {
          type: 'success',
          category: 'system',
          actionUrl: '/admin/backups',
          actionLabel: 'Ver backups',
          metadata: {
            backupTime: '02:00',
            size: '2.3 GB',
            status: 'success'
          }
        }
      );
      
      console.log(`✅ 5 notificações criadas para ${admin.name}`);
    }
    
    console.log('🎉 Notificações administrativas criadas com sucesso!');
    
  } catch (error) {
    console.error('❌ Erro ao criar notificações administrativas:', error);
  }
}

createAdminNotifications();
