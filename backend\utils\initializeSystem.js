import User from '../models/User.js';
import config from '../config.js';
import fs from 'fs-extra';
import path from 'path';
import { fileURLToPath } from 'url';

// ES module equivalent of __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class SystemInitializer {
  static async initialize() {
    try {
      console.log('🚀 Inicializando sistema...');

      // Garantir que os diretórios existem
      await this.ensureDirectories();

      // Criar usuário admin padrão se não existir
      await this.createDefaultAdmin();

      // Criar dados iniciais se necessário
      await this.createInitialData();

      console.log('✅ Sistema inicializado com sucesso!');
      
    } catch (error) {
      console.error('❌ Erro ao inicializar sistema:', error);
      throw error;
    }
  }

  static async ensureDirectories() {
    const directories = [
      path.dirname(config.database.usersPath),
      path.dirname(config.database.sessionsPath),
      path.dirname(config.database.logsPath)
    ];

    for (const dir of directories) {
      await fs.ensureDir(dir);
    }

    console.log('📁 Diretórios criados/verificados');
  }

  static async createDefaultAdmin() {
    try {
      // Verificar se já existe um admin
      const existingAdmin = await User.findByEmail(config.defaultAdmin.email);
      
      if (existingAdmin) {
        console.log('👤 Usuário admin já existe:', config.defaultAdmin.email);
        return;
      }

      // Criar usuário admin padrão
      const adminData = {
        email: config.defaultAdmin.email,
        password: config.defaultAdmin.password,
        name: config.defaultAdmin.name,
        role: config.defaultAdmin.role,
        permissions: config.defaultAdmin.permissions,
        isActive: true,
        emailVerified: true,
        profile: {
          department: 'Administração',
          position: 'Administrador do Sistema'
        }
      };

      const admin = await User.create(adminData);
      
      console.log('👤 Usuário admin criado com sucesso!');
      console.log('📧 Email:', config.defaultAdmin.email);
      console.log('🔑 Senha:', config.defaultAdmin.password);
      console.log('⚠️  IMPORTANTE: Altere a senha padrão após o primeiro login!');

    } catch (error) {
      console.error('Erro ao criar usuário admin:', error);
      throw error;
    }
  }

  static async createInitialData() {
    // Criar arquivo de sessões vazio se não existir
    try {
      await fs.ensureFile(config.database.sessionsPath);
      const sessionsData = await fs.readFile(config.database.sessionsPath, 'utf8');
      if (!sessionsData) {
        await fs.writeFile(config.database.sessionsPath, '[]');
      }
    } catch (error) {
      await fs.writeFile(config.database.sessionsPath, '[]');
    }

    // Criar arquivo de logs vazio se não existir
    try {
      await fs.ensureFile(config.database.logsPath);
      const logsData = await fs.readFile(config.database.logsPath, 'utf8');
      if (!logsData) {
        await fs.writeFile(config.database.logsPath, '[]');
      }
    } catch (error) {
      await fs.writeFile(config.database.logsPath, '[]');
    }

    console.log('📄 Arquivos de dados inicializados');
  }

  static async getSystemInfo() {
    try {
      const users = await User.findAll();
      const adminUsers = users.filter(u => u.role === 'ADMIN');
      const activeUsers = users.filter(u => u.isActive);

      return {
        totalUsers: users.length,
        adminUsers: adminUsers.length,
        activeUsers: activeUsers.length,
        inactiveUsers: users.length - activeUsers.length,
        hasDefaultAdmin: users.some(u => u.email === config.defaultAdmin.email),
        systemInitialized: users.length > 0
      };
    } catch (error) {
      return {
        totalUsers: 0,
        adminUsers: 0,
        activeUsers: 0,
        inactiveUsers: 0,
        hasDefaultAdmin: false,
        systemInitialized: false,
        error: error.message
      };
    }
  }

  static async resetSystem() {
    try {
      console.log('🔄 Resetando sistema...');

      // Backup dos dados atuais
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupDir = path.join(__dirname, '../backups', timestamp);
      
      await fs.ensureDir(backupDir);
      
      // Fazer backup dos arquivos existentes
      const filesToBackup = [
        config.database.usersPath,
        config.database.sessionsPath,
        config.database.logsPath
      ];

      for (const filePath of filesToBackup) {
        if (await fs.pathExists(filePath)) {
          const fileName = path.basename(filePath);
          await fs.copy(filePath, path.join(backupDir, fileName));
        }
      }

      console.log('💾 Backup criado em:', backupDir);

      // Limpar dados
      await fs.writeFile(config.database.usersPath, '[]');
      await fs.writeFile(config.database.sessionsPath, '[]');
      await fs.writeFile(config.database.logsPath, '[]');

      // Reinicializar
      await this.initialize();

      console.log('✅ Sistema resetado com sucesso!');

    } catch (error) {
      console.error('❌ Erro ao resetar sistema:', error);
      throw error;
    }
  }

  static async createTestUsers() {
    try {
      console.log('👥 Criando usuários de teste...');

      const testUsers = [
        {
          email: '<EMAIL>',
          password: 'manager123',
          name: 'Gerente Teste',
          role: 'MANAGER',
          profile: {
            department: 'Gestão',
            position: 'Gerente'
          }
        },
        {
          email: '<EMAIL>',
          password: 'user123',
          name: 'Usuário Teste',
          role: 'USER',
          profile: {
            department: 'Operacional',
            position: 'Usuário'
          }
        }
      ];

      for (const userData of testUsers) {
        const existingUser = await User.findByEmail(userData.email);
        if (!existingUser) {
          await User.create({
            ...userData,
            isActive: true,
            emailVerified: true
          });
          console.log(`✅ Usuário criado: ${userData.email} (${userData.role})`);
        } else {
          console.log(`⚠️  Usuário já existe: ${userData.email}`);
        }
      }

      console.log('👥 Usuários de teste criados!');

    } catch (error) {
      console.error('❌ Erro ao criar usuários de teste:', error);
      throw error;
    }
  }
}

export default SystemInitializer;