import fs from 'fs-extra';
import path from 'path';
import config from '../config.js';

class AuthLogger {
  static async log(event, data = {}) {
    try {
      await fs.ensureFile(config.database.logsPath);
      
      const logEntry = {
        id: this.generateId(),
        timestamp: new Date().toISOString(),
        event,
        data: {
          ...data,
          ip: data.ip || 'unknown',
          userAgent: data.userAgent || 'unknown'
        }
      };

      // Ler logs existentes
      let logs = [];
      try {
        const existingLogs = await fs.readFile(config.database.logsPath, 'utf8');
        logs = existingLogs ? JSON.parse(existingLogs) : [];
      } catch (error) {
        logs = [];
      }

      // Adicionar novo log
      logs.push(logEntry);

      // Manter apenas os últimos 1000 logs para não crescer muito
      if (logs.length > 1000) {
        logs = logs.slice(-1000);
      }

      // Salvar logs
      await fs.writeFile(config.database.logsPath, JSON.stringify(logs, null, 2));
      
      // Log no console também
      console.log(`[AUTH] ${event}:`, {
        userId: data.userId,
        email: data.email,
        ip: data.ip,
        success: data.success
      });

    } catch (error) {
      console.error('Erro ao salvar log de autenticação:', error);
    }
  }

  static async logLogin(userId, email, success, ip, userAgent, details = {}) {
    await this.log('LOGIN_ATTEMPT', {
      userId,
      email,
      success,
      ip,
      userAgent,
      ...details
    });
  }

  static async logLogout(userId, email, ip, userAgent) {
    await this.log('LOGOUT', {
      userId,
      email,
      ip,
      userAgent
    });
  }

  static async logPasswordChange(userId, email, ip, userAgent) {
    await this.log('PASSWORD_CHANGE', {
      userId,
      email,
      ip,
      userAgent
    });
  }

  static async logPasswordReset(email, ip, userAgent, success = true) {
    await this.log('PASSWORD_RESET', {
      email,
      ip,
      userAgent,
      success
    });
  }

  static async logUserCreated(userId, email, createdBy, ip, userAgent) {
    await this.log('USER_CREATED', {
      userId,
      email,
      createdBy,
      ip,
      userAgent
    });
  }

  static async logUserDeleted(userId, email, deletedBy, ip, userAgent) {
    await this.log('USER_DELETED', {
      userId,
      email,
      deletedBy,
      ip,
      userAgent
    });
  }

  static async logPermissionChange(userId, email, changedBy, oldRole, newRole, ip, userAgent) {
    await this.log('PERMISSION_CHANGE', {
      userId,
      email,
      changedBy,
      oldRole,
      newRole,
      ip,
      userAgent
    });
  }

  static async logSecurityEvent(event, userId, email, ip, userAgent, details = {}) {
    await this.log(`SECURITY_${event}`, {
      userId,
      email,
      ip,
      userAgent,
      ...details
    });
  }

  static async logProfileUpdate(userId, email, ip, userAgent) {
    await this.log('PROFILE_UPDATE', {
      userId,
      email,
      ip,
      userAgent
    });
  }

  static async getLogs(filters = {}) {
    try {
      await fs.ensureFile(config.database.logsPath);
      const data = await fs.readFile(config.database.logsPath, 'utf8');
      let logs = data ? JSON.parse(data) : [];

      // Aplicar filtros
      if (filters.event) {
        logs = logs.filter(log => log.event === filters.event);
      }

      if (filters.userId) {
        logs = logs.filter(log => log.data.userId === filters.userId);
      }

      if (filters.email) {
        logs = logs.filter(log => log.data.email === filters.email);
      }

      if (filters.ip) {
        logs = logs.filter(log => log.data.ip === filters.ip);
      }

      if (filters.startDate) {
        logs = logs.filter(log => new Date(log.timestamp) >= new Date(filters.startDate));
      }

      if (filters.endDate) {
        logs = logs.filter(log => new Date(log.timestamp) <= new Date(filters.endDate));
      }

      if (filters.success !== undefined) {
        logs = logs.filter(log => log.data.success === filters.success);
      }

      // Ordenar por timestamp (mais recente primeiro)
      logs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

      // Limitar resultados
      const limit = filters.limit || 100;
      return logs.slice(0, limit);

    } catch (error) {
      console.error('Erro ao buscar logs:', error);
      return [];
    }
  }

  static async getLoginAttempts(email, timeWindow = 15 * 60 * 1000) {
    const logs = await this.getLogs({
      event: 'LOGIN_ATTEMPT',
      email,
      startDate: new Date(Date.now() - timeWindow)
    });

    return {
      total: logs.length,
      failed: logs.filter(log => !log.data.success).length,
      successful: logs.filter(log => log.data.success).length,
      lastAttempt: logs.length > 0 ? logs[0].timestamp : null
    };
  }

  static async getSecuritySummary(timeWindow = 24 * 60 * 60 * 1000) {
    const logs = await this.getLogs({
      startDate: new Date(Date.now() - timeWindow)
    });

    const summary = {
      totalEvents: logs.length,
      loginAttempts: logs.filter(log => log.event === 'LOGIN_ATTEMPT').length,
      failedLogins: logs.filter(log => log.event === 'LOGIN_ATTEMPT' && !log.data.success).length,
      successfulLogins: logs.filter(log => log.event === 'LOGIN_ATTEMPT' && log.data.success).length,
      passwordChanges: logs.filter(log => log.event === 'PASSWORD_CHANGE').length,
      passwordResets: logs.filter(log => log.event === 'PASSWORD_RESET').length,
      userCreations: logs.filter(log => log.event === 'USER_CREATED').length,
      securityEvents: logs.filter(log => log.event.startsWith('SECURITY_')).length,
      uniqueIPs: [...new Set(logs.map(log => log.data.ip))].length,
      uniqueUsers: [...new Set(logs.map(log => log.data.userId).filter(Boolean))].length
    };

    return summary;
  }

  static generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}

export default AuthLogger;