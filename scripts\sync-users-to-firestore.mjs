import { initializeApp } from 'firebase/app';
import { getFirestore, collection, doc, setDoc, serverTimestamp, getDocs, deleteDoc } from 'firebase/firestore';
import { readFileSync } from 'fs';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Configuração do Firebase
const FIREBASE_CONFIG = {
  apiKey: "AIzaSyB04qDOcU5jdZAYy-yaYQRUrrYQ_l49vcY",
  authDomain: "promandato-9a4cf.firebaseapp.com",
  projectId: "promandato-9a4cf",
  storageBucket: "promandato-9a4cf.firebasestorage.app",
  messagingSenderId: "517140455601",
  appId: "1:517140455601:web:fa2eb0ec2f88b506594290",
  measurementId: "G-MXYM2GKJS5"
};

// Inicializa o Firebase
const app = initializeApp(FIREBASE_CONFIG);
const db = getFirestore(app);

// Função para ler usuários do backend
function readBackendUsers() {
  try {
    const usersPath = join(__dirname, 'backend', 'data', 'users.json');
    const usersData = readFileSync(usersPath, 'utf8');
    return JSON.parse(usersData);
  } catch (error) {
    console.error('❌ Erro ao ler arquivo de usuários do backend:', error);
    return [];
  }
}

// Função para mapear plano do backend para formato do Firestore
function mapPlanId(planId) {
  if (!planId) return 'basic';
  
  switch (planId.toUpperCase()) {
    case 'BASIC': return 'basic';
    case 'STANDARD': return 'standard';
    case 'PROFESSIONAL': return 'professional';
    default: return 'basic';
  }
}

// Função para mapear role do backend para formato do Firestore
function mapRole(role) {
  if (!role) return 'staff';
  
  switch (role.toUpperCase()) {
    case 'ADMIN': return 'admin';
    case 'USER': return 'staff';
    case 'MANAGER': return 'master';
    default: return 'staff';
  }
}

// Função principal para sincronizar usuários
async function syncUsersToFirestore() {
  console.log('🚀 Iniciando sincronização de usuários do backend para o Firestore...\n');

  try {
    // Ler usuários do backend
    const backendUsers = readBackendUsers();
    console.log(`📁 Encontrados ${backendUsers.length} usuários no backend`);

    if (backendUsers.length === 0) {
      console.log('⚠️ Nenhum usuário encontrado no backend. Verifique o arquivo backend/data/users.json');
      return;
    }

    // Verificar usuários existentes no Firestore
    const usersCollection = collection(db, 'users');
    const existingSnapshot = await getDocs(usersCollection);
    const existingEmails = new Set();
    
    existingSnapshot.forEach((doc) => {
      const userData = doc.data();
      if (userData.email) {
        existingEmails.add(userData.email);
      }
    });

    console.log(`🔍 Encontrados ${existingEmails.size} usuários já existentes no Firestore`);

    // Sincronizar cada usuário
    let syncedCount = 0;
    let skippedCount = 0;

    for (const backendUser of backendUsers) {
      try {
        if (existingEmails.has(backendUser.email)) {
          console.log(`⏭️ Usuário já existe no Firestore: ${backendUser.email}`);
          skippedCount++;
          continue;
        }

        // Preparar dados do usuário para o Firestore
        const firestoreUser = {
          id: backendUser.id,
          name: backendUser.name,
          email: backendUser.email,
          role: mapRole(backendUser.role),
          planId: mapPlanId(backendUser.planId),
          isActive: backendUser.isActive || true,
          emailVerified: backendUser.emailVerified || false,
          department: backendUser.profile?.department || 'Não informado',
          position: backendUser.profile?.position || 'Não informado',
          phone: backendUser.profile?.phone || null,
          avatar: backendUser.profile?.avatar || null,
          lastLogin: backendUser.lastLogin || null,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        };

        // Adicionar ao Firestore
        await setDoc(doc(db, 'users', backendUser.id), firestoreUser);
        
        console.log(`✅ Usuário sincronizado: ${backendUser.email} (${firestoreUser.role}, ${firestoreUser.planId})`);
        syncedCount++;

      } catch (error) {
        console.error(`❌ Erro ao sincronizar usuário ${backendUser.email}:`, error.message);
      }
    }

    console.log('\n🎉 Sincronização concluída!');
    console.log(`📊 Resumo:`);
    console.log(`   • Usuários sincronizados: ${syncedCount}`);
    console.log(`   • Usuários já existentes: ${skippedCount}`);
    console.log(`   • Total no backend: ${backendUsers.length}`);

    // Verificar resultado final
    const finalSnapshot = await getDocs(usersCollection);
    console.log(`   • Total no Firestore: ${finalSnapshot.size}`);

    console.log('\n💡 Agora você pode testar a página "Clientes Online" com dados reais!');
    console.log('🔗 Acesse: http://localhost:3000/#/online-clients');

  } catch (error) {
    console.error('❌ Erro na sincronização:', error);
  }
}

// Função para limpar usuários do Firestore (útil para reset)
async function clearFirestoreUsers() {
  console.log('🧹 Limpando usuários do Firestore...');
  
  try {
    const usersCollection = collection(db, 'users');
    const snapshot = await getDocs(usersCollection);
    
    const deletePromises = [];
    snapshot.forEach((doc) => {
      deletePromises.push(deleteDoc(doc.ref));
    });
    
    await Promise.all(deletePromises);
    console.log(`✅ ${snapshot.size} usuários removidos do Firestore`);
  } catch (error) {
    console.error('❌ Erro ao limpar usuários:', error);
  }
}

// Verificar argumentos da linha de comando
const args = process.argv.slice(2);

if (args.includes('--clear')) {
  clearFirestoreUsers().then(() => process.exit(0));
} else {
  syncUsersToFirestore().then(() => process.exit(0));
}
