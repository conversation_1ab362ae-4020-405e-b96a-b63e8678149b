
import React from 'react';
import { NavLink } from 'react-router-dom';
import { NAV_ITEMS, APP_NAME, ICONS } from '../../constants';
import { useAuth } from '../../hooks/useAuth';
import { UserRole } from '../../types';

interface SidebarProps {
  isSidebarOpen: boolean;
  toggleSidebar: () => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ isSidebarOpen, toggleSidebar }) => {
  const { currentUser } = useAuth();

  const filteredNavItems = NAV_ITEMS.filter(item => {
    if (!item.allowedRoles) return true; // No specific roles required
    if (!currentUser) return false; // User not logged in, shouldn't see role-specific items
    return item.allowedRoles.includes(currentUser.role);
  });

  return (
    <>
      {/* Overlay for mobile */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 z-30 bg-black opacity-50 lg:hidden dark:bg-opacity-70"
          onClick={toggleSidebar}
        ></div>
      )}

      <aside
        className={`fixed top-0 left-0 z-40 w-64 h-screen bg-neutral-darker text-white transition-transform transform ${
          isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
        } lg:translate-x-0 lg:static lg:inset-y-0 dark:bg-neutral-darkest`}
      >
        <div className="flex items-center justify-center h-16 border-b border-neutral-dark dark:border-neutral-darker shadow-md bg-primary-dark dark:bg-primary-dark">
          {/* Replace with a proper logo */}
          <span className="text-2xl font-bold tracking-tight flex items-center text-white">
            {React.cloneElement(ICONS.TEAM, { className: "text-white"})} <span className="ml-2">{APP_NAME}</span>
          </span>
        </div>
        <nav className="mt-4 px-2 space-y-1">
          {filteredNavItems.map((item) => (
            <NavLink
              key={item.label}
              to={item.path}
              onClick={() => { if (window.innerWidth < 1024) toggleSidebar(); }} // Close sidebar on mobile nav click
              className={({ isActive }) =>
                `group flex items-center px-3 py-2.5 text-sm font-medium rounded-md transition-colors
                 ${isActive
                   ? 'bg-primary text-white shadow-inner dark:bg-primary-dark'
                   : 'text-gray-300 dark:text-neutral-DEFAULT hover:bg-neutral-dark dark:hover:bg-neutral-darker hover:text-white dark:hover:text-neutral-extralight'
                 }`
              }
            >
              <span className="mr-3">{item.icon}</span>
              {item.label}
            </NavLink>
          ))}
        </nav>
        <div className="absolute bottom-0 w-full p-4 border-t border-neutral-DEFAULT dark:border-neutral-dark">
           <p className="text-xs text-gray-400 dark:text-neutral-medium">&copy; {new Date().getFullYear()} {APP_NAME}</p>
        </div>
      </aside>
    </>
  );
};