@echo off
setlocal enabledelayedexpansion

echo ========================================
echo     ASSISTENTE GODADDY - PROMANDATO
echo     Configuracao DNS Simplificada
echo ========================================
echo.

:MENU
echo ESCOLHA UMA OPCAO:
echo.
echo [1] Fazer deploy e preparar Firebase
echo [2] Verificar status atual do DNS
echo [3] Mostrar valores para GoDaddy
echo [4] Testar dominio completo
echo [5] Abrir guias de ajuda
echo [6] Troubleshooting
echo [0] Sair
echo.
set /p CHOICE="Digite sua escolha (0-6): "

if "%CHOICE%"=="1" goto DEPLOY
if "%CHOICE%"=="2" goto CHECK_DNS
if "%CHOICE%"=="3" goto SHOW_VALUES
if "%CHOICE%"=="4" goto TEST_DOMAIN
if "%CHOICE%"=="5" goto OPEN_GUIDES
if "%CHOICE%"=="6" goto TROUBLESHOOT
if "%CHOICE%"=="0" goto EXIT
goto MENU

:DEPLOY
echo.
echo ========================================
echo     FAZENDO DEPLOY E PREPARACAO
echo ========================================
echo.

echo [1/3] Executando deploy customizado...
call scripts\deploy-custom-domain.bat

echo.
echo [2/3] Abrindo Firebase Console...
start https://console.firebase.google.com/project/promandato-9a4cf/hosting

echo.
echo [3/3] Instrucoes:
echo.
echo No Firebase Console que abriu:
echo   1. Clique em "Adicionar dominio personalizado"
echo   2. Digite: www.promandato.com.br
echo   3. Anote os valores TXT e A fornecidos
echo   4. Volte aqui e escolha opcao [3]
echo.

pause
goto MENU

:CHECK_DNS
echo.
echo ========================================
echo     VERIFICANDO STATUS DNS
echo ========================================
echo.

call scripts\check-godaddy-dns.bat
goto MENU

:SHOW_VALUES
echo.
echo ========================================
echo     VALORES PARA CONFIGURAR GODADDY
echo ========================================
echo.

echo COPIE ESTES VALORES PARA O GODADDY:
echo.
echo =========================================
echo REGISTRO TXT (Verificacao)
echo =========================================
echo Type: TXT
echo Name: www
echo Value: firebase-hosting-verification=
echo        [VALOR DO FIREBASE]
echo TTL: 1 Hour
echo =========================================
echo.
echo =========================================
echo REGISTROS A (Apos verificacao)
echo =========================================
echo Primeiro registro A:
echo Type: A
echo Name: www
echo Value: *************
echo TTL: 1 Hour
echo.
echo Segundo registro A:
echo Type: A
echo Name: www
echo Value: **************
echo TTL: 1 Hour
echo =========================================
echo.
echo IMPORTANTE: Use os valores EXATOS do Firebase!
echo Os IPs acima sao exemplos comuns, mas podem variar.
echo.
echo Para configurar no GoDaddy:
echo   1. Acesse: https://dcc.godaddy.com/
echo   2. Va em DNS do dominio promandato.com.br
echo   3. Remova registros existentes para "www"
echo   4. Adicione os registros acima
echo.

set /p OPEN_GODADDY="Abrir GoDaddy agora? (s/n): "
if /i "%OPEN_GODADDY%"=="s" start https://dcc.godaddy.com/

pause
goto MENU

:TEST_DOMAIN
echo.
echo ========================================
echo     TESTE COMPLETO DO DOMINIO
echo ========================================
echo.

echo Executando bateria completa de testes...
call scripts\test-custom-domain.bat
goto MENU

:OPEN_GUIDES
echo.
echo ========================================
echo     ABRINDO GUIAS DE AJUDA
echo ========================================
echo.

echo Abrindo documentacao...

if exist "GODADDY_SETUP_GUIDE.md" (
    start notepad "GODADDY_SETUP_GUIDE.md"
    echo Guia GoDaddy aberto
) else (
    echo Guia GoDaddy nao encontrado
)

if exist "GODADDY_VISUAL_GUIDE.md" (
    start notepad "GODADDY_VISUAL_GUIDE.md"
    echo Guia Visual aberto
) else (
    echo Guia Visual nao encontrado
)

if exist "DNS_CONFIGURATION.md" (
    start notepad "DNS_CONFIGURATION.md"
    echo Configuracao DNS aberta
) else (
    echo Configuracao DNS nao encontrada
)

echo.
echo Links uteis tambem abertos no navegador:
start https://dcc.godaddy.com/
start https://console.firebase.google.com/project/promandato-9a4cf/hosting

pause
goto MENU

:TROUBLESHOOT
echo.
echo ========================================
echo     TROUBLESHOOTING GODADDY
echo ========================================
echo.

echo DIAGNOSTICO RAPIDO:
echo.

:: Verificar se dominio resolve
nslookup www.promandato.com.br >nul 2>&1
if %errorlevel% equ 0 (
    echo DNS basico funcionando
) else (
    echo DNS nao resolve - Problema na configuracao GoDaddy
)

:: Verificar TXT
nslookup -type=TXT www.promandato.com.br | findstr "firebase" >nul 2>&1
if %errorlevel% equ 0 (
    echo Registro TXT encontrado
) else (
    echo Registro TXT nao encontrado
)

:: Verificar HTTPS
curl -s -o nul -w "%%{http_code}" https://www.promandato.com.br/ >temp_status.txt 2>nul
set /p STATUS=<temp_status.txt
del temp_status.txt >nul 2>&1

if "%STATUS%"=="200" (
    echo HTTPS funcionando
) else if "%STATUS%"=="301" (
    echo HTTPS com redirecionamento
) else (
    echo HTTPS nao funcionando - Status: %STATUS%
)

echo.
echo SOLUCOES COMUNS:
echo.
echo DNS nao resolve:
echo   - Verificar registros A no GoDaddy
echo   - Aguardar propagacao (1-4 horas)
echo   - Contatar suporte GoDaddy: 0800 606 4656
echo.
echo TXT nao encontrado:
echo   - Adicionar registro TXT no GoDaddy
echo   - Aguardar 10-15 minutos
echo   - Verificar valor exato do Firebase
echo.
echo HTTPS nao funciona:
echo   - Aguardar SSL ser provisionado (ate 24h)
echo   - Verificar se dominio foi verificado no Firebase
echo   - Limpar cache do navegador
echo.

echo CONTATOS DE SUPORTE:
echo   GoDaddy: 0800 606 4656
echo   Firebase: Console - Support
echo.

pause
goto MENU

:EXIT
echo.
echo ========================================
echo     OBRIGADO POR USAR O ASSISTENTE!
echo ========================================
echo.
echo Documentacao disponivel:
echo   - GODADDY_SETUP_GUIDE.md
echo   - GODADDY_VISUAL_GUIDE.md
echo   - DNS_CONFIGURATION.md
echo.
echo Objetivo: www.promandato.com.br funcionando
echo.
echo Em caso de duvidas:
echo   Execute novamente: scripts\godaddy-helper.bat
echo.
pause
exit /b 0
