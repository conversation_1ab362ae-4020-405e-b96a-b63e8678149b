export enum PlanType {
  BASIC = 'basic',
  STANDARD = 'standard',
  PROFESSIONAL = 'professional'
}

export interface PlanPrice {
  monthly: number;
  yearly: number;
}

export interface PlanFeatures {
  maxUsers: number;
  maxDemands: number;
  maxCitizens: number;
  maxDocuments: number;
  storageGB: number;
  bulkMessages: boolean;
  socialMediaIntegration: boolean;
  emailNotifications: boolean;
  smsNotifications: boolean;
  basicReports: boolean;
  advancedReports: boolean;
  customReports: boolean;
  dataExport: boolean;
  aiFeatures: AIFeature[];
  apiAccess: boolean;
  webhooks: boolean;
  thirdPartyIntegrations: string[];
  supportLevel: 'basic' | 'priority' | 'dedicated';
  supportChannels: string[];
}

export interface AIFeature {
  id: string;
  name: string;
  description: string;
  category: string;
  enabled: boolean;
}

export interface Plan {
  id: PlanType;
  name: string;
  description: string;
  price: PlanPrice;
  features: PlanFeatures;
  popular?: boolean;
  badge?: string;
  enabled: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface PlanUpdateRequest {
  planId: PlanType;
  updates: Partial<Plan>;
}

export interface PlanResponse {
  success: boolean;
  message: string;
  data?: Plan | Plan[];
  error?: string;
}