import { initializeApp } from 'firebase/app';
import {
  getFirestore,
  collection,
  getDocs,
  query,
  orderBy,
  onSnapshot,
  doc,
  getDoc,
  setDoc,
  serverTimestamp
} from 'firebase/firestore';

// Configuração do Firebase (mesma da aplicação principal)
const FIREBASE_CONFIG = {
  apiKey: "AIzaSyB04qDOcU5jdZAYy-yaYQRUrrYQ_l49vcY",
  authDomain: "promandato-9a4cf.firebaseapp.com",
  projectId: "promandato-9a4cf",
  storageBucket: "promandato-9a4cf.firebasestorage.app",
  messagingSenderId: "517140455601",
  appId: "1:517140455601:web:fa2eb0ec2f88b506594290",
  measurementId: "G-MXYM2GKJS5"
};

// Inicializar Firebase
const app = initializeApp(FIREBASE_CONFIG);
const db = getFirestore(app);

// Interfaces
export interface FirestoreUser {
  id: string;
  name: string;
  email: string;
  role: string;
  createdAt?: any;
  updatedAt?: any;
}

export interface OnlineUser {
  id: string;
  name: string;
  email: string;
  role: string;
  status: 'online' | 'away' | 'busy' | 'offline';
  lastActivity: string;
  sessionStart: string;
  sessionDuration: number;
  device?: string;
  browser?: string;
  location?: string;
  currentPage?: string;
  actionsToday: number;
  ipAddress?: string;
}

export interface ClientStatus {
  id: string;
  name: string;
  email: string;
  role: string;
  plan: 'basic' | 'standard' | 'professional';
  status: 'online' | 'offline';
  lastActivity?: string;
  sessionDuration?: number;
  device?: string;
  browser?: string;
  location?: string;
  currentPage?: string;
  actionsToday?: number;
  ipAddress?: string;
  organization?: string;
}

// Função para obter todos os usuários reais do sistema
export const getAllUsers = async (): Promise<FirestoreUser[]> => {
  try {
    console.log('🔍 Buscando todos os usuários reais do sistema...');

    const usersCollection = collection(db, 'users');
    const usersSnapshot = await getDocs(usersCollection);

    const users: FirestoreUser[] = [];

    if (usersSnapshot.empty) {
      console.log('⚠️ Nenhum usuário encontrado na coleção users do Firestore');
      console.log('💡 Criando usuários demo baseados nos dados reais do backend...');

      // Criar usuários demo baseados nos dados reais do backend
      const demoUsers = [
        {
          id: 'demo-basic',
          name: 'Usuário Básico',
          email: '<EMAIL>',
          role: 'staff',
          planId: 'BASIC',
          department: 'Demonstração',
          position: 'Usuário de Teste - Plano Básico'
        },
        {
          id: 'demo-standard',
          name: 'Usuário Padrão',
          email: '<EMAIL>',
          role: 'staff',
          planId: 'STANDARD',
          department: 'Demonstração',
          position: 'Usuário de Teste - Plano Padrão'
        },
        {
          id: 'demo-professional',
          name: 'Usuário Profissional',
          email: '<EMAIL>',
          role: 'admin',
          planId: 'PROFESSIONAL',
          department: 'Demonstração',
          position: 'Usuário de Teste - Plano Profissional'
        },
        {
          id: 'demo-admin',
          name: 'Administrador',
          email: '<EMAIL>',
          role: 'admin',
          planId: 'PROFESSIONAL',
          department: 'Administração',
          position: 'Administrador do Sistema'
        }
      ];

      // Adicionar usuários demo ao Firestore
      for (const user of demoUsers) {
        try {
          await setDoc(doc(db, 'users', user.id), {
            ...user,
            createdAt: serverTimestamp(),
            updatedAt: serverTimestamp()
          });
          users.push({
            id: user.id,
            name: user.name,
            email: user.email,
            role: user.role,
            createdAt: new Date(),
            updatedAt: new Date()
          });
          console.log(`✅ Usuário demo criado: ${user.email}`);
        } catch (error) {
          console.error(`❌ Erro ao criar usuário demo ${user.email}:`, error);
        }
      }
    } else {
      // Processar usuários existentes
      usersSnapshot.forEach((doc) => {
        const userData = doc.data();
        users.push({
          id: doc.id,
          name: userData.name || userData.fullName || 'Nome não informado',
          email: userData.email || 'Email não informado',
          role: userData.role || 'staff',
          createdAt: userData.createdAt,
          updatedAt: userData.updatedAt
        });
      });
    }

    console.log(`✅ Encontrados ${users.length} usuários no Firestore`);
    return users;
  } catch (error) {
    console.error('❌ Erro ao buscar usuários:', error);
    throw error;
  }
};

// Função para obter usuários online
export const getOnlineUsers = async (): Promise<OnlineUser[]> => {
  try {
    console.log('🔍 Buscando usuários online...');
    
    const onlineUsersCollection = collection(db, 'online_users');
    const onlineUsersQuery = query(onlineUsersCollection, orderBy('lastActivity', 'desc'));
    const onlineSnapshot = await getDocs(onlineUsersQuery);
    
    const onlineUsers: OnlineUser[] = [];
    onlineSnapshot.forEach((doc) => {
      const userData = doc.data();
      
      // Calcular duração da sessão
      const sessionStart = new Date(userData.sessionStart);
      const now = new Date();
      const sessionDuration = Math.floor((now.getTime() - sessionStart.getTime()) / (1000 * 60));
      
      // Verificar se ainda está online (última atividade < 2 minutos)
      const lastActivity = new Date(userData.lastActivity);
      const timeSinceLastActivity = now.getTime() - lastActivity.getTime();
      const isOnline = timeSinceLastActivity < 2 * 60 * 1000; // 2 minutos
      
      onlineUsers.push({
        id: doc.id,
        name: userData.name || 'Nome não informado',
        email: userData.email || 'Email não informado',
        role: userData.role || 'staff',
        status: isOnline ? (userData.status || 'online') : 'offline',
        lastActivity: userData.lastActivity,
        sessionStart: userData.sessionStart,
        sessionDuration,
        device: userData.device,
        browser: userData.browser,
        location: userData.location,
        currentPage: userData.currentPage,
        actionsToday: userData.actionsToday || 0,
        ipAddress: userData.ipAddress
      });
    });
    
    console.log(`✅ Encontrados ${onlineUsers.length} usuários online`);
    return onlineUsers;
  } catch (error) {
    console.error('❌ Erro ao buscar usuários online:', error);
    throw error;
  }
};

// Função para combinar dados de usuários e status online
export const getClientsStatus = async (): Promise<ClientStatus[]> => {
  try {
    console.log('🔄 Combinando dados de usuários e status online...');
    
    const [allUsers, onlineUsers] = await Promise.all([
      getAllUsers(),
      getOnlineUsers()
    ]);
    
    const clientsStatus: ClientStatus[] = allUsers.map(user => {
      // Procurar se o usuário está online
      const onlineData = onlineUsers.find(online => online.id === user.id || online.email === user.email);

      // Determinar plano baseado no email (dados reais do backend)
      let plan: 'basic' | 'standard' | 'professional' = 'basic';
      if (user.email === '<EMAIL>') {
        plan = 'basic';
      } else if (user.email === '<EMAIL>') {
        plan = 'standard';
      } else if (user.email === '<EMAIL>') {
        plan = 'professional';
      } else if (user.role === 'admin') {
        plan = 'professional';
      } else if (user.role === 'master') {
        plan = 'professional';
      } else if (user.role === 'staff') {
        plan = 'standard';
      }

      // Determinar organização baseada no email e plano
      let organization = 'Sistema Promandato';
      if (user.email.includes('promandato.com.br')) {
        if (plan === 'basic') organization = 'Demo - Plano Básico';
        else if (plan === 'standard') organization = 'Demo - Plano Padrão';
        else if (plan === 'professional') organization = 'Demo - Plano Profissional';
      } else if (user.email.includes('prefeitura')) {
        organization = 'Prefeitura';
      } else if (user.email.includes('camara')) {
        organization = 'Câmara Municipal';
      } else if (user.email.includes('assembleia')) {
        organization = 'Assembleia Legislativa';
      }

      return {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        plan,
        status: onlineData ? onlineData.status : 'offline',
        lastActivity: onlineData?.lastActivity || new Date().toISOString(),
        sessionDuration: onlineData?.sessionDuration || 0,
        device: onlineData?.device || 'Não informado',
        browser: onlineData?.browser || 'Não informado',
        location: onlineData?.location || 'Não informada',
        currentPage: onlineData?.currentPage || '/',
        actionsToday: onlineData?.actionsToday || 0,
        ipAddress: onlineData?.ipAddress || 'Não informado',
        organization
      };
    });
    
    console.log(`✅ Status combinado para ${clientsStatus.length} clientes`);
    return clientsStatus;
  } catch (error) {
    console.error('❌ Erro ao combinar status dos clientes:', error);
    throw error;
  }
};

// Listener em tempo real para usuários online
export const subscribeToOnlineUsers = (callback: (users: OnlineUser[]) => void) => {
  console.log('🔄 Configurando listener para usuários online...');
  
  const onlineUsersCollection = collection(db, 'online_users');
  const onlineUsersQuery = query(onlineUsersCollection, orderBy('lastActivity', 'desc'));
  
  return onSnapshot(onlineUsersQuery, (snapshot) => {
    console.log('📡 Snapshot recebido:', snapshot.size, 'usuários online');
    
    const onlineUsers: OnlineUser[] = [];
    const now = new Date();
    
    snapshot.forEach((doc) => {
      const userData = doc.data();
      
      // Calcular duração da sessão
      const sessionStart = new Date(userData.sessionStart);
      const sessionDuration = Math.floor((now.getTime() - sessionStart.getTime()) / (1000 * 60));
      
      // Verificar se ainda está online
      const lastActivity = new Date(userData.lastActivity);
      const timeSinceLastActivity = now.getTime() - lastActivity.getTime();
      const isOnline = timeSinceLastActivity < 2 * 60 * 1000; // 2 minutos
      
      onlineUsers.push({
        id: doc.id,
        name: userData.name || 'Nome não informado',
        email: userData.email || 'Email não informado',
        role: userData.role || 'staff',
        status: isOnline ? (userData.status || 'online') : 'offline',
        lastActivity: userData.lastActivity,
        sessionStart: userData.sessionStart,
        sessionDuration,
        device: userData.device,
        browser: userData.browser,
        location: userData.location,
        currentPage: userData.currentPage,
        actionsToday: userData.actionsToday || 0,
        ipAddress: userData.ipAddress
      });
    });
    
    callback(onlineUsers);
  });
};

export { db };
