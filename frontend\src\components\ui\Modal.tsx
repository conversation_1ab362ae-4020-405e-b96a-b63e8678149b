
import React from 'react';
import { ModalProps } from '../../types';

export const Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children, size = 'md' }) => {
  console.log("Modal props:", { isOpen, title, size });
  
  if (!isOpen) return null;

  const sizeClasses = {
    sm: 'sm:max-w-sm',
    md: 'sm:max-w-md',
    lg: 'sm:max-w-lg',
    xl: 'sm:max-w-xl',
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
      <div className="flex items-end justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
            className="fixed inset-0 bg-gray-500 dark:bg-neutral-darkest bg-opacity-75 dark:bg-opacity-80 transition-opacity" 
            aria-hidden="true" 
            onClick={onClose}>
        </div>

        {/* Modal panel */}
        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div className={`inline-block align-bottom bg-white dark:bg-neutral-darker rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle w-full ${sizeClasses[size]}`}>
          {title && (
            <div className="bg-white dark:bg-neutral-darker px-4 pt-5 pb-4 sm:p-6 sm:pb-4 border-b border-gray-200 dark:border-neutral-dark">
              <h3 className="text-lg leading-6 font-medium text-gray-900 dark:text-neutral-extralight" id="modal-title">
                {title}
              </h3>
            </div>
          )}
          <div className="bg-white dark:bg-neutral-darker px-4 pt-5 pb-4 sm:p-6 text-neutral-dark dark:text-neutral-light">
            {children}
          </div>
          <div className="bg-gray-50 dark:bg-neutral-dark px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse border-t dark:border-neutral-medium">
            <button
              type="button"
              onClick={onClose}
              className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-neutral-medium shadow-sm px-4 py-2 bg-white dark:bg-neutral-darker text-base font-medium text-gray-700 dark:text-neutral-light hover:bg-gray-50 dark:hover:bg-neutral-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-neutral-dark sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Fechar
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
