import { useState } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import { apiService } from '../services/apiService';
import { STRIPE_CONFIG } from '../config/stripe';

const stripePromise = loadStripe(process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY!);

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

interface CheckoutOptions {
  priceId: string;
  planName: string;
  billingCycle: 'month' | 'year';
}

export const useStripeCheckout = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createCheckoutSession = async (options: CheckoutOptions) => {
    setLoading(true);
    setError(null);

    try {
      // Criar sessão de checkout no backend
      const response: ApiResponse<{ sessionId: string }> = await apiService.post('/stripe/create-checkout-session', {
        priceId: options.priceId,
        planName: options.planName,
        billingCycle: options.billingCycle,
        successUrl: STRIPE_CONFIG.SUCCESS_URL,
        cancelUrl: STRIPE_CONFIG.CANCEL_URL
      });

      if (!response.success || !response.data?.sessionId) {
        throw new Error(response.error || 'Erro ao criar sessão de checkout');
      }

      // Redirecionar para o Stripe Checkout
      const stripe = await stripePromise;
      if (!stripe) {
        throw new Error('Stripe não foi carregado');
      }

      const { error: stripeError } = await stripe.redirectToCheckout({
        sessionId: response.data.sessionId.valueOf()
      });

      if (stripeError) {
        throw new Error(stripeError.message);
      }

      return response.data.sessionId;

    } catch (err: any) {
      console.error('Erro no checkout:', err);
      setError(err.message || 'Erro ao processar pagamento');
    } finally {
      setLoading(false);
    }
  };

  const verifyPaymentStatus = async (sessionId: string) => {
    try {
      const response: ApiResponse = await apiService.get(`/stripe/payment-status/${sessionId}`);
      return response.success && response.data;
    } catch (err) {
      console.error('Erro ao verificar status do pagamento:', err);
      return false;
    }
  };

  return {
    createCheckoutSession,
    verifyPaymentStatus,
    loading,
    error,
    clearError: () => setError(null)
  };
};