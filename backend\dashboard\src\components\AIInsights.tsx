import React, { useState, useEffect } from 'react';
import {
  Brain,
  TrendingUp,
  <PERSON><PERSON><PERSON><PERSON>gle,
  CheckCircle,
  Clock,
  BarChart3,
  Lightbulb,
  Zap,
  Target,
  RefreshCw
} from 'lucide-react';

interface AIInsightsProps {
  darkMode: boolean;
}

interface InsightCard {
  id: string;
  title: string;
  description: string;
  type: 'trend' | 'anomaly' | 'suggestion' | 'prediction';
  priority: 'low' | 'medium' | 'high';
  icon: React.ReactNode;
  value?: string | number;
  change?: number;
  action?: string;
}

const AIInsights: React.FC<AIInsightsProps> = ({ darkMode }) => {
  const [insights, setInsights] = useState<InsightCard[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'trends' | 'anomalies' | 'suggestions' | 'predictions'>('all');

  useEffect(() => {
    loadAIInsights();
  }, []);

  const loadAIInsights = async () => {
    setIsLoading(true);
    try {
      // Simular dados de insights de IA
      const mockInsights: InsightCard[] = [
        {
          id: '1',
          title: 'Aumento de Demandas de Saúde',
          description: 'Detectado aumento de 23% nas demandas relacionadas à saúde nos últimos 7 dias.',
          type: 'trend',
          priority: 'high',
          icon: <TrendingUp className="h-5 w-5" />,
          value: '+23%',
          change: 23,
          action: 'Considere alocar mais recursos para o departamento de saúde'
        },
        {
          id: '2',
          title: 'Anomalia no Tempo de Resposta',
          description: 'Tempo médio de resposta 40% acima do normal no setor de infraestrutura.',
          type: 'anomaly',
          priority: 'high',
          icon: <AlertTriangle className="h-5 w-5" />,
          value: '4.2h',
          change: 40,
          action: 'Investigar possíveis gargalos no processo'
        },
        {
          id: '3',
          title: 'Oportunidade de Automação',
          description: '65% das demandas de documentação podem ser automatizadas.',
          type: 'suggestion',
          priority: 'medium',
          icon: <Lightbulb className="h-5 w-5" />,
          value: '65%',
          action: 'Implementar sistema de geração automática de documentos'
        },
        {
          id: '4',
          title: 'Previsão de Demanda',
          description: 'Esperado aumento de 15% nas demandas de educação no próximo mês.',
          type: 'prediction',
          priority: 'medium',
          icon: <Target className="h-5 w-5" />,
          value: '+15%',
          change: 15,
          action: 'Preparar equipe para aumento de demanda'
        },
        {
          id: '5',
          title: 'Melhoria na Satisfação',
          description: 'Implementação de respostas automáticas resultou em +12% de satisfação.',
          type: 'trend',
          priority: 'low',
          icon: <CheckCircle className="h-5 w-5" />,
          value: '+12%',
          change: 12,
          action: 'Expandir uso de respostas automáticas para outros setores'
        },
        {
          id: '6',
          title: 'Padrão de Horário Identificado',
          description: '78% das demandas urgentes chegam entre 8h-10h.',
          type: 'trend',
          priority: 'medium',
          icon: <Clock className="h-5 w-5" />,
          value: '78%',
          action: 'Ajustar escala de atendimento para horários de pico'
        }
      ];

      setInsights(mockInsights);
      setLastUpdate(new Date());
    } catch (error) {
      console.error('Erro ao carregar insights de IA:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredInsights = insights.filter(insight => 
    selectedCategory === 'all' || insight.type === selectedCategory.slice(0, -1)
  );

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/20';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/20';
      case 'low':
        return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/20';
      default:
        return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900/20';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'trend':
        return <BarChart3 className="h-4 w-4" />;
      case 'anomaly':
        return <AlertTriangle className="h-4 w-4" />;
      case 'suggestion':
        return <Lightbulb className="h-4 w-4" />;
      case 'prediction':
        return <Target className="h-4 w-4" />;
      default:
        return <Brain className="h-4 w-4" />;
    }
  };

  return (
    <div className={`p-6 rounded-lg border ${
      darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
    }`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <Brain className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h2 className={`text-xl font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              Insights de IA
            </h2>
            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
              Última atualização: {lastUpdate.toLocaleTimeString('pt-BR')}
            </p>
          </div>
        </div>
        
        <button
          type="button"
          onClick={loadAIInsights}
          disabled={isLoading}
          className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
            darkMode
              ? 'border-gray-600 text-gray-300 hover:bg-gray-700'
              : 'border-gray-300 text-gray-700 hover:bg-gray-50'
          } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
        >
          <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          <span>Atualizar</span>
        </button>
      </div>

      {/* Filtros */}
      <div className="flex flex-wrap gap-2 mb-6">
        {[
          { key: 'all', label: 'Todos', icon: <Brain className="h-4 w-4" /> },
          { key: 'trends', label: 'Tendências', icon: <TrendingUp className="h-4 w-4" /> },
          { key: 'anomalies', label: 'Anomalias', icon: <AlertTriangle className="h-4 w-4" /> },
          { key: 'suggestions', label: 'Sugestões', icon: <Lightbulb className="h-4 w-4" /> },
          { key: 'predictions', label: 'Previsões', icon: <Target className="h-4 w-4" /> }
        ].map((filter) => (
          <button
            type="button"
            key={filter.key}
            onClick={() => setSelectedCategory(filter.key as any)}
            className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
              selectedCategory === filter.key
                ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400'
                : darkMode
                ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
            }`}
          >
            {filter.icon}
            <span>{filter.label}</span>
          </button>
        ))}
      </div>

      {/* Loading State */}
      {isLoading && (
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center space-x-3">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
            <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
              Analisando dados...
            </span>
          </div>
        </div>
      )}

      {/* Insights Grid */}
      {!isLoading && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredInsights.map((insight) => (
            <div
              key={insight.id}
              className={`p-4 rounded-lg border transition-all hover:shadow-md ${
                darkMode
                  ? 'bg-gray-700 border-gray-600 hover:bg-gray-650'
                  : 'bg-gray-50 border-gray-200 hover:bg-white'
              }`}
            >
              {/* Header */}
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <div className={`p-1.5 rounded ${
                    insight.type === 'trend' ? 'bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400' :
                    insight.type === 'anomaly' ? 'bg-red-100 text-red-600 dark:bg-red-900/20 dark:text-red-400' :
                    insight.type === 'suggestion' ? 'bg-yellow-100 text-yellow-600 dark:bg-yellow-900/20 dark:text-yellow-400' :
                    'bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400'
                  }`}>
                    {insight.icon}
                  </div>
                  <div className="flex items-center space-x-1">
                    {getTypeIcon(insight.type)}
                    <span className={`text-xs font-medium ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      {insight.type === 'trend' ? 'Tendência' :
                       insight.type === 'anomaly' ? 'Anomalia' :
                       insight.type === 'suggestion' ? 'Sugestão' : 'Previsão'}
                    </span>
                  </div>
                </div>
                
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(insight.priority)}`}>
                  {insight.priority === 'high' ? 'Alta' :
                   insight.priority === 'medium' ? 'Média' : 'Baixa'}
                </span>
              </div>

              {/* Content */}
              <div className="mb-3">
                <h3 className={`font-medium mb-1 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  {insight.title}
                </h3>
                <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  {insight.description}
                </p>
              </div>

              {/* Value */}
              {insight.value && (
                <div className="mb-3">
                  <span className={`text-2xl font-bold ${
                    insight.change && insight.change > 0 ? 'text-green-600 dark:text-green-400' :
                    insight.change && insight.change < 0 ? 'text-red-600 dark:text-red-400' :
                    'text-blue-600 dark:text-blue-400'
                  }`}>
                    {insight.value}
                  </span>
                </div>
              )}

              {/* Action */}
              {insight.action && (
                <div className={`text-xs p-2 rounded border-l-2 ${
                  insight.priority === 'high' ? 'border-red-400 bg-red-50 dark:bg-red-900/10' :
                  insight.priority === 'medium' ? 'border-yellow-400 bg-yellow-50 dark:bg-yellow-900/10' :
                  'border-green-400 bg-green-50 dark:bg-green-900/10'
                }`}>
                  <div className="flex items-start space-x-2">
                    <Zap className="h-3 w-3 mt-0.5 text-blue-500" />
                    <span className={`${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      {insight.action}
                    </span>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Empty State */}
      {!isLoading && filteredInsights.length === 0 && (
        <div className="text-center py-12">
          <Brain className={`h-12 w-12 mx-auto mb-4 ${darkMode ? 'text-gray-600' : 'text-gray-400'}`} />
          <h3 className={`text-lg font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-900'}`}>
            Nenhum insight encontrado
          </h3>
          <p className={`text-sm ${darkMode ? 'text-gray-500' : 'text-gray-600'}`}>
            Não há insights disponíveis para o filtro selecionado.
          </p>
        </div>
      )}
    </div>
  );
};

export default AIInsights;