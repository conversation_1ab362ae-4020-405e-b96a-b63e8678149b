// Script para limpar cache do Service Worker e resolver problemas de assets

(async function clearServiceWorkerCache() {
  console.log('🧹 Iniciando limpeza do cache do Service Worker...');
  
  try {
    // 1. Desregistrar Service Worker existente
    if ('serviceWorker' in navigator) {
      const registrations = await navigator.serviceWorker.getRegistrations();
      
      for (let registration of registrations) {
        console.log('🗑️ Desregistrando Service Worker:', registration.scope);
        await registration.unregister();
      }
      
      if (registrations.length > 0) {
        console.log(`✅ ${registrations.length} Service Worker(s) desregistrado(s)`);
      } else {
        console.log('ℹ️ Nenhum Service Worker encontrado');
      }
    }
    
    // 2. Limpar todos os caches
    if ('caches' in window) {
      const cacheNames = await caches.keys();
      
      for (let cacheName of cacheNames) {
        console.log('🗑️ Removendo cache:', cacheName);
        await caches.delete(cacheName);
      }
      
      if (cacheNames.length > 0) {
        console.log(`✅ ${cacheNames.length} cache(s) removido(s)`);
      } else {
        console.log('ℹ️ Nenhum cache encontrado');
      }
    }
    
    // 3. Limpar localStorage e sessionStorage
    try {
      localStorage.clear();
      sessionStorage.clear();
      console.log('✅ Storage local limpo');
    } catch (error) {
      console.warn('⚠️ Erro ao limpar storage:', error);
    }
    
    // 4. Limpar IndexedDB (se existir)
    if ('indexedDB' in window) {
      try {
        // Tentar limpar databases conhecidos
        const dbNames = ['firebaseLocalStorageDb', 'firebase-heartbeat-database'];
        
        for (let dbName of dbNames) {
          try {
            const deleteReq = indexedDB.deleteDatabase(dbName);
            deleteReq.onsuccess = () => console.log(`✅ Database ${dbName} removido`);
            deleteReq.onerror = () => console.log(`ℹ️ Database ${dbName} não encontrado`);
          } catch (error) {
            // Ignorar erros de databases que não existem
          }
        }
      } catch (error) {
        console.warn('⚠️ Erro ao limpar IndexedDB:', error);
      }
    }
    
    console.log('✅ Limpeza completa!');
    console.log('🔄 Recarregue a página para aplicar as mudanças');
    
    // Opcional: recarregar automaticamente após 2 segundos
    setTimeout(() => {
      console.log('🔄 Recarregando página...');
      window.location.reload(true);
    }, 2000);
    
  } catch (error) {
    console.error('❌ Erro durante a limpeza:', error);
  }
})();

// Função para executar manualmente
window.clearSWCache = async function() {
  await clearServiceWorkerCache();
};

console.log('💡 Para limpar manualmente, execute: clearSWCache()');
