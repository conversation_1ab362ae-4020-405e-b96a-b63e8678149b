import { Routes, Route } from 'react-router-dom';
import { Layout } from './components/layout/Layout';
import { ROUTE_PATHS } from './constants';
import SettingsPage from './pages/SettingsPage';
import PoliticianProfilePage from './pages/PoliticianProfilePage';
import TeamPage from './pages/TeamPage';
import WorkspaceSettingsPage from './pages/WorkspaceSettingsPage';
import LoginPage from './pages/LoginPage';
import RegisterAfterPaymentPage from './pages/RegisterAfterPaymentPage';
import PaymentSuccessPage from './pages/PaymentSuccessPage';
import { ProtectedRoute } from './components/auth/ProtectedRoute';
import DashboardPage from './pages/DashboardPage';

// Outras importações...

const AppRoutes = () => {
  return (
    <Routes>
      <Route path={ROUTE_PATHS.LOGIN} element={<LoginPage />} />
      <Route path={ROUTE_PATHS.PAYMENT_SUCCESS} element={<PaymentSuccessPage />} />
      <Route path={ROUTE_PATHS.REGISTER_AFTER_PAYMENT} element={<RegisterAfterPaymentPage />} />
      <Route element={<ProtectedRoute><Layout /></ProtectedRoute>}>
        <Route path={ROUTE_PATHS.DASHBOARD} element={<DashboardPage />} />
        {/* Outras rotas... */}

        {/* Configurações */}
        <Route path={ROUTE_PATHS.SETTINGS} element={<SettingsPage />} />
        <Route path={ROUTE_PATHS.POLITICIAN_PROFILE} element={
          <SettingsPage>
            <PoliticianProfilePage />
          </SettingsPage>
        } />
        <Route path={ROUTE_PATHS.TEAM} element={
          <SettingsPage>
            <TeamPage />
          </SettingsPage>
        } />
        <Route path={ROUTE_PATHS.WORKSPACE_SETTINGS} element={
          <SettingsPage>
            <WorkspaceSettingsPage />
          </SettingsPage>
        } />

        {/* Outras rotas... */}
      </Route>
    </Routes>
  );
};

export default AppRoutes;
