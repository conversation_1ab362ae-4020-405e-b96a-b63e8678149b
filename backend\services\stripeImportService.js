import fs from 'fs-extra';
import path from 'path';
import csv from 'csv-parser';
import { fileURLToPath } from 'url';
import User from '../models/User.js';
import { sendEmail } from './emailService.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class StripeImportService {
  constructor() {
    this.importedUsers = [];
    this.errors = [];
    this.stats = {
      total: 0,
      created: 0,
      updated: 0,
      errors: 0
    };
  }

  /**
   * Importar dados do CSV do Stripe
   * @param {string} csvFilePath - Caminho para o arquivo CSV
   * @returns {Promise<Object>} - Resultado da importação
   */
  async importFromCSV(csvFilePath) {
    try {
      console.log('Iniciando importação do CSV do Stripe...');
      
      if (!await fs.pathExists(csvFilePath)) {
        throw new Error('Arquivo CSV não encontrado');
      }

      const csvData = await this.readCSV(csvFilePath);
      console.log(`${csvData.length} registros encontrados no CSV`);

      this.stats.total = csvData.length;

      for (const row of csvData) {
        try {
          await this.processRow(row);
        } catch (error) {
          this.errors.push({
            row: row,
            error: error.message
          });
          this.stats.errors++;
        }
      }

      // Salvar relatório da importação
      await this.saveImportReport();

      console.log('Importação concluída:', this.stats);
      return {
        success: true,
        stats: this.stats,
        errors: this.errors
      };

    } catch (error) {
      console.error('Erro na importação:', error);
      return {
        success: false,
        error: error.message,
        stats: this.stats
      };
    }
  }

  /**
   * Ler arquivo CSV
   * @param {string} filePath - Caminho do arquivo
   * @returns {Promise<Array>} - Dados do CSV
   */
  async readCSV(filePath) {
    return new Promise((resolve, reject) => {
      const results = [];
      
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (data) => results.push(data))
        .on('end', () => resolve(results))
        .on('error', (error) => reject(error));
    });
  }

  /**
   * Processar uma linha do CSV
   * @param {Object} row - Linha do CSV
   */
  async processRow(row) {
    // Mapear campos do CSV do Stripe para nosso formato
    const userData = this.mapStripeData(row);
    
    if (!userData.email || !userData.name) {
      throw new Error('Email ou nome não encontrado');
    }

    // Verificar se o usuário já existe
    const existingUser = await User.findByEmail(userData.email);
    
    if (existingUser) {
      // Atualizar usuário existente
      await this.updateExistingUser(existingUser, userData);
      this.stats.updated++;
    } else {
      // Criar novo usuário
      await this.createNewUser(userData);
      this.stats.created++;
    }
  }

  /**
   * Mapear dados do Stripe para nosso formato
   * @param {Object} row - Linha do CSV
   * @returns {Object} - Dados mapeados
   */
  mapStripeData(row) {
    // Adapte estes campos conforme a estrutura do seu CSV do Stripe
    return {
      email: row['Customer Email'] || row['email'] || row['Email'],
      name: row['Customer Name'] || row['name'] || row['Name'],
      stripeCustomerId: row['Customer ID'] || row['customer_id'] || row['id'],
      stripeSubscriptionId: row['Subscription ID'] || row['subscription_id'],
      planType: this.mapPlanType(row['Product Name'] || row['plan'] || row['Plan']),
      billingCycle: this.mapBillingCycle(row['Billing Cycle'] || row['interval']),
      subscriptionStatus: row['Status'] || row['status'] || 'active',
      amount: this.parseAmount(row['Amount'] || row['amount']),
      currency: row['Currency'] || row['currency'] || 'brl',
      createdAt: row['Created'] || row['created'] || row['Created At'],
      phone: row['Phone'] || row['phone'],
      country: row['Country'] || row['country'],
      // Adicione outros campos conforme necessário
    };
  }

  /**
   * Mapear tipo de plano do Stripe para nosso formato
   * @param {string} stripePlan - Nome do plano no Stripe
   * @returns {string} - Tipo de plano padronizado
   */
  mapPlanType(stripePlan) {
    if (!stripePlan) return 'basic';
    
    const plan = stripePlan.toLowerCase();
    
    if (plan.includes('basic') || plan.includes('básico')) return 'basic';
    if (plan.includes('standard') || plan.includes('padrão')) return 'standard';
    if (plan.includes('professional') || plan.includes('profissional') || plan.includes('premium')) return 'professional';
    
    return 'basic'; // Default
  }

  /**
   * Mapear ciclo de cobrança
   * @param {string} interval - Intervalo do Stripe
   * @returns {string} - Ciclo padronizado
   */
  mapBillingCycle(interval) {
    if (!interval) return 'monthly';
    
    const cycle = interval.toLowerCase();
    if (cycle.includes('year') || cycle.includes('annual') || cycle.includes('anual')) return 'yearly';
    
    return 'monthly';
  }

  /**
   * Converter valor para centavos
   * @param {string|number} amount - Valor
   * @returns {number} - Valor em centavos
   */
  parseAmount(amount) {
    if (!amount) return 0;
    
    // Remove símbolos de moeda e converte para número
    const numericAmount = parseFloat(amount.toString().replace(/[^\d.,]/g, '').replace(',', '.'));
    return Math.round(numericAmount * 100); // Converter para centavos
  }

  /**
   * Criar novo usuário
   * @param {Object} userData - Dados do usuário
   */
  async createNewUser(userData) {
    // Gerar senha temporária
    const tempPassword = this.generateTempPassword();
    
    const newUser = await User.create({
      email: userData.email,
      name: userData.name,
      password: tempPassword,
      role: 'ADMIN', // Usuários importados do Stripe são administradores
      planId: userData.planType,
      isActive: userData.subscriptionStatus === 'active',
      emailVerified: false,
      stripeCustomerId: userData.stripeCustomerId,
      stripeSubscriptionId: userData.stripeSubscriptionId,
      profile: {
        phone: userData.phone,
        country: userData.country
      }
    });

    // Enviar email de boas-vindas
    await this.sendWelcomeEmail(newUser, tempPassword, userData.planType);
    
    this.importedUsers.push({
      action: 'created',
      user: newUser.toSafeJSON(),
      tempPassword
    });
  }

  /**
   * Atualizar usuário existente
   * @param {Object} existingUser - Usuário existente
   * @param {Object} userData - Novos dados
   */
  async updateExistingUser(existingUser, userData) {
    const userInstance = new User(existingUser);
    
    // Atualizar apenas campos relevantes
    userInstance.planId = userData.planType;
    userInstance.stripeCustomerId = userData.stripeCustomerId;
    userInstance.stripeSubscriptionId = userData.stripeSubscriptionId;
    userInstance.isActive = userData.subscriptionStatus === 'active';
    
    if (userData.phone && !userInstance.profile.phone) {
      userInstance.profile.phone = userData.phone;
    }
    
    if (userData.country && !userInstance.profile.country) {
      userInstance.profile.country = userData.country;
    }
    
    await userInstance.save();
    
    this.importedUsers.push({
      action: 'updated',
      user: userInstance.toSafeJSON()
    });
  }

  /**
   * Gerar senha temporária
   * @returns {string} - Senha temporária
   */
  generateTempPassword() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let password = '';
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return password;
  }

  /**
   * Enviar email de boas-vindas
   * @param {Object} user - Usuário
   * @param {string} tempPassword - Senha temporária
   * @param {string} planType - Tipo de plano
   */
  async sendWelcomeEmail(user, tempPassword, planType) {
    const planNames = {
      basic: 'Básico',
      standard: 'Padrão',
      professional: 'Profissional'
    };

    const emailContent = `
      <h2>Bem-vindo ao Pro-Mandato!</h2>
      <p>Olá ${user.name},</p>
      <p>Sua conta foi migrada com sucesso para o Pro-Mandato!</p>
      
      <h3>Dados de Acesso:</h3>
      <p><strong>Email:</strong> ${user.email}</p>
      <p><strong>Senha Temporária:</strong> ${tempPassword}</p>
      <p><strong>Plano:</strong> ${planNames[planType] || planType}</p>
      
      <h3>Próximos Passos:</h3>
      <ol>
        <li>Acesse o sistema: <a href="${process.env.FRONTEND_URL || 'http://localhost:5174'}">Pro-Mandato Dashboard</a></li>
        <li>Faça login com suas credenciais</li>
        <li>Altere sua senha na primeira vez</li>
        <li>Configure seu perfil e equipe</li>
      </ol>
      
      <p>Seus dados de pagamento e assinatura foram preservados.</p>
      <p>Se precisar de ajuda, entre em contato conosco.</p>
      <p>Equipe Pro-Mandato</p>
    `;

    try {
      await sendEmail({
        to: user.email,
        subject: 'Conta Migrada - Pro-Mandato',
        html: emailContent
      });
    } catch (error) {
      console.error('Erro ao enviar email de boas-vindas:', error);
    }
  }

  /**
   * Salvar relatório da importação
   */
  async saveImportReport() {
    const reportPath = path.join(__dirname, '../data/import-reports');
    await fs.ensureDir(reportPath);
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const reportFile = path.join(reportPath, `stripe-import-${timestamp}.json`);
    
    const report = {
      timestamp: new Date().toISOString(),
      stats: this.stats,
      importedUsers: this.importedUsers,
      errors: this.errors
    };
    
    await fs.writeFile(reportFile, JSON.stringify(report, null, 2));
    console.log(`Relatório salvo em: ${reportFile}`);
  }
}

export default StripeImportService;
