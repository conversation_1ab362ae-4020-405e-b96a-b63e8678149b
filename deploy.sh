#!/bin/bash

set -e

echo "🚀 Iniciando deploy do ProMandato..."

# Build do frontend
echo "📦 Building frontend..."
cd frontend
npm ci --only=production
npm run build
cd ..

# Build do backend
echo "📦 Building backend..."
cd backend
npm ci --only=production
cd ..

# Deploy com Docker
echo "🐳 Deploying with Docker..."
docker-compose -f docker-compose.prod.yml down
docker-compose -f docker-compose.prod.yml build --no-cache
docker-compose -f docker-compose.prod.yml up -d

echo "✅ Deploy concluído!"
echo "🔍 Verificando status dos serviços..."
docker-compose -f docker-compose.prod.yml ps

echo "🌐 Aplicação disponível em:"
echo "  - Frontend: http://localhost"
echo "  - Backend: http://localhost:8080"
echo "  - Health Check: http://localhost:8080/api/health"
