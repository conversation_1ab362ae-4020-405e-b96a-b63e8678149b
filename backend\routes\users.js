import express from 'express';
import { body, validationResult, query } from 'express-validator';
import User from '../models/User.js';
import AuthLogger from '../utils/authLogger.js';
import { authenticateToken, requireAdmin, requireOwnerOrAdmin, requirePermission } from '../middleware/auth.js';
import { createUserLimiter } from '../middleware/rateLimiter.js';
import config from '../config.js';

const router = express.Router();

// Aplicar autenticação a todas as rotas
router.use(authenticateToken);

// Validações
const createUserValidation = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Email inválido'),
  body('password')
    .isLength({ min: config.security.passwordMinLength })
    .withMessage(`Senha deve ter pelo menos ${config.security.passwordMinLength} caracteres`)
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Senha deve conter pelo menos uma letra minúscula, uma maiúscula e um número'),
  body('name')
    .isLength({ min: 2, max: 100 })
    .withMessage('Nome deve ter entre 2 e 100 caracteres')
    .trim(),
  body('role')
    .isIn(['USER', 'MANAGER', 'ADMIN'])
    .withMessage('Role inválida')
];

const updateUserValidation = [
  body('name')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('Nome deve ter entre 2 e 100 caracteres')
    .trim(),
  body('role')
    .optional()
    .isIn(['USER', 'MANAGER', 'ADMIN'])
    .withMessage('Role inválida'),
  body('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive deve ser boolean'),
  body('profile.phone')
    .optional()
    .isMobilePhone('pt-BR')
    .withMessage('Telefone inválido'),
  body('profile.department')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Departamento deve ter no máximo 100 caracteres'),
  body('profile.position')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Cargo deve ter no máximo 100 caracteres')
];

const listUsersValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Página deve ser um número inteiro positivo'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limite deve ser entre 1 e 100'),
  query('role')
    .optional()
    .isIn(['USER', 'MANAGER', 'ADMIN'])
    .withMessage('Role inválida'),
  query('isActive')
    .optional()
    .isBoolean()
    .withMessage('isActive deve ser boolean'),
  query('search')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Busca deve ter no máximo 100 caracteres')
];

// Função para extrair IP e User-Agent
const getClientInfo = (req) => ({
  ip: req.ip || 'unknown',
  userAgent: req.get('User-Agent') || 'unknown'
});

// GET /api/users - Listar usuários
router.get('/', requirePermission('users.read'), listUsersValidation, async (req, res) => {
  try {
    // Verificar erros de validação
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Parâmetros inválidos',
        errors: errors.array()
      });
    }

    const {
      page = 1,
      limit = 20,
      role,
      isActive,
      search
    } = req.query;

    // Filtros
    const filters = {};
    if (role) filters.role = role;
    if (isActive !== undefined) filters.isActive = isActive === 'true';
    if (search) filters.search = search;

    // Buscar usuários
    const users = await User.findAll(filters);

    // Paginação
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedUsers = users.slice(startIndex, endIndex);

    // Estatísticas
    const stats = {
      total: users.length,
      active: users.filter(u => u.isActive).length,
      inactive: users.filter(u => !u.isActive).length,
      byRole: {
        ADMIN: users.filter(u => u.role === 'ADMIN').length,
        MANAGER: users.filter(u => u.role === 'MANAGER').length,
        USER: users.filter(u => u.role === 'USER').length
      }
    };

    res.json({
      success: true,
      data: {
        users: paginatedUsers.map(user => user.toSafeJSON()),
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: users.length,
          pages: Math.ceil(users.length / limit),
          hasNext: endIndex < users.length,
          hasPrev: page > 1
        },
        stats
      }
    });

  } catch (error) {
    console.error('Erro ao listar usuários:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// GET /api/users/:id - Buscar usuário por ID
router.get('/:id', requireOwnerOrAdmin, async (req, res) => {
  try {
    const { id } = req.params;

    const user = await User.findById(id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Usuário não encontrado'
      });
    }

    res.json({
      success: true,
      data: {
        user: user.toSafeJSON()
      }
    });

  } catch (error) {
    console.error('Erro ao buscar usuário:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// POST /api/users - Criar usuário
router.post('/', requirePermission('users.create'), createUserLimiter, createUserValidation, async (req, res) => {
  try {
    // Verificar erros de validação
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Dados inválidos',
        errors: errors.array()
      });
    }

    const { email, password, name, role, profile = {} } = req.body;
    const { ip, userAgent } = getClientInfo(req);

    // Verificar se o email já existe
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      return res.status(409).json({
        success: false,
        message: 'Email já está em uso'
      });
    }

    // Verificar se pode criar admin (apenas admin pode criar admin)
    if (role === 'ADMIN' && req.user.role !== 'ADMIN') {
      return res.status(403).json({
        success: false,
        message: 'Apenas administradores podem criar outros administradores'
      });
    }

    // Criar usuário
    const userData = {
      email,
      password,
      name,
      role,
      isActive: true,
      emailVerified: false,
      profile
    };

    const newUser = await User.create(userData);
    await AuthLogger.logUserCreated(newUser.id, email, req.user.id, ip, userAgent);

    res.status(201).json({
      success: true,
      message: 'Usuário criado com sucesso',
      data: {
        user: newUser.toSafeJSON()
      }
    });

  } catch (error) {
    console.error('Erro ao criar usuário:', error);
    res.status(500).json({
      success: false,
      message: error.message || 'Erro interno do servidor'
    });
  }
});

// PUT /api/users/:id - Atualizar usuário
router.put('/:id', requireOwnerOrAdmin, updateUserValidation, async (req, res) => {
  try {
    // Verificar erros de validação
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Dados inválidos',
        errors: errors.array()
      });
    }

    const { id } = req.params;
    const updates = req.body;
    const { ip, userAgent } = getClientInfo(req);

    const user = await User.findById(id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Usuário não encontrado'
      });
    }

    // Verificar permissões para alteração de role
    if (updates.role && updates.role !== user.role) {
      // Apenas admin pode alterar roles
      if (req.user.role !== 'ADMIN') {
        return res.status(403).json({
          success: false,
          message: 'Apenas administradores podem alterar roles'
        });
      }

      // Não pode alterar própria role
      if (req.user.id === user.id) {
        return res.status(403).json({
          success: false,
          message: 'Você não pode alterar sua própria role'
        });
      }

      // Log da mudança de permissão
      await AuthLogger.logPermissionChange(
        user.id, 
        user.email, 
        req.user.id, 
        user.role, 
        updates.role, 
        ip, 
        userAgent
      );
    }

    // Verificar permissões para ativação/desativação
    if (updates.isActive !== undefined && updates.isActive !== user.isActive) {
      // Apenas admin pode ativar/desativar usuários
      if (req.user.role !== 'ADMIN') {
        return res.status(403).json({
          success: false,
          message: 'Apenas administradores podem ativar/desativar usuários'
        });
      }

      // Não pode desativar a si mesmo
      if (req.user.id === user.id && !updates.isActive) {
        return res.status(403).json({
          success: false,
          message: 'Você não pode desativar sua própria conta'
        });
      }
    }

    // Aplicar atualizações
    Object.keys(updates).forEach(key => {
      if (key === 'profile' && typeof updates.profile === 'object') {
        user.profile = { ...user.profile, ...updates.profile };
      } else if (key !== 'password') { // Senha deve ser alterada via endpoint específico
        user[key] = updates[key];
      }
    });

    await user.save();

    res.json({
      success: true,
      message: 'Usuário atualizado com sucesso',
      data: {
        user: user.toSafeJSON()
      }
    });

  } catch (error) {
    console.error('Erro ao atualizar usuário:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// DELETE /api/users/:id - Deletar usuário
router.delete('/:id', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { ip, userAgent } = getClientInfo(req);

    const user = await User.findById(id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Usuário não encontrado'
      });
    }

    // Não pode deletar a si mesmo
    if (req.user.id === user.id) {
      return res.status(403).json({
        success: false,
        message: 'Você não pode deletar sua própria conta'
      });
    }

    await user.delete();
    await AuthLogger.logUserDeleted(user.id, user.email, req.user.id, ip, userAgent);

    res.json({
      success: true,
      message: 'Usuário deletado com sucesso'
    });

  } catch (error) {
    console.error('Erro ao deletar usuário:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// POST /api/users/:id/reset-password - Reset de senha (admin)
router.post('/:id/reset-password', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { newPassword } = req.body;
    const { ip, userAgent } = getClientInfo(req);

    if (!newPassword || newPassword.length < config.security.passwordMinLength) {
      return res.status(400).json({
        success: false,
        message: `Nova senha deve ter pelo menos ${config.security.passwordMinLength} caracteres`
      });
    }

    const user = await User.findById(id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Usuário não encontrado'
      });
    }

    await user.updatePassword(newPassword);
    await AuthLogger.logPasswordChange(user.id, user.email, ip, userAgent);

    res.json({
      success: true,
      message: 'Senha resetada com sucesso'
    });

  } catch (error) {
    console.error('Erro ao resetar senha:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

// POST /api/users/:id/unlock - Desbloquear usuário
router.post('/:id/unlock', requireAdmin, async (req, res) => {
  try {
    const { id } = req.params;
    const { ip, userAgent } = getClientInfo(req);

    const user = await User.findById(id);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'Usuário não encontrado'
      });
    }

    await user.resetLoginAttempts();
    await AuthLogger.logSecurityEvent('ACCOUNT_UNLOCKED', user.id, user.email, ip, userAgent, {
      unlockedBy: req.user.id
    });

    res.json({
      success: true,
      message: 'Usuário desbloqueado com sucesso'
    });

  } catch (error) {
    console.error('Erro ao desbloquear usuário:', error);
    res.status(500).json({
      success: false,
      message: 'Erro interno do servidor'
    });
  }
});

export default router;