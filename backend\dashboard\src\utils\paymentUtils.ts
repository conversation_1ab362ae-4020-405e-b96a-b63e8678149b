import { CheckCircle, XCircle, Clock } from 'lucide-react';
import { PaymentStatus, PlanType } from '../types/payment.types';

export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(amount / 100);
};

export const formatDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

export const getStatusIcon = (status: PaymentStatus) => {
  switch (status) {
    case 'succeeded':
      return CheckCircle;
    case 'failed':
      return XCircle;
    case 'pending':
      return Clock;
    default:
      return XCircle;
  }
};

export const getStatusColor = (status: PaymentStatus): string => {
  switch (status) {
    case 'succeeded':
      return 'text-green-600 bg-green-100';
    case 'failed':
      return 'text-red-600 bg-red-100';
    case 'pending':
      return 'text-yellow-600 bg-yellow-100';
    default:
      return 'text-gray-600 bg-gray-100';
  }
};

export const getPlanColor = (plan: PlanType): string => {
  switch (plan) {
    case 'basic':
      return 'text-gray-600 bg-gray-100';
    case 'standard':
      return 'text-blue-600 bg-blue-100';
    case 'professional':
      return 'text-purple-600 bg-purple-100';
    default:
      return 'text-gray-600 bg-gray-100';
  }
};

export const getPlanDisplayName = (plan: PlanType): string => {
  switch (plan) {
    case 'basic':
      return 'Básico';
    case 'standard':
      return 'Padrão';
    case 'professional':
      return 'Profissional';
    default:
      return plan;
  }
};

export const getBillingCycleDisplayName = (cycle: 'monthly' | 'yearly'): string => {
  return cycle === 'monthly' ? 'Mensal' : 'Anual';
};

export const getStatusDisplayName = (status: PaymentStatus): string => {
  switch (status) {
    case 'succeeded':
      return 'Sucesso';
    case 'failed':
      return 'Falha';
    case 'pending':
      return 'Pendente';
    case 'canceled':
      return 'Cancelado';
    default:
      return status;
  }
};
