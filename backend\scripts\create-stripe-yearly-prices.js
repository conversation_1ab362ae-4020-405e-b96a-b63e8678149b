import 'dotenv/config';
import Stripe from 'stripe';

// Inicializar <PERSON>e
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2023-10-16',
});

// IDs dos produtos existentes (obtidos do CSV)
const PRODUCTS = {
  basic: 'prod_ST1FHIjc6hMSJI',
  standard: 'prod_ST1IFGktXPqPvR', 
  professional: 'prod_ST1ID3J11bYcuD'
};

// Preços anuais (em centavos) - 20% de desconto
const YEARLY_PRICES = {
  basic: 183492,      // R$ 1.834,92 (de R$ 169,90 x 12 = R$ 2.038,80)
  standard: 280708,   // R$ 2.807,08 (de R$ 259,90 x 12 = R$ 3.118,80)
  professional: 647892 // R$ 6.478,92 (de R$ 599,90 x 12 = R$ 7.198,80)
};

async function createYearlyPrices() {
  console.log('🚀 Criando preços anuais no Stripe...\n');

  try {
    for (const [planType, productId] of Object.entries(PRODUCTS)) {
      console.log(`📦 Criando preço anual para plano ${planType}...`);
      
      const price = await stripe.prices.create({
        product: productId,
        unit_amount: YEARLY_PRICES[planType],
        currency: 'brl',
        recurring: {
          interval: 'year',
        },
        nickname: `${planType}-yearly`,
        metadata: {
          plan_type: planType,
          billing_cycle: 'yearly',
          discount: '20%'
        }
      });

      console.log(`✅ Preço anual criado para ${planType}:`);
      console.log(`   ID: ${price.id}`);
      console.log(`   Valor: R$ ${(price.unit_amount / 100).toFixed(2)}`);
      console.log(`   Produto: ${price.product}`);
      console.log('');
    }

    console.log('🎉 Todos os preços anuais foram criados com sucesso!');
    console.log('\n📝 Atualize o arquivo backend/routes/stripe.js com os novos IDs:');
    console.log('');
    console.log('const STRIPE_PRICE_IDS = {');
    console.log('  basic: {');
    console.log('    monthly: "price_1RY5DHClUIoqY19kkU0D8pmf",');
    console.log('    yearly: "NOVO_ID_AQUI", // Copie do output acima');
    console.log('  },');
    console.log('  standard: {');
    console.log('    monthly: "price_1RY5FSClUIoqY19kh9kieCaY",');
    console.log('    yearly: "NOVO_ID_AQUI", // Copie do output acima');
    console.log('  },');
    console.log('  professional: {');
    console.log('    monthly: "price_1RY5GJClUIoqY19k1bCNt4lG",');
    console.log('    yearly: "NOVO_ID_AQUI", // Copie do output acima');
    console.log('  }');
    console.log('};');

  } catch (error) {
    console.error('❌ Erro ao criar preços:', error.message);
    process.exit(1);
  }
}

// Executar script
createYearlyPrices();
