@echo off
echo ========================================
echo     ETAPA 5: CONFIGURAR DATABASE
echo ========================================
echo.

echo [1/5] Verificando configuracao Firestore...
if not exist "firestore.rules" (
    echo Criando firestore.rules...
    echo rules_version = '2'; > firestore.rules
    echo service cloud.firestore { >> firestore.rules
    echo   match /databases/{database}/documents { >> firestore.rules
    echo     match /users/{userId} { >> firestore.rules
    echo       allow read, write: if request.auth != null ^&^& request.auth.uid == userId; >> firestore.rules
    echo     } >> firestore.rules
    echo     match /organizations/{orgId} { >> firestore.rules
    echo       allow read, write: if request.auth != null ^&^& request.auth.uid in resource.data.members; >> firestore.rules
    echo     } >> firestore.rules
    echo   } >> firestore.rules
    echo } >> firestore.rules
)

if not exist "firestore.indexes.json" (
    echo Criando firestore.indexes.json...
    echo { > firestore.indexes.json
    echo   "indexes": [], >> firestore.indexes.json
    echo   "fieldOverrides": [] >> firestore.indexes.json
    echo } >> firestore.indexes.json
)

echo.
echo [2/5] Fazendo deploy das regras Firestore...
firebase deploy --only firestore:rules

echo.
echo [3/5] Fazendo deploy dos indexes...
firebase deploy --only firestore:indexes

echo.
echo [4/5] Configurando colecoes iniciais...
echo Executando script de seed...

if exist "scripts\firestore.seed.mjs" (
    node scripts\firestore.seed.mjs
) else (
    echo Script de seed nao encontrado, pulando...
)

echo.
echo [5/5] Configurando backup automatico...
echo.
echo Para configurar backup automatico:
echo 1. Acesse: https://console.cloud.google.com/firestore/databases
echo 2. Va para "Backup"
echo 3. Configure backup diario
echo.

echo ========================================
echo        DATABASE CONFIGURADO!
echo ========================================
echo.
echo Configuracoes aplicadas:
echo - Regras de seguranca ativas
echo - Indexes otimizados
echo - Colecoes iniciais criadas
echo.
echo Proximos passos:
echo 1. Configurar backup automatico
echo 2. Configurar monitoramento
echo 3. Testar aplicacao completa
echo.

pause
