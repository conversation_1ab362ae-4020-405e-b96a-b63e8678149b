import Notification from '../models/Notification.js';
import User from '../models/User.js';
import websocketService from './websocketService.js';

class NotificationService {
  
  /**
   * Criar notificação para um usuário específico
   */
  static async createNotification(userId, notificationData) {
    try {
      const notification = await Notification.create({
        userId,
        ...notificationData
      });

      // Enviar notificação em tempo real via WebSocket
      this.sendRealTimeNotification(userId, notification);

      return notification;
    } catch (error) {
      console.error('Erro ao criar notificação:', error);
      throw error;
    }
  }

  /**
   * Criar notificações para múltiplos usuários
   */
  static async createBulkNotifications(userIds, notificationData) {
    try {
      const notifications = userIds.map(userId => ({
        userId,
        ...notificationData
      }));

      const createdNotifications = await Notification.createBatch(notifications);

      // Enviar notificações em tempo real para todos os usuários
      // createdNotifications.forEach(notification => {
      //   this.sendRealTimeNotification(notification.userId, notification);
      // });

      return createdNotifications;
    } catch (error) {
      console.error('Erro ao criar notificações em lote:', error);
      throw error;
    }
  }

  /**
   * Notificar sobre nova demanda
   */
  static async notifyNewDemand(demandData, assignedUserId = null) {
    try {
      const notificationData = {
        title: 'Nova demanda recebida',
        message: `${demandData.citizenName || 'Cidadão'} enviou uma solicitação sobre ${demandData.category || 'assunto geral'}`,
        type: 'info',
        category: 'demand',
        actionUrl: `/demands/${demandData.id}`,
        actionLabel: 'Ver demanda',
        metadata: {
          demandId: demandData.id,
          category: demandData.category,
          priority: demandData.priority
        }
      };

      if (assignedUserId) {
        // Notificar usuário específico responsável
        return await this.createNotification(assignedUserId, notificationData);
      } else {
        // Notificar todos os usuários com permissão para ver demandas
        const users = await User.findAll();
        const eligibleUsers = users.filter(user => 
          user.isActive && (
            user.permissions.includes('*') || 
            user.permissions.includes('demands.read')
          )
        );
        
        const userIds = eligibleUsers.map(user => user.id);
        return await this.createBulkNotifications(userIds, notificationData);
      }
    } catch (error) {
      console.error('Erro ao notificar nova demanda:', error);
      throw error;
    }
  }

  /**
   * Notificar sobre demanda resolvida
   */
  static async notifyDemandResolved(demandData, resolvedByUserId) {
    try {
      const notificationData = {
        title: 'Demanda resolvida',
        message: `A solicitação #${demandData.id} foi marcada como concluída`,
        type: 'success',
        category: 'demand',
        actionUrl: `/demands/${demandData.id}`,
        actionLabel: 'Ver detalhes',
        metadata: {
          demandId: demandData.id,
          resolvedBy: resolvedByUserId,
          category: demandData.category
        }
      };

      // Notificar usuários interessados (exceto quem resolveu)
      const users = await User.findAll();
      const eligibleUsers = users.filter(user => 
        user.isActive && 
        user.id !== resolvedByUserId &&
        (user.permissions.includes('*') || user.permissions.includes('demands.read'))
      );
      
      const userIds = eligibleUsers.map(user => user.id);
      return await this.createBulkNotifications(userIds, notificationData);
    } catch (error) {
      console.error('Erro ao notificar demanda resolvida:', error);
      throw error;
    }
  }

  /**
   * Notificar sobre prazo vencendo
   */
  static async notifyDeadlineApproaching(demandData, daysRemaining) {
    try {
      const notificationData = {
        title: 'Prazo vencendo',
        message: `A demanda #${demandData.id} vence em ${daysRemaining} ${daysRemaining === 1 ? 'dia' : 'dias'}`,
        type: daysRemaining <= 1 ? 'error' : 'warning',
        category: 'demand',
        actionUrl: `/demands/${demandData.id}`,
        actionLabel: 'Ver demanda',
        metadata: {
          demandId: demandData.id,
          daysRemaining,
          deadline: demandData.deadline
        }
      };

      // Notificar usuários responsáveis
      const users = await User.findAll();
      const eligibleUsers = users.filter(user => 
        user.isActive && (
          user.permissions.includes('*') || 
          user.permissions.includes('demands.update')
        )
      );
      
      const userIds = eligibleUsers.map(user => user.id);
      return await this.createBulkNotifications(userIds, notificationData);
    } catch (error) {
      console.error('Erro ao notificar prazo vencendo:', error);
      throw error;
    }
  }

  /**
   * Notificar sobre evento próximo
   */
  static async notifyUpcomingEvent(eventData, hoursUntilEvent) {
    try {
      const notificationData = {
        title: 'Evento próximo',
        message: `${eventData.title} está agendado para ${hoursUntilEvent < 24 ? 'hoje' : 'amanhã'} às ${eventData.time}`,
        type: 'warning',
        category: 'event',
        actionUrl: `/agenda`,
        actionLabel: 'Ver agenda',
        metadata: {
          eventId: eventData.id,
          eventDate: eventData.date,
          hoursUntilEvent
        }
      };

      // Notificar todos os usuários ativos
      const users = await User.findAll();
      const activeUsers = users.filter(user => user.isActive);
      const userIds = activeUsers.map(user => user.id);
      
      return await this.createBulkNotifications(userIds, notificationData);
    } catch (error) {
      console.error('Erro ao notificar evento próximo:', error);
      throw error;
    }
  }

  /**
   * Notificar sobre atualização do sistema
   */
  static async notifySystemUpdate(updateData) {
    try {
      const notificationData = {
        title: 'Atualização do sistema',
        message: updateData.message || 'O sistema foi atualizado com novas funcionalidades',
        type: 'info',
        category: 'system',
        actionUrl: updateData.actionUrl || null,
        actionLabel: updateData.actionLabel || null,
        metadata: {
          version: updateData.version,
          updateType: updateData.type
        }
      };

      // Notificar todos os usuários ativos
      const users = await User.findAll();
      const activeUsers = users.filter(user => user.isActive);
      const userIds = activeUsers.map(user => user.id);
      
      return await this.createBulkNotifications(userIds, notificationData);
    } catch (error) {
      console.error('Erro ao notificar atualização do sistema:', error);
      throw error;
    }
  }

  /**
   * Notificar usuário específico
   */
  static async notifyUser(userId, title, message, options = {}) {
    try {
      const notificationData = {
        title,
        message,
        type: options.type || 'info',
        category: options.category || 'general',
        actionUrl: options.actionUrl || null,
        actionLabel: options.actionLabel || null,
        metadata: options.metadata || {},
        expiresAt: options.expiresAt || null
      };

      return await this.createNotification(userId, notificationData);
    } catch (error) {
      console.error('Erro ao notificar usuário:', error);
      throw error;
    }
  }

  /**
   * Limpar notificações antigas automaticamente
   */
  static async cleanupOldNotifications() {
    try {
      const deletedCount = await Notification.cleanupExpired();
      console.log(`Limpeza automática: ${deletedCount} notificações expiradas removidas`);
      return deletedCount;
    } catch (error) {
      console.error('Erro na limpeza automática de notificações:', error);
      throw error;
    }
  }

  /**
   * Verificar prazos e enviar notificações automáticas
   * Esta função deve ser chamada periodicamente (ex: via cron job)
   */
  static async checkDeadlinesAndNotify() {
    try {
      // Aqui você implementaria a lógica para verificar prazos
      // Por exemplo, buscar demandas com deadline próximo
      
      console.log('Verificação de prazos executada');
      // Implementar quando tiver o modelo de Demand
    } catch (error) {
      console.error('Erro ao verificar prazos:', error);
      throw error;
    }
  }

  /**
   * Enviar notificação em tempo real via WebSocket
   */
  static sendRealTimeNotification(userId, notification) {
    try {
      const sent = websocketService.sendNotificationToUser(userId, notification);
      if (sent) {
        console.log(`✅ Notificação em tempo real enviada para usuário ${userId}:`, notification.title);
      } else {
        console.log(`📭 Usuário ${userId} não conectado via WebSocket:`, notification.title);
      }
    } catch (error) {
      console.error('Erro ao enviar notificação via WebSocket:', error);
    }
  }

  /**
   * Enviar notificação para múltiplos usuários via WebSocket
   */
  static sendRealTimeNotificationToUsers(userIds, notification) {
    try {
      const sentCount = websocketService.sendNotificationToUsers(userIds, notification);
      console.log(`✅ Notificação em tempo real enviada para ${sentCount}/${userIds.length} usuários:`, notification.title);
    } catch (error) {
      console.error('Erro ao enviar notificação via WebSocket para múltiplos usuários:', error);
    }
  }

  /**
   * Broadcast notificação para todos os usuários conectados
   */
  static broadcastRealTimeNotification(notification) {
    try {
      websocketService.broadcastNotification(notification);
      console.log(`📢 Notificação broadcast via WebSocket:`, notification.title);
    } catch (error) {
      console.error('Erro ao fazer broadcast de notificação via WebSocket:', error);
    }
  }
}

export default NotificationService;
