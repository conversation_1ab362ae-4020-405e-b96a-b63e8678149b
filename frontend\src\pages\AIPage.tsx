import React, { useState } from 'react';
import { ICONS } from '../constants';
import { usePlan } from '../hooks/usePlan';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import SentimentAnalysis from '../components/ai/SentimentAnalysis';
import AutoCategorization from '../components/ai/AutoCategorization';

type AIFeatureTab = 'overview' | 'sentiment' | 'categorization' | 'predictive' | 'responses';

const AIPage: React.FC = () => {
  const { currentPlan, canUseAIFeature, getAIFeatureUsage, upgradePlan } = usePlan();
  const [activeTab, setActiveTab] = useState<AIFeatureTab>('overview');

  // Para demonstração - alternar entre planos
  const handlePlanDemo = (planId: string) => {
    localStorage.setItem('demo_plan', planId);
    window.location.reload(); // Recarregar para aplicar o novo plano
  };

  const aiFeatures = [
    {
      id: 'text-analysis',
      name: 'Aná<PERSON><PERSON> de Sentimento',
      description: 'Analise automaticamente o sentimento de textos, comentários e demandas',
      icon: ICONS.BRAIN,
      tab: 'sentiment' as AIFeatureTab,
      available: canUseAIFeature('text-analysis'),
      requiredPlan: 'Padrão',
      color: 'bg-blue-500'
    },
    {
      id: 'auto-categorization',
      name: 'Categorização Automática',
      description: 'Categorize demandas automaticamente por área e prioridade',
      icon: ICONS.ZAP,
      tab: 'categorization' as AIFeatureTab,
      available: canUseAIFeature('auto-categorization'),
      requiredPlan: 'Profissional',
      color: 'bg-purple-500'
    },
    {
      id: 'predictive-analytics',
      name: 'Análise Preditiva',
      description: 'Previsões baseadas em dados históricos e tendências',
      icon: ICONS.TRENDING_UP,
      tab: 'predictive' as AIFeatureTab,
      available: canUseAIFeature('predictive-analytics'),
      requiredPlan: 'Profissional',
      color: 'bg-green-500'
    },
    {
      id: 'smart-responses',
      name: 'Respostas Inteligentes',
      description: 'Sugestões automáticas de respostas para demandas',
      icon: ICONS.MESSAGE_SQUARE,
      tab: 'responses' as AIFeatureTab,
      available: canUseAIFeature('smart-responses'),
      requiredPlan: 'Profissional',
      color: 'bg-orange-500'
    }
  ];

  const availableFeatures = aiFeatures.filter(feature => feature.available);
  const unavailableFeatures = aiFeatures.filter(feature => !feature.available);

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Header */}
      <Card className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Inteligência Artificial
            </h1>
            <p className="text-gray-600">
              Potencialize sua gestão com recursos de IA avançados
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-5 h-5 text-yellow-500">
                {ICONS.CROWN}
              </div>
              <span className="text-sm font-medium text-gray-700">
                Plano {currentPlan?.name || 'Básico'}
              </span>
            </div>

            {/* Botões de demonstração */}
            <div className="flex items-center space-x-2">
              <span className="text-xs text-gray-500">Demo:</span>
              <button
                type="button"
                onClick={() => handlePlanDemo('basic')}
                className={`px-2 py-1 text-xs rounded ${
                  currentPlan?.id === 'BASIC'
                    ? 'bg-gray-200 text-gray-800'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                Básico
              </button>
              <button
                type="button"
                onClick={() => handlePlanDemo('standard')}
                className={`px-2 py-1 text-xs rounded ${
                  currentPlan?.id === 'STANDARD'
                    ? 'bg-blue-200 text-blue-800'
                    : 'bg-blue-100 text-blue-600 hover:bg-blue-200'
                }`}
              >
                Padrão
              </button>
              <button
                type="button"
                onClick={() => handlePlanDemo('professional')}
                className={`px-2 py-1 text-xs rounded ${
                  currentPlan?.id === 'PROFESSIONAL'
                    ? 'bg-purple-200 text-purple-800'
                    : 'bg-purple-100 text-purple-600 hover:bg-purple-200'
                }`}
              >
                Profissional
              </button>
            </div>
          </div>
        </div>
      </Card>

      {/* Funcionalidades Disponíveis */}
      {availableFeatures.length > 0 && (
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            Funcionalidades Disponíveis
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {availableFeatures.map((feature) => {
              const usage = getAIFeatureUsage(feature.id);
              return (
                <div
                  key={feature.id}
                  className="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors cursor-pointer"
                  onClick={() => setActiveTab(feature.tab)}
                >
                  <div className="flex items-start space-x-3">
                    <div className={`${feature.color} rounded-lg p-2`}>
                      <div className="w-5 h-5 text-white">
                        {feature.icon}
                      </div>
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900 mb-1">
                        {feature.name}
                      </h3>
                      <p className="text-sm text-gray-600 mb-2">
                        {feature.description}
                      </p>
                      {usage && usage.limit > 0 && (
                        <div className="text-xs text-gray-500">
                          {usage.used} / {usage.limit} usos este mês
                        </div>
                      )}
                    </div>
                    <div className="w-4 h-4 text-gray-400">
                      {ICONS.ARROW_RIGHT}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </Card>
      )}

      {/* Funcionalidades Indisponíveis */}
      {unavailableFeatures.length > 0 && (
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            Funcionalidades Premium
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {unavailableFeatures.map((feature) => (
              <div
                key={feature.id}
                className="border border-gray-200 rounded-lg p-4 opacity-60"
              >
                <div className="flex items-start space-x-3">
                  <div className="bg-gray-300 rounded-lg p-2">
                    <div className="w-5 h-5 text-gray-500">
                      {ICONS.LOCK}
                    </div>
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900 mb-1">
                      {feature.name}
                    </h3>
                    <p className="text-sm text-gray-600 mb-2">
                      {feature.description}
                    </p>
                    <div className="text-xs text-gray-500">
                      Requer plano {feature.requiredPlan}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {unavailableFeatures.length > 0 && (
            <div className="mt-6 text-center">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                <h3 className="font-medium text-blue-900 mb-2">
                  Desbloqueie o Poder da IA
                </h3>
                <p className="text-sm text-blue-700">
                  Faça upgrade para acessar funcionalidades avançadas de inteligência artificial
                  e potencializar sua gestão política.
                </p>
              </div>
              <Button variant="primary">
                Ver Planos e Preços
              </Button>
            </div>
          )}
        </Card>
      )}

      {/* Estatísticas de Uso */}
      {availableFeatures.length > 0 && (
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            Uso de IA Este Mês
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {availableFeatures.map((feature) => {
              const usage = getAIFeatureUsage(feature.id);
              if (!usage) return null;

              const percentage = usage.limit > 0 ? (usage.used / usage.limit) * 100 : 0;
              
              return (
                <div key={feature.id} className="text-center">
                  <div className={`${feature.color} rounded-lg p-3 mb-2 inline-block`}>
                    <div className="w-6 h-6 text-white">
                      {feature.icon}
                    </div>
                  </div>
                  <h3 className="font-medium text-gray-900 text-sm mb-1">
                    {feature.name}
                  </h3>
                  <div className="text-2xl font-bold text-gray-900">
                    {usage.used}
                  </div>
                  {usage.limit > 0 ? (
                    <>
                      <div className="text-sm text-gray-500">
                        de {usage.limit}
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                        <div 
                          className={`h-2 rounded-full ${feature.color}`}
                          style={{ width: `${Math.min(percentage, 100)}%` }}
                        ></div>
                      </div>
                    </>
                  ) : (
                    <div className="text-sm text-gray-500">
                      Ilimitado
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </Card>
      )}
    </div>
  );

  const renderComingSoon = (title: string) => (
    <Card className="p-6">
      <div className="text-center py-12">
        <div className="bg-gray-100 rounded-full p-4 inline-block mb-4">
          <div className="w-8 h-8 text-gray-400">
            {ICONS.BRAIN}
          </div>
        </div>
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          {title}
        </h2>
        <p className="text-gray-600 mb-4">
          Esta funcionalidade estará disponível em breve.
        </p>
        <Button 
          variant="outline"
          onClick={() => setActiveTab('overview')}
        >
          Voltar para Visão Geral
        </Button>
      </div>
    </Card>
  );

  const tabs = [
    { id: 'overview', name: 'Visão Geral', icon: ICONS.BRAIN },
    { id: 'sentiment', name: 'Análise de Sentimento', icon: ICONS.BRAIN, available: canUseAIFeature('text-analysis') },
    { id: 'categorization', name: 'Categorização', icon: ICONS.ZAP, available: canUseAIFeature('auto-categorization') },
    { id: 'predictive', name: 'Análise Preditiva', icon: ICONS.TRENDING_UP, available: canUseAIFeature('predictive-analytics') },
    { id: 'responses', name: 'Respostas Inteligentes', icon: ICONS.MESSAGE_SQUARE, available: canUseAIFeature('smart-responses') }
  ];

  return (
    <div className="space-y-6">
      {/* Navigation Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              type="button"
              key={tab.id}
              onClick={() => setActiveTab(tab.id as AIFeatureTab)}
              className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              } ${tab.available === false ? 'opacity-50' : ''}`}
              disabled={tab.available === false}
            >
              <div className="w-4 h-4">
                {tab.icon}
              </div>
              <span>{tab.name}</span>
              {tab.available === false && (
                <div className="w-3 h-3">
                  {ICONS.LOCK}
                </div>
              )}
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      {activeTab === 'overview' && renderOverview()}
      {activeTab === 'sentiment' && <SentimentAnalysis />}
      {activeTab === 'categorization' && <AutoCategorization />}
      {activeTab === 'predictive' && renderComingSoon('Análise Preditiva')}
      {activeTab === 'responses' && renderComingSoon('Respostas Inteligentes')}
    </div>
  );
};

export default AIPage;
