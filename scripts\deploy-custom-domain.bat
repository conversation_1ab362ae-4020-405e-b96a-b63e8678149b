@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo     DEPLOY PARA DOMÍNIO CUSTOMIZADO
echo     www.promandato.com.br
echo ========================================
echo.

:: Verificar se Firebase CLI está instalado
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Firebase CLI não está instalado
    echo Instale com: npm install -g firebase-tools
    pause
    exit /b 1
)

:: Verificar se está logado
firebase projects:list >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Não está logado no Firebase
    echo Execute: firebase login
    pause
    exit /b 1
)

echo [1/8] Limpando diretórios anteriores...

:: Limpar diretório public se existir
if exist "public" (
    rmdir /s /q "public"
    echo ✅ Diretório public limpo
)

echo.
echo [2/8] Criando nova estrutura...

:: Criar estrutura de diretórios
mkdir public
mkdir public\landingpage
mkdir public\app

echo ✅ Estrutura criada

echo.
echo [3/8] Copiando landing page...

:: Copiar arquivos da landing page
xcopy "landingpage\*" "public\landingpage\" /E /Y /Q

if %errorlevel% equ 0 (
    echo ✅ Landing page copiada
) else (
    echo ❌ Erro ao copiar landing page
    pause
    exit /b 1
)

echo.
echo [4/8] Fazendo build do frontend...

cd frontend

:: Verificar se package.json existe
if not exist "package.json" (
    echo ❌ package.json não encontrado
    pause
    exit /b 1
)

:: Limpar cache e fazer build limpo
if exist "dist" rmdir /s /q "dist"
if exist "node_modules\.cache" rmdir /s /q "node_modules\.cache"

call npm ci --only=production
call npm run build

if %errorlevel% neq 0 (
    echo ❌ Build do frontend falhou
    pause
    exit /b 1
)

echo ✅ Build concluído

echo.
echo [5/8] Copiando frontend...

cd ..

:: Copiar build do frontend
xcopy "frontend\dist\*" "public\app\" /E /Y /Q

if %errorlevel% equ 0 (
    echo ✅ Frontend copiado
) else (
    echo ❌ Erro ao copiar frontend
    pause
    exit /b 1
)

echo.
echo [6/8] Verificando estrutura...

:: Verificar se arquivos principais existem
if not exist "public\index.html" (
    echo ❌ public\index.html não encontrado
    pause
    exit /b 1
)

if not exist "public\landingpage\index.html" (
    echo ❌ public\landingpage\index.html não encontrado
    pause
    exit /b 1
)

if not exist "public\app\index.html" (
    echo ❌ public\app\index.html não encontrado
    pause
    exit /b 1
)

echo ✅ Estrutura verificada

echo.
echo [7/8] Configurando Firebase...

:: Configurar projeto
firebase use promandato-9a4cf

if %errorlevel% neq 0 (
    echo ❌ Erro ao configurar projeto Firebase
    pause
    exit /b 1
)

echo ✅ Projeto configurado

echo.
echo [8/8] Fazendo deploy...

:: Deploy para Firebase Hosting
firebase deploy --only hosting

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo         DEPLOY CONCLUÍDO!
    echo ========================================
    echo.
    echo 🌐 URLs disponíveis:
    echo   - Firebase: https://promandato-9a4cf.web.app
    echo   - Landing: https://promandato-9a4cf.web.app/landingpage/
    echo   - App: https://promandato-9a4cf.web.app/app/
    echo.
    echo 📋 PRÓXIMOS PASSOS:
    echo.
    echo 1. Configurar domínio customizado no Firebase Console:
    echo    https://console.firebase.google.com/project/promandato-9a4cf/hosting
    echo.
    echo 2. Adicionar domínio: www.promandato.com.br
    echo.
    echo 3. Configurar DNS conforme instruções do Firebase
    echo.
    echo 4. Aguardar propagação (até 24h)
    echo.
    echo 5. Testar URLs finais:
    echo    - https://www.promandato.com.br/
    echo    - https://www.promandato.com.br/landingpage/
    echo    - https://www.promandato.com.br/app/
    echo.
) else (
    echo.
    echo ❌ ERRO: Deploy falhou!
    echo Verifique os logs acima e tente novamente.
    echo.
)

pause
