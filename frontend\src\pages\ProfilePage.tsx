
import React from 'react';
import { useAuth } from '../hooks/useAuth';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/Input';
import { ProfileImageUpload } from '../components/ui/ProfileImageUpload';
import { ICONS } from '../constants';
import { updateUserProfile } from '../services/firebaseService';

const ProfilePage: React.FC = () => {
  const { currentUser, refreshUser } = useAuth();
  const [name, setName] = React.useState(currentUser?.name || '');
  const [email, setEmail] = React.useState(currentUser?.email || '');
  const [phone, setPhone] = React.useState('');
  const [avatarUrl, setAvatarUrl] = React.useState(currentUser?.avatarUrl || '');
  const [isEditing, setIsEditing] = React.useState(false);
  const [isSaving, setIsSaving] = React.useState(false);

  const handleSaveChanges = async () => {
    if (!currentUser) return;
    
    setIsSaving(true);
    try {
      await updateUserProfile(currentUser.id, {
        name,
        avatarUrl,
        // phone pode ser adicionado quando implementarmos campos extras
      });
      
      // Atualizar o contexto de autenticação com os novos dados
      await refreshUser();
      
      setIsEditing(false);
      alert("Alterações salvas com sucesso!");
    } catch (error) {
      console.error('Erro ao salvar perfil:', error);
      alert("Erro ao salvar alterações. Tente novamente.");
    } finally {
      setIsSaving(false);
    }
  };

  const handleImageUpdate = async (newImageUrl: string) => {
    setAvatarUrl(newImageUrl);
    
    // Salvar automaticamente a nova imagem no perfil
    if (currentUser) {
      try {
        await updateUserProfile(currentUser.id, {
          avatarUrl: newImageUrl,
        });
        
        // Atualizar o contexto de autenticação
        await refreshUser();
        
        console.log("Foto de perfil atualizada com sucesso");
      } catch (error) {
        console.error('Erro ao salvar nova foto de perfil:', error);
      }
    }
  };

  if (!currentUser) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="dark:text-neutral-light">Carregando perfil...</p>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <div className="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-6 mb-6">
        <ProfileImageUpload
          currentImageUrl={avatarUrl}
          onImageUpdate={handleImageUpdate}
          userId={currentUser.id}
          size="xl"
        />
        <div className="text-center sm:text-left">
          <h1 className="text-3xl font-bold text-neutral-dark dark:text-neutral-light">{currentUser.name}</h1>
          <p className="text-md text-gray-500 dark:text-neutral-DEFAULT">{currentUser.email}</p>
          <p className="text-sm text-gray-400 dark:text-neutral-medium capitalize">Função: {currentUser.role}</p>
        </div>
      </div>

      <Card title="Informações Pessoais">
        <form onSubmit={(e) => { e.preventDefault(); handleSaveChanges(); }}>
          <div className="space-y-4">
            <Input
              label="Nome Completo"
              id="fullName"
              value={name}
              onChange={(e) => setName(e.target.value)}
              disabled={!isEditing}
            />
            <Input
              label="Email"
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled 
            />
            <Input
              label="Telefone (Opcional)"
              id="phone"
              type="tel"
              placeholder="(XX) XXXXX-XXXX"
              value={phone}
              onChange={(e) => setPhone(e.target.value)}
              disabled={!isEditing}
            />
            <Input
              label="Alterar Senha"
              id="newPassword"
              type="password"
              placeholder="Deixe em branco para não alterar"
              disabled={!isEditing}
            />
            
            <div className="pt-4 flex justify-end space-x-3">
              {isEditing ? (
                <>
                  <Button type="button" variant="outline" onClick={() => setIsEditing(false)}>
                    Cancelar
                  </Button>
                  <Button type="submit" variant="primary" disabled={isSaving}>
                    {isSaving ? 'Salvando...' : 'Salvar Alterações'}
                  </Button>
                </>
              ) : (
                <Button type="button" onClick={() => setIsEditing(true)} leftIcon={ICONS.PENCIL}>
                  Editar Perfil
                </Button>
              )}
            </div>
          </div>
        </form>
      </Card>

      <Card title="Atividade Recente">
         <p className="text-sm text-gray-500 dark:text-neutral-DEFAULT">
            Registro das suas últimas ações no sistema. (Funcionalidade a ser implementada)
        </p>
      </Card>
    </div>
  );
};

export default ProfilePage;