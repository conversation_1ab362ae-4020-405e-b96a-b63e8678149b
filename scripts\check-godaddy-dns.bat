@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo     VERIFICAR DNS GODADDY
echo     www.promandato.com.br
echo ========================================
echo.

set DOMAIN=www.promandato.com.br

echo [1/5] Verificando resolução DNS básica...

:: Verificar se o domínio resolve
nslookup %DOMAIN% >temp_dns.txt 2>&1
if %errorlevel% equ 0 (
    echo ✅ DNS está resolvendo
    echo.
    echo 📊 Resultado do nslookup:
    type temp_dns.txt
    echo.
) else (
    echo ❌ DNS não está resolvendo
    echo.
    echo 🔧 Possíveis causas:
    echo   - Registros A não configurados
    echo   - Propagação ainda em andamento
    echo   - Erro na configuração GoDaddy
    echo.
)
del temp_dns.txt >nul 2>&1

echo.
echo [2/5] Verificando registro TXT de verificação...

:: Verificar registro TXT
nslookup -type=TXT %DOMAIN% >temp_txt.txt 2>&1
findstr "firebase-hosting-verification" temp_txt.txt >nul
if %errorlevel% equ 0 (
    echo ✅ Registro TXT encontrado
    echo.
    echo 📊 Registros TXT:
    type temp_txt.txt | findstr "firebase"
    echo.
) else (
    echo ❌ Registro TXT não encontrado
    echo.
    echo 🔧 Ação necessária:
    echo   - Adicionar registro TXT no GoDaddy
    echo   - Aguardar propagação (10-15 min)
    echo.
)
del temp_txt.txt >nul 2>&1

echo.
echo [3/5] Verificando registros A específicos...

:: Verificar IPs específicos do Firebase
echo Verificando IPs do Firebase...
nslookup %DOMAIN% ******* >temp_ips.txt 2>&1

findstr "*************" temp_ips.txt >nul
set IP1_FOUND=%errorlevel%

findstr "**************" temp_ips.txt >nul  
set IP2_FOUND=%errorlevel%

if %IP1_FOUND% equ 0 (
    echo ✅ IP ************* encontrado
) else (
    echo ❌ IP ************* não encontrado
)

if %IP2_FOUND% equ 0 (
    echo ✅ IP ************** encontrado
) else (
    echo ❌ IP ************** não encontrado
)

echo.
echo 📊 Todos os IPs encontrados:
type temp_ips.txt | findstr "Address:"
del temp_ips.txt >nul 2>&1

echo.
echo [4/5] Testando conectividade HTTPS...

:: Testar HTTPS
curl -s -o nul -w "%%{http_code}" https://%DOMAIN%/ >temp_https.txt 2>nul
set /p HTTPS_STATUS=<temp_https.txt
del temp_https.txt >nul 2>&1

if "%HTTPS_STATUS%"=="200" (
    echo ✅ HTTPS funcionando - Status: %HTTPS_STATUS%
) else if "%HTTPS_STATUS%"=="301" (
    echo ✅ HTTPS com redirecionamento - Status: %HTTPS_STATUS%
) else if "%HTTPS_STATUS%"=="302" (
    echo ✅ HTTPS com redirecionamento - Status: %HTTPS_STATUS%
) else if "%HTTPS_STATUS%"=="" (
    echo ❌ HTTPS não acessível - Sem resposta
) else (
    echo ⚠️  HTTPS com problema - Status: %HTTPS_STATUS%
)

echo.
echo [5/5] Verificando propagação global...

echo Testando diferentes servidores DNS...

:: Testar Google DNS
echo 🌐 Google DNS (*******):
nslookup %DOMAIN% ******* | findstr "Address:" | findstr -v "*******"

:: Testar Cloudflare DNS  
echo 🌐 Cloudflare DNS (*******):
nslookup %DOMAIN% ******* | findstr "Address:" | findstr -v "*******"

:: Testar OpenDNS
echo 🌐 OpenDNS (**************):
nslookup %DOMAIN% ************** | findstr "Address:" | findstr -v "**************"

echo.
echo ========================================
echo         RESUMO DA VERIFICAÇÃO
echo ========================================
echo.

echo 🌐 Domínio: %DOMAIN%
echo 🔍 DNS Básico: 
if %errorlevel% equ 0 (echo ✅ Funcionando) else (echo ❌ Problema)

echo 📝 Registro TXT: 
findstr "firebase-hosting-verification" temp_txt.txt >nul 2>&1
if %errorlevel% equ 0 (echo ✅ Configurado) else (echo ❌ Não encontrado)

echo 🎯 Registros A: 
if %IP1_FOUND% equ 0 if %IP2_FOUND% equ 0 (
    echo ✅ Ambos IPs configurados
) else (
    echo ❌ IPs incompletos ou incorretos
)

echo 🔒 HTTPS: %HTTPS_STATUS%

echo.
echo 📋 PRÓXIMOS PASSOS:
echo.

if %IP1_FOUND% neq 0 (
    echo ❌ PROBLEMA: Registros A não configurados corretamente
    echo.
    echo 🔧 AÇÃO NECESSÁRIA:
    echo   1. Acesse: https://dcc.godaddy.com/
    echo   2. Vá em DNS do domínio promandato.com.br
    echo   3. Adicione registros A:
    echo      - Nome: www, Valor: *************
    echo      - Nome: www, Valor: **************
    echo   4. Aguarde 1-4 horas para propagação
    echo.
) else if "%HTTPS_STATUS%"=="200" (
    echo ✅ CONFIGURAÇÃO COMPLETA!
    echo.
    echo 🎉 Seu domínio está funcionando corretamente!
    echo.
    echo 🔗 URLs para testar:
    echo   - https://www.promandato.com.br/
    echo   - https://www.promandato.com.br/landingpage/
    echo   - https://www.promandato.com.br/app/
    echo.
) else (
    echo ⏳ AGUARDANDO PROPAGAÇÃO
    echo.
    echo 🕐 DNS configurado, aguardando propagação completa
    echo 📅 Tempo estimado: 1-4 horas
    echo 🔄 Execute este script novamente em 1 hora
    echo.
)

echo 🆘 Se problemas persistirem:
echo   - Consulte: GODADDY_SETUP_GUIDE.md
echo   - Contato GoDaddy: 0800 606 4656
echo   - Verifique Firebase Console
echo.

pause
