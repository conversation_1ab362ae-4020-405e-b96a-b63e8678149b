{"name": "promandato-admin-dashboard", "version": "1.0.0", "description": "Dashboard administrativa para gerenciar planos do Promandato", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build-check": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"csv-parser": "^3.2.0", "firebase": "^11.9.0", "lucide-react": "^0.294.0", "multer": "^2.0.1", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "terser": "^5.41.0", "typescript": "^5.2.2", "vite": "^5.0.8"}, "keywords": ["admin", "dashboard", "plans", "management"], "author": "Promandato Team", "license": "MIT"}