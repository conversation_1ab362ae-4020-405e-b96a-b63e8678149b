rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Funções auxiliares
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    function isMaster() {
      return isAuthenticated() && 
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'master';
    }
    
    // Função isStaff removida pois não estava sendo usada
    
    function belongsToMaster(masterId) {
      return isAuthenticated() && (
        request.auth.uid == masterId || 
        (exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.masterId == masterId)
      );
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    // Regra temporária para desenvolvimento - REMOVER ANTES DE PRODUÇÃO
    match /{document=**} {
      allow read, write: if true;
    }
    
    // Regras para coleção de usuários
    match /users/{userId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated() && request.auth.uid == userId;
      allow update: if isOwner(userId) || isAdmin() || isMaster();
      allow delete: if isAdmin() || isMaster();
    }
    
    // Regras para perfis de políticos
    match /politician_profiles/{profileId} {
      allow read: if true; // Informações públicas
      allow write: if isAuthenticated() && (isAdmin() || isMaster());
    }
    
    // Regras para demandas
    match /demands/{demandId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
      allow update, delete: if isAuthenticated() && (
        isOwner(resource.data.createdBy) || 
        belongsToMaster(resource.data.masterId) ||
        isAdmin() || 
        isMaster()
      );
    }
    
    // Regras para cidadãos
    match /citizens/{citizenId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
      allow update, delete: if isAuthenticated() && (
        isOwner(resource.data.createdBy) || 
        belongsToMaster(resource.data.masterId) ||
        isAdmin() || 
        isMaster()
      );
    }
    
    // Regras para eventos de agenda
    match /agendaEvents/{eventId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
      allow update, delete: if isAuthenticated() && (
        isOwner(resource.data.createdBy) || 
        belongsToMaster(resource.data.masterId) ||
        isAdmin() || 
        isMaster()
      );
    }
    
    // Regras para documentos
    match /documents/{documentId} {
      allow read: if isAuthenticated();
      allow create: if isAuthenticated();
      allow update, delete: if isAuthenticated() && (
        isOwner(resource.data.createdBy) || 
        belongsToMaster(resource.data.masterId) ||
        isAdmin() || 
        isMaster()
      );
    }
    
    // Regras para redes sociais
    match /social_media/{platformId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && (isAdmin() || isMaster());
    }
    
    match /politician_social/{profileId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && (isAdmin() || isMaster());
    }
    
    // Regras para configurações do workspace
    match /workspace_settings/{settingId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && (isAdmin() || isMaster());
    }
    
    // Regras para membros da equipe
    match /team_members/{memberId} {
      allow read: if isAuthenticated();
      allow write: if isAuthenticated() && (isAdmin() || isMaster());
    }
  }
}
