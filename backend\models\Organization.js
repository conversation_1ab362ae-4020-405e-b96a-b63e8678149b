import fs from 'fs-extra';
import { v4 as uuidv4 } from 'uuid';
import config from '../config.js';
import path from 'path';

class Organization {
  constructor() {
    this.organizationsPath = config.database.organizationsPath;
  }

  async ensureDataFile() {
    try {
      await fs.ensureDir(path.dirname(this.organizationsPath));
      
      if (!await fs.pathExists(this.organizationsPath)) {
        await fs.writeJson(this.organizationsPath, []);
      }
    } catch (error) {
      console.error('Erro ao criar arquivo de organizações:', error);
      throw error;
    }
  }

  async getAll() {
    try {
      await this.ensureDataFile();
      return await fs.readJson(this.organizationsPath);
    } catch (error) {
      console.error('Erro ao ler organizações:', error);
      return [];
    }
  }

  async getById(id) {
    try {
      const organizations = await this.getAll();
      return organizations.find(org => org.id === id);
    } catch (error) {
      console.error('Erro ao buscar organização:', error);
      return null;
    }
  }

  async getBySlug(slug) {
    try {
      const organizations = await this.getAll();
      return organizations.find(org => org.slug === slug);
    } catch (error) {
      console.error('Erro ao buscar organização por slug:', error);
      return null;
    }
  }

  async create(organizationData) {
    try {
      const organizations = await this.getAll();
      
      // Verificar se já existe organização com mesmo slug
      const existingOrg = organizations.find(org => org.slug === organizationData.slug);
      if (existingOrg) {
        throw new Error('Já existe uma organização com este identificador');
      }

      const newOrganization = {
        id: uuidv4(),
        name: organizationData.name,
        slug: organizationData.slug,
        type: organizationData.type || 'municipal', // municipal, estadual, federal
        planId: organizationData.planId,
        status: 'active',
        settings: {
          logo: organizationData.logo || null,
          primaryColor: organizationData.primaryColor || '#3B82F6',
          secondaryColor: organizationData.secondaryColor || '#1E40AF',
          customDomain: organizationData.customDomain || null,
          allowPublicSubmissions: true,
          requireCitizenRegistration: false,
          moderateSubmissions: true
        },
        contact: {
          email: organizationData.email,
          phone: organizationData.phone || null,
          address: organizationData.address || null,
          website: organizationData.website || null
        },
        limits: this.getPlanLimits(organizationData.planId),
        usage: {
          users: 0,
          demands: 0,
          citizens: 0,
          storage: 0
        },
        features: this.getPlanFeatures(organizationData.planId),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdBy: organizationData.createdBy
      };

      organizations.push(newOrganization);
      await fs.writeJson(this.organizationsPath, organizations, { spaces: 2 });

      return newOrganization;
    } catch (error) {
      console.error('Erro ao criar organização:', error);
      throw error;
    }
  }

  async update(id, updateData) {
    try {
      const organizations = await this.getAll();
      const index = organizations.findIndex(org => org.id === id);
      
      if (index === -1) {
        throw new Error('Organização não encontrada');
      }

      // Atualizar dados
      organizations[index] = {
        ...organizations[index],
        ...updateData,
        updatedAt: new Date().toISOString()
      };

      // Se o plano mudou, atualizar limites e funcionalidades
      if (updateData.planId && updateData.planId !== organizations[index].planId) {
        organizations[index].limits = this.getPlanLimits(updateData.planId);
        organizations[index].features = this.getPlanFeatures(updateData.planId);
      }

      await fs.writeJson(this.organizationsPath, organizations, { spaces: 2 });
      return organizations[index];
    } catch (error) {
      console.error('Erro ao atualizar organização:', error);
      throw error;
    }
  }

  async delete(id) {
    try {
      const organizations = await this.getAll();
      const filteredOrganizations = organizations.filter(org => org.id !== id);
      
      if (organizations.length === filteredOrganizations.length) {
        throw new Error('Organização não encontrada');
      }

      await fs.writeJson(this.organizationsPath, filteredOrganizations, { spaces: 2 });
      return true;
    } catch (error) {
      console.error('Erro ao deletar organização:', error);
      throw error;
    }
  }

  getPlanLimits(planId) {
    const limits = {
      basic: {
        users: 5,
        demands: 100,
        citizens: 500,
        storage: 10 * 1024 * 1024 * 1024, // 10GB em bytes
        apiCalls: 1000
      },
      standard: {
        users: 15,
        demands: 500,
        citizens: 2000,
        storage: 50 * 1024 * 1024 * 1024, // 50GB em bytes
        apiCalls: 10000
      },
      professional: {
        users: -1, // ilimitado
        demands: -1, // ilimitado
        citizens: -1, // ilimitado
        storage: 200 * 1024 * 1024 * 1024, // 200GB em bytes
        apiCalls: -1 // ilimitado
      }
    };

    return limits[planId] || limits.basic;
  }

  getPlanFeatures(planId) {
    const features = {
      basic: {
        exportData: true,
        basicSupport: true,
        aiFeatures: []
      },
      standard: {
        exportData: true,
        basicSupport: true,
        prioritySupport: true,
        massMessaging: true,
        socialIntegration: true,
        advancedReports: true,
        apiAccess: true,
        aiFeatures: [
          {
            id: 'sentiment-analysis',
            name: 'Análise de Sentimento',
            description: 'Análise automática de sentimentos em textos',
            enabled: true
          }
        ]
      },
      professional: {
        exportData: true,
        basicSupport: true,
        prioritySupport: true,
        dedicatedSupport: true,
        massMessaging: true,
        socialIntegration: true,
        smsNotifications: true,
        advancedReports: true,
        customReports: true,
        apiAccess: true,
        webhooks: true,
        aiFeatures: [
          {
            id: 'sentiment-analysis',
            name: 'Análise de Sentimento',
            description: 'Análise automática de sentimentos em textos',
            enabled: true
          },
          {
            id: 'auto-categorization',
            name: 'Categorização Automática',
            description: 'Categorização automática de demandas',
            enabled: true
          },
          {
            id: 'predictive-analysis',
            name: 'Análise Preditiva',
            description: 'Previsões baseadas em dados históricos',
            enabled: true
          },
          {
            id: 'smart-responses',
            name: 'Respostas Inteligentes',
            description: 'Sugestões automáticas de respostas',
            enabled: true
          }
        ]
      }
    };

    return features[planId] || features.basic;
  }

  async updateUsage(organizationId, usageType, increment = 1) {
    try {
      const organization = await this.getById(organizationId);
      if (!organization) {
        throw new Error('Organização não encontrada');
      }

      organization.usage[usageType] = (organization.usage[usageType] || 0) + increment;
      organization.updatedAt = new Date().toISOString();

      await this.update(organizationId, organization);
      return organization;
    } catch (error) {
      console.error('Erro ao atualizar uso:', error);
      throw error;
    }
  }

  async checkLimits(organizationId, limitType, requestedAmount = 1) {
    try {
      const organization = await this.getById(organizationId);
      if (!organization) {
        throw new Error('Organização não encontrada');
      }

      const limit = organization.limits[limitType];
      const currentUsage = organization.usage[limitType] || 0;

      // -1 significa ilimitado
      if (limit === -1) {
        return { allowed: true, remaining: -1 };
      }

      const remaining = limit - currentUsage;
      const allowed = remaining >= requestedAmount;

      return { allowed, remaining, limit, currentUsage };
    } catch (error) {
      console.error('Erro ao verificar limites:', error);
      return { allowed: false, remaining: 0 };
    }
  }
}

export default Organization;
