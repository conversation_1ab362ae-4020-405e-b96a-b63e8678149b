
// Service Worker for caching and performance optimization

const CACHE_NAME = 'promandato-admin-v5';
const STATIC_CACHE_URLS = [
  '/',
  '/index.html'
];

// Lista de arquivos que devem ser ignorados se não existirem
const IGNORE_MISSING_FILES = [
  /\/assets\/.*\.css$/,
  /\/assets\/.*\.js$/
];

// Lista de arquivos específicos que podem não existir no desenvolvimento
const DEVELOPMENT_ASSETS = [
  'index-Dj5g2f-X.js',
  'react-vendor-CMDrCEic.js',
  'vendor-3CIACNA8.js',
  'user-management-Dw9qCxEp.js',
  'firebase-vendor-BhatmcAL.js',
  'critical.css'
];

// Install event - cache static resources
self.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        return cache.addAll(STATIC_CACHE_URLS);
      })
      .then(() => {
        return self.skipWaiting();
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== CACHE_NAME) {
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        return self.clients.claim();
      })
  );
});

// Fetch event - serve from cache when possible
self.addEventListener('fetch', (event) => {
  // Skip cross-origin requests
  if (!event.request.url.startsWith(self.location.origin)) {
    return;
  }

  // Skip requests for files that might not exist
  const shouldIgnoreMissing = IGNORE_MISSING_FILES.some(pattern =>
    pattern.test(event.request.url)
  );

  // Check if this is a development asset that might not exist
  const isDevelopmentAsset = DEVELOPMENT_ASSETS.some(asset =>
    event.request.url.includes(asset)
  );

  // Handle API requests with network-first strategy
  if (event.request.url.includes('/api/')) {
    event.respondWith(
      fetch(event.request)
        .then((response) => {
          // Only cache GET requests (POST, PUT, DELETE are not cacheable)
          if (event.request.method === 'GET' && response.ok) {
            // Clone the response before caching
            const responseClone = response.clone();
            caches.open(CACHE_NAME)
              .then((cache) => {
                cache.put(event.request, responseClone);
              })
              .catch((error) => {
                console.warn('Failed to cache API response:', event.request.url, error);
              });
          }
          return response;
        })
        .catch(() => {
          // Fallback to cache if network fails (only for GET requests)
          if (event.request.method === 'GET') {
            return caches.match(event.request);
          }
          // For non-GET requests, return a network error response
          return new Response(
            JSON.stringify({
              success: false,
              error: 'Network error - request failed and cannot be cached',
              offline: true
            }),
            {
              status: 503,
              statusText: 'Service Unavailable',
              headers: { 'Content-Type': 'application/json' }
            }
          );
        })
    );
    return;
  }

  // Handle static assets with cache-first strategy
  event.respondWith(
    caches.match(event.request)
      .then((cachedResponse) => {
        if (cachedResponse) {
          return cachedResponse;
        }

        return fetch(event.request)
          .then((response) => {
            // Don't cache non-successful responses or non-GET requests
            if (!response || response.status !== 200 || response.type !== 'basic' || event.request.method !== 'GET') {
              return response;
            }

            // Clone the response before caching
            const responseClone = response.clone();
            caches.open(CACHE_NAME)
              .then((cache) => {
                cache.put(event.request, responseClone);
              })
              .catch((error) => {
                console.warn('Failed to cache resource:', event.request.url, error);
              });

            return response;
          })
          .catch((error) => {
            // Se é um asset de desenvolvimento que pode não existir, não gerar erro
            if (isDevelopmentAsset || shouldIgnoreMissing) {
              console.log('Development asset not found (this is normal):', event.request.url);

              if (event.request.url.includes('.css')) {
                return new Response('/* CSS file not found */', {
                  status: 200,
                  headers: { 'Content-Type': 'text/css' }
                });
              }
              if (event.request.url.includes('.js')) {
                return new Response('// JS file not found', {
                  status: 200,
                  headers: { 'Content-Type': 'application/javascript' }
                });
              }
            } else {
              console.warn('Failed to fetch resource:', event.request.url, error);
            }

            // Para outros arquivos, tentar cache ou retornar erro silencioso
            return caches.match(event.request).then(cachedResponse => {
              if (cachedResponse) {
                return cachedResponse;
              }
              // Retornar resposta vazia em vez de erro
              return new Response('', { status: 404 });
            });
          });
      })
  );
});
