@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ========================================
echo     TESTE DE DOMÍNIO CUSTOMIZADO
echo     www.promandato.com.br
echo ========================================
echo.

set DOMAIN=www.promandato.com.br
set FIREBASE_URL=promandato-9a4cf.web.app

echo [1/6] Testando resolução DNS...

:: Testar se o domínio resolve
nslookup %DOMAIN% >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ DNS resolvendo para %DOMAIN%
    nslookup %DOMAIN%
) else (
    echo ❌ DNS não está resolvendo para %DOMAIN%
    echo ⚠️  Verifique a configuração DNS
)

echo.
echo [2/6] Testando conectividade HTTP...

:: Testar conectividade básica
curl -s -o nul -w "%%{http_code}" https://%DOMAIN%/ >temp_status.txt 2>nul
set /p HTTP_STATUS=<temp_status.txt
del temp_status.txt >nul 2>&1

if "%HTTP_STATUS%"=="200" (
    echo ✅ HTTPS funcionando - Status: %HTTP_STATUS%
) else if "%HTTP_STATUS%"=="301" (
    echo ✅ Redirecionamento funcionando - Status: %HTTP_STATUS%
) else if "%HTTP_STATUS%"=="302" (
    echo ✅ Redirecionamento funcionando - Status: %HTTP_STATUS%
) else (
    echo ❌ Problema com HTTPS - Status: %HTTP_STATUS%
)

echo.
echo [3/6] Testando URLs específicas...

echo Testando landing page...
curl -s -o nul -w "%%{http_code}" https://%DOMAIN%/landingpage/ >temp_status.txt 2>nul
set /p LANDING_STATUS=<temp_status.txt
del temp_status.txt >nul 2>&1

if "%LANDING_STATUS%"=="200" (
    echo ✅ Landing page: %LANDING_STATUS%
) else (
    echo ❌ Landing page: %LANDING_STATUS%
)

echo Testando aplicação...
curl -s -o nul -w "%%{http_code}" https://%DOMAIN%/app/ >temp_status.txt 2>nul
set /p APP_STATUS=<temp_status.txt
del temp_status.txt >nul 2>&1

if "%APP_STATUS%"=="200" (
    echo ✅ Aplicação: %APP_STATUS%
) else (
    echo ❌ Aplicação: %APP_STATUS%
)

echo.
echo [4/6] Testando SSL...

:: Verificar certificado SSL
curl -s -I https://%DOMAIN%/ | findstr "HTTP" >temp_ssl.txt 2>nul
if exist temp_ssl.txt (
    echo ✅ SSL ativo
    type temp_ssl.txt
    del temp_ssl.txt >nul 2>&1
) else (
    echo ❌ Problema com SSL
)

echo.
echo [5/6] Testando redirecionamentos...

echo Testando redirecionamento da raiz...
curl -s -I https://%DOMAIN%/ | findstr "Location" >temp_redirect.txt 2>nul
if exist temp_redirect.txt (
    echo ✅ Redirecionamento configurado:
    type temp_redirect.txt
    del temp_redirect.txt >nul 2>&1
) else (
    echo ⚠️  Nenhum redirecionamento detectado
)

echo.
echo [6/6] Comparando com Firebase URL...

echo Testando URL original do Firebase...
curl -s -o nul -w "%%{http_code}" https://%FIREBASE_URL%/ >temp_firebase.txt 2>nul
set /p FIREBASE_STATUS=<temp_firebase.txt
del temp_firebase.txt >nul 2>&1

if "%FIREBASE_STATUS%"=="200" (
    echo ✅ Firebase URL funcionando: %FIREBASE_STATUS%
) else (
    echo ❌ Firebase URL com problema: %FIREBASE_STATUS%
)

echo.
echo ========================================
echo         RESUMO DOS TESTES
echo ========================================
echo.

echo 🌐 Domínio: %DOMAIN%
echo 📊 Status HTTP: %HTTP_STATUS%
echo 🏠 Landing Page: %LANDING_STATUS%
echo 🖥️  Aplicação: %APP_STATUS%
echo 🔒 SSL: Verificado acima
echo 🔄 Redirecionamento: Verificado acima
echo 🔥 Firebase Original: %FIREBASE_STATUS%

echo.
echo 📋 PRÓXIMOS PASSOS:
echo.

if "%HTTP_STATUS%"=="200" (
    echo ✅ Domínio customizado funcionando!
    echo.
    echo 🔗 URLs para testar no navegador:
    echo   - https://%DOMAIN%/
    echo   - https://%DOMAIN%/landingpage/
    echo   - https://%DOMAIN%/app/
    echo.
    echo 🧪 Testes adicionais recomendados:
    echo   - Fluxo de pagamento Stripe
    echo   - Login na aplicação
    echo   - Funcionalidades da landing page
    echo   - Responsividade mobile
) else (
    echo ❌ Domínio customizado com problemas
    echo.
    echo 🔧 Ações recomendadas:
    echo   1. Verificar configuração DNS
    echo   2. Aguardar propagação (até 24h)
    echo   3. Verificar Firebase Console
    echo   4. Refazer deploy se necessário
    echo.
    echo 📖 Consulte: DNS_CONFIGURATION.md
)

echo.
echo 🆘 Em caso de problemas:
echo   - Verifique DNS_CONFIGURATION.md
echo   - Consulte Firebase Console
echo   - Execute: firebase hosting:channel:list
echo.

pause
