import { Plan, PlanType, PlanUpdateRequest, PlanResponse } from '../types/plans';

// Detectar URL da API baseado no ambiente
const getApiBaseUrl = (): string => {
  // Se estiver rodando no mesmo domínio (produção), usar URL relativa
  if (window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1') {
    return '/api';
  }

  // Para desenvolvimento local
  return import.meta.env.VITE_API_URL || 'http://localhost:3002/api';
};

const API_BASE_URL = getApiBaseUrl();

class PlanService {
  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  async getAllPlans(): Promise<Plan[]> {
    const response = await this.request<PlanResponse>('/plans');
    if (response.success && Array.isArray(response.data)) {
      return response.data;
    }
    throw new Error(response.error || 'Failed to fetch plans');
  }

  async getPlan(planId: PlanType): Promise<Plan> {
    const response = await this.request<PlanResponse>(`/plans/${planId}`);
    if (response.success && response.data && !Array.isArray(response.data)) {
      return response.data;
    }
    throw new Error(response.error || 'Failed to fetch plan');
  }

  async updatePlan(planId: PlanType, updates: Partial<Plan>): Promise<Plan> {
    const response = await this.request<PlanResponse>(`/plans/${planId}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
    
    if (response.success && response.data && !Array.isArray(response.data)) {
      return response.data;
    }
    throw new Error(response.error || 'Failed to update plan');
  }

  async updatePlanPrice(planId: PlanType, monthly: number, yearly: number): Promise<Plan> {
    return this.updatePlan(planId, {
      price: { monthly, yearly },
      updatedAt: new Date().toISOString()
    });
  }

  async togglePlanStatus(planId: PlanType, enabled: boolean): Promise<Plan> {
    return this.updatePlan(planId, {
      enabled,
      updatedAt: new Date().toISOString()
    });
  }

  async updatePlanFeatures(planId: PlanType, features: Partial<Plan['features']>): Promise<Plan> {
    const currentPlan = await this.getPlan(planId);
    return this.updatePlan(planId, {
      features: { ...currentPlan.features, ...features },
      updatedAt: new Date().toISOString()
    });
  }

  async bulkUpdatePlans(updates: PlanUpdateRequest[]): Promise<Plan[]> {
    const response = await this.request<PlanResponse>('/plans/bulk-update', {
      method: 'PUT',
      body: JSON.stringify({ updates }),
    });
    
    if (response.success && Array.isArray(response.data)) {
      return response.data;
    }
    throw new Error(response.error || 'Failed to bulk update plans');
  }

  async resetPlansToDefault(): Promise<Plan[]> {
    const response = await this.request<PlanResponse>('/plans/reset', {
      method: 'POST',
    });
    
    if (response.success && Array.isArray(response.data)) {
      return response.data;
    }
    throw new Error(response.error || 'Failed to reset plans');
  }

  async exportPlansConfig(): Promise<string> {
    const response = await this.request<{ config: string }>('/plans/export');
    return response.config;
  }

  async importPlansConfig(config: string): Promise<Plan[]> {
    const response = await this.request<PlanResponse>('/plans/import', {
      method: 'POST',
      body: JSON.stringify({ config }),
    });
    
    if (response.success && Array.isArray(response.data)) {
      return response.data;
    }
    throw new Error(response.error || 'Failed to import plans config');
  }
}

export const planService = new PlanService();
export default planService;