import { 
  collection, 
  doc, 
  setDoc, 
  getDoc, 
  getDocs, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy, 
  onSnapshot, 
  serverTimestamp,
  Timestamp,
  writeBatch
} from 'firebase/firestore';
import { firestoreDb as db } from './firebaseService';
import { OnlineUser, UserSession, User } from '../types';

const ONLINE_USERS_COLLECTION = 'online_users';
const USER_SESSIONS_COLLECTION = 'user_sessions';
const HEARTBEAT_INTERVAL = 30000; // 30 segundos
const OFFLINE_THRESHOLD = 60000; // 1 minuto

let heartbeatInterval: NodeJS.Timeout | null = null;

// Função para detectar informações do dispositivo/browser
const getDeviceInfo = () => {
  const userAgent = navigator.userAgent;
  let device = 'Desktop';
  let browser = 'Unknown';

  // Detectar dispositivo
  if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
    if (/iPad/.test(userAgent)) {
      device = 'Tablet';
    } else {
      device = 'Mobile';
    }
  }

  // Detectar browser
  if (userAgent.includes('Chrome')) browser = 'Chrome';
  else if (userAgent.includes('Firefox')) browser = 'Firefox';
  else if (userAgent.includes('Safari')) browser = 'Safari';
  else if (userAgent.includes('Edge')) browser = 'Edge';

  return { device, browser };
};

// Função para obter localização aproximada (simulada)
const getLocation = async (): Promise<string> => {
  try {
    // Em produção, você poderia usar uma API de geolocalização
    // Por enquanto, retornamos uma localização simulada
    return 'Brasil';
  } catch (error) {
    console.error('Erro ao obter localização:', error);
    return 'Localização desconhecida';
  }
};

// Iniciar sessão do usuário
export const startUserSession = async (user: User): Promise<void> => {
  if (!user || !user.id) {
    console.error('startUserSession: Usuário inválido', user);
    return;
  }

  try {
    const { device, browser } = getDeviceInfo();
    const location = await getLocation();
    const now = new Date().toISOString();
    
    const sessionData: UserSession = {
      id: `${user.id}_${Date.now()}`,
      userId: user.id,
      sessionStart: now,
      lastActivity: now,
      device,
      browser,
      location,
      currentPage: window.location.pathname,
      isActive: true
    };

    const onlineUserData: OnlineUser = {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      avatarUrl: user.avatarUrl,
      status: 'online',
      lastActivity: now,
      sessionStart: now,
      sessionDuration: 0,
      device,
      browser,
      location,
      currentPage: window.location.pathname,
      actionsToday: 0
    };

    // Salvar sessão
    await setDoc(doc(db, USER_SESSIONS_COLLECTION, sessionData.id), sessionData);
    
    // Salvar usuário online
    await setDoc(doc(db, ONLINE_USERS_COLLECTION, user.id), onlineUserData);
    console.log('✅ Usuário salvo na coleção online_users:', user.email, onlineUserData);

    // Iniciar heartbeat
    startHeartbeat(user.id);

    console.log('✅ Sessão do usuário iniciada:', user.email);
  } catch (error) {
    console.error('Erro ao iniciar sessão do usuário:', error);
    throw error;
  }
};

// Atualizar atividade do usuário
export const updateUserActivity = async (userId: string, currentPage?: string): Promise<void> => {
  try {
    const now = new Date().toISOString();
    
    const onlineUserRef = doc(db, ONLINE_USERS_COLLECTION, userId);
    const updateData: Partial<OnlineUser> = {
      lastActivity: now,
      status: 'online'
    };

    if (currentPage) {
      updateData.currentPage = currentPage;
    }

    await updateDoc(onlineUserRef, updateData);
  } catch (error) {
    console.error('Erro ao atualizar atividade do usuário:', error);
  }
};

// Finalizar sessão do usuário
export const endUserSession = async (userId: string): Promise<void> => {
  if (!userId) {
    console.error('endUserSession: userId inválido', userId);
    return;
  }

  try {
    // Remover usuário da lista de online
    await deleteDoc(doc(db, ONLINE_USERS_COLLECTION, userId));

    // Atualizar sessões ativas para inativas
    const sessionsQuery = query(
      collection(db, USER_SESSIONS_COLLECTION),
      where('userId', '==', userId),
      where('isActive', '==', true)
    );

    const sessionsSnapshot = await getDocs(sessionsQuery);
    const batch = writeBatch(db);

    sessionsSnapshot.forEach((sessionDoc) => {
      batch.update(sessionDoc.ref, { 
        isActive: false,
        lastActivity: new Date().toISOString()
      });
    });

    await batch.commit();

    // Parar heartbeat
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval);
      heartbeatInterval = null;
    }

    console.log('Sessão do usuário finalizada:', userId);
  } catch (error) {
    console.error('Erro ao finalizar sessão do usuário:', error);
  }
};

// Heartbeat para manter usuário online
const startHeartbeat = (userId: string) => {
  if (heartbeatInterval) {
    clearInterval(heartbeatInterval);
  }

  heartbeatInterval = setInterval(() => {
    updateUserActivity(userId);
  }, HEARTBEAT_INTERVAL);
};

// Obter todos os usuários online
export const getOnlineUsers = async (): Promise<OnlineUser[]> => {
  try {
    console.log('🔍 Buscando usuários online...');

    const onlineUsersQuery = query(
      collection(db, ONLINE_USERS_COLLECTION),
      orderBy('lastActivity', 'desc')
    );

    const snapshot = await getDocs(onlineUsersQuery);
    console.log('📊 Encontrados', snapshot.size, 'usuários online');
    const onlineUsers: OnlineUser[] = [];

    snapshot.forEach((doc) => {
      const userData = doc.data() as OnlineUser;
      
      // Calcular duração da sessão
      const sessionStart = new Date(userData.sessionStart);
      const now = new Date();
      const sessionDuration = Math.floor((now.getTime() - sessionStart.getTime()) / (1000 * 60));
      
      // Verificar se o usuário ainda está online (baseado na última atividade)
      const lastActivity = new Date(userData.lastActivity);
      const timeSinceLastActivity = now.getTime() - lastActivity.getTime();
      
      let status = userData.status;
      if (timeSinceLastActivity > OFFLINE_THRESHOLD) {
        status = 'offline';
      }

      onlineUsers.push({
        ...userData,
        sessionDuration,
        status
      });
    });

    return onlineUsers;
  } catch (error) {
    console.error('Erro ao obter usuários online:', error);
    throw error;
  }
};

// Listener em tempo real para usuários online
export const subscribeToOnlineUsers = (callback: (users: OnlineUser[]) => void) => {
  console.log('🔄 Configurando listener para usuários online...');

  const onlineUsersQuery = query(
    collection(db, ONLINE_USERS_COLLECTION),
    orderBy('lastActivity', 'desc')
  );

  return onSnapshot(onlineUsersQuery, (snapshot) => {
    console.log('📡 Snapshot recebido:', snapshot.size, 'documentos');
    const onlineUsers: OnlineUser[] = [];
    const now = new Date();

    snapshot.forEach((doc) => {
      const userData = doc.data() as OnlineUser;
      
      // Calcular duração da sessão
      const sessionStart = new Date(userData.sessionStart);
      const sessionDuration = Math.floor((now.getTime() - sessionStart.getTime()) / (1000 * 60));
      
      // Verificar se o usuário ainda está online
      const lastActivity = new Date(userData.lastActivity);
      const timeSinceLastActivity = now.getTime() - lastActivity.getTime();
      
      let status = userData.status;
      if (timeSinceLastActivity > OFFLINE_THRESHOLD) {
        status = 'offline';
      }

      onlineUsers.push({
        ...userData,
        sessionDuration,
        status
      });
    });

    callback(onlineUsers);
  });
};

// Incrementar contador de ações do usuário
export const incrementUserActions = async (userId: string): Promise<void> => {
  try {
    const onlineUserRef = doc(db, ONLINE_USERS_COLLECTION, userId);
    const userDoc = await getDoc(onlineUserRef);
    
    if (userDoc.exists()) {
      const currentActions = userDoc.data().actionsToday || 0;
      await updateDoc(onlineUserRef, {
        actionsToday: currentActions + 1,
        lastActivity: new Date().toISOString()
      });
    }
  } catch (error) {
    console.error('Erro ao incrementar ações do usuário:', error);
  }
};

// Limpar usuários offline antigos
export const cleanupOfflineUsers = async (): Promise<void> => {
  try {
    const onlineUsersSnapshot = await getDocs(collection(db, ONLINE_USERS_COLLECTION));
    const batch = writeBatch(db);
    const now = new Date();

    onlineUsersSnapshot.forEach((doc) => {
      const userData = doc.data() as OnlineUser;
      const lastActivity = new Date(userData.lastActivity);
      const timeSinceLastActivity = now.getTime() - lastActivity.getTime();

      // Remover usuários que estão offline há mais de 5 minutos
      if (timeSinceLastActivity > 5 * 60 * 1000) {
        batch.delete(doc.ref);
      }
    });

    await batch.commit();
  } catch (error) {
    console.error('Erro ao limpar usuários offline:', error);
  }
};
