
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Button } from '../components/ui/Button';
import { ROUTE_PATHS, ICONS } from '../constants';

const NotFoundPage: React.FC = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-[calc(100vh-150px)] text-center px-4">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1} stroke="currentColor" className="w-32 h-32 text-primary dark:text-primary-light mb-6">
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z" />
      </svg>

      <h1 className="text-5xl font-bold text-neutral-dark dark:text-neutral-light mb-4">Oops! Página não encontrada.</h1>
      <p className="text-lg text-gray-600 dark:text-neutral-DEFAULT mb-8">
        A página que você está procurando não existe ou foi movida.
      </p>
      <Button 
        onClick={() => window.history.back()}
        variant="outline"
        leftIcon={ICONS.ARROW_LEFT}
        className="mr-4"
      >
        Voltar
      </Button>
      <Link to={ROUTE_PATHS.DASHBOARD}>
        <Button variant="primary">
          Ir para o Painel
        </Button>
      </Link>
    </div>
  );
};

export default NotFoundPage;