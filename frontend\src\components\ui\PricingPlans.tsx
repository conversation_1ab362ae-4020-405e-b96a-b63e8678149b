import React, { useState } from 'react';
import { Plan, PlanType, PLANS } from '../../types/plans';

interface PricingPlansProps {
  currentPlan?: PlanType;
  onSelectPlan?: (planId: PlanType) => void;
}

export const PricingPlans: React.FC<PricingPlansProps> = ({
  currentPlan,
  onSelectPlan
}) => {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(price);
  };

  const getDiscountPercentage = (monthly: number, yearly: number) => {
    const monthlyTotal = monthly * 12;
    const discount = ((monthlyTotal - yearly) / monthlyTotal) * 100;
    return Math.round(discount);
  };

  const renderFeatureList = (plan: Plan) => {
    const features = [];

    // Recursos básicos
    features.push(
      plan.features.maxUsers === -1 
        ? 'Usuários ilimitados' 
        : `Até ${plan.features.maxUsers} usuários`
    );
    
    features.push(
      plan.features.maxDemands === -1 
        ? 'Demandas ilimitadas' 
        : `Até ${plan.features.maxDemands} demandas/mês`
    );
    
    features.push(
      plan.features.maxCitizens === -1 
        ? 'Cidadãos ilimitados' 
        : `Até ${plan.features.maxCitizens} cidadãos`
    );
    
    features.push(`${plan.features.storageGB}GB de armazenamento`);

    // Recursos de comunicação
    if (plan.features.bulkMessages) {
      features.push('Mensagens em massa');
    }
    
    if (plan.features.socialMediaIntegration) {
      features.push('Integração redes sociais');
    }
    
    if (plan.features.smsNotifications) {
      features.push('Notificações SMS');
    }

    // Recursos de relatórios
    if (plan.features.advancedReports) {
      features.push('Relatórios avançados');
    }
    
    if (plan.features.customReports) {
      features.push('Relatórios personalizados');
    }
    
    if (plan.features.dataExport) {
      features.push('Exportação de dados');
    }

    // Recursos de IA (destaque especial)
    if (plan.features.aiFeatures.length > 0) {
      features.push('🤖 Inteligência Artificial');
      plan.features.aiFeatures.slice(0, 3).forEach(aiFeature => {
        features.push(`  • ${aiFeature.name}`);
      });
      if (plan.features.aiFeatures.length > 3) {
        features.push(`  • +${plan.features.aiFeatures.length - 3} recursos de IA`);
      }
    }

    // Suporte
    const supportText = {
      basic: 'Suporte básico',
      priority: 'Suporte prioritário',
      dedicated: 'Suporte dedicado'
    };
    features.push(supportText[plan.features.supportLevel]);

    return features;
  };

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-12">
        <h2 className="text-3xl font-bold text-neutral-dark dark:text-neutral-light mb-4">
          Escolha o Plano Ideal para seu Mandato
        </h2>
        <p className="text-lg text-neutral-medium mb-8">
          Potencialize sua gestão política com nossas soluções inteligentes
        </p>
        
        {/* Billing Toggle */}
        <div className="inline-flex items-center bg-neutral-light dark:bg-neutral-darker rounded-lg p-1">
          <button
            type="button"
            onClick={() => setBillingCycle('monthly')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              billingCycle === 'monthly'
                ? 'bg-primary text-white'
                : 'text-neutral-medium hover:text-neutral-dark dark:hover:text-neutral-light'
            }`}
          >
            Mensal
          </button>
          <button
            type="button"
            onClick={() => setBillingCycle('yearly')}
            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              billingCycle === 'yearly'
                ? 'bg-primary text-white'
                : 'text-neutral-medium hover:text-neutral-dark dark:hover:text-neutral-light'
            }`}
          >
            Anual
            <span className="ml-1 text-xs bg-secondary text-neutral-dark px-2 py-1 rounded-full">
              -17%
            </span>
          </button>
        </div>
      </div>

      {/* Plans Grid */}
      <div className="grid md:grid-cols-3 gap-8">
        {PLANS.map((plan) => {
          const isCurrentPlan = currentPlan === plan.id;
          const isEnterprise = plan.id === PlanType.PROFESSIONAL;
          
          return (
            <div
              key={plan.id}
              className={`relative bg-white dark:bg-neutral-darker rounded-xl shadow-lg border-2 transition-all duration-300 hover:shadow-xl ${
                plan.popular 
                  ? 'border-primary scale-105' 
                  : isEnterprise
                  ? 'border-secondary'
                  : 'border-neutral-light dark:border-neutral-dark'
              } ${isCurrentPlan ? 'ring-2 ring-primary' : ''}`}
            >
              {/* Badge */}
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-primary text-white px-4 py-1 rounded-full text-sm font-medium">
                    Mais Popular
                  </span>
                </div>
              )}
              
              {plan.badge && (
                <div className="absolute -top-3 right-4">
                  <span className="bg-secondary text-neutral-dark px-3 py-1 rounded-full text-sm font-bold">
                    {plan.badge}
                  </span>
                </div>
              )}

              <div className="p-8">
                {/* Plan Header */}
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-neutral-dark dark:text-neutral-light mb-2">
                    {plan.name}
                  </h3>
                  <p className="text-neutral-medium mb-6">
                    {plan.description}
                  </p>
                  
                  {/* Price */}
                  <div className="mb-6">
                    <div className="flex items-baseline justify-center">
                      <span className="text-4xl font-bold text-neutral-dark dark:text-neutral-light">
                        {formatPrice(plan.price[billingCycle])}
                      </span>
                      <span className="text-neutral-medium ml-2">
                        /{billingCycle === 'monthly' ? 'mês' : 'ano'}
                      </span>
                    </div>
                    
                    {billingCycle === 'yearly' && (
                      <div className="text-sm text-neutral-medium mt-2">
                        {formatPrice(plan.price.monthly)}/mês se pago mensalmente
                        <br />
                        <span className="text-secondary font-medium">
                          Economize {getDiscountPercentage(plan.price.monthly, plan.price.yearly)}%
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Features */}
                <div className="mb-8">
                  <ul className="space-y-3">
                    {renderFeatureList(plan).map((feature, index) => (
                      <li key={index} className="flex items-start">
                        <svg
                          className="w-5 h-5 text-primary mt-0.5 mr-3 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                        <span className={`text-sm ${
                          feature.includes('•') 
                            ? 'text-neutral-medium ml-4' 
                            : 'text-neutral-dark dark:text-neutral-light'
                        }`}>
                          {feature}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* CTA Button */}
                <button
                  type="button"
                  onClick={() => onSelectPlan?.(plan.id)}
                  disabled={isCurrentPlan}
                  className={`w-full py-3 px-6 rounded-lg font-medium transition-colors ${
                    isCurrentPlan
                      ? 'bg-neutral-light text-neutral-medium cursor-not-allowed'
                      : isEnterprise
                      ? 'bg-secondary text-neutral-dark hover:bg-secondary-dark'
                      : plan.popular
                      ? 'bg-primary text-white hover:bg-primary-dark'
                      : 'bg-neutral-dark text-white hover:bg-neutral-darker dark:bg-neutral-light dark:text-neutral-dark dark:hover:bg-neutral-extralight'
                  }`}
                >
                  {isCurrentPlan ? 'Plano Atual' : `Escolher ${plan.name}`}
                </button>
              </div>
            </div>
          );
        })}
      </div>

      {/* AI Features Highlight */}
      <div className="mt-16 bg-gradient-to-r from-secondary-light to-secondary p-8 rounded-xl">
        <div className="text-center">
          <h3 className="text-2xl font-bold text-neutral-dark mb-4">
            🤖 Revolucione seu Mandato com Inteligência Artificial
          </h3>
          <p className="text-lg text-neutral-dark mb-6">
            O Plano Enterprise inclui recursos exclusivos de IA que transformam a gestão política
          </p>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4 mt-8">
            {PLANS.find(p => p.id === PlanType.PROFESSIONAL)?.features.aiFeatures.slice(0, 4).map((feature) => (
              <div key={feature.id} className="bg-white/20 backdrop-blur-sm rounded-lg p-4">
                <h4 className="font-semibold text-neutral-dark mb-2">
                  {feature.name}
                </h4>
                <p className="text-sm text-neutral-dark opacity-80">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PricingPlans;