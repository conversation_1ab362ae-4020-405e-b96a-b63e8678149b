import path from 'path';
import { fileURLToPath } from 'url';

// ES module equivalent of __dirname
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export default {
  // JWT Configuration
  jwt: {
    secret: process.env.JWT_SECRET || 'promandato_super_secret_key_2024',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d'
  },

  // Database Configuration (usando arquivo JSON como banco simples)
  database: {
    usersPath: path.join(__dirname, 'data/users.json'),
    organizationsPath: path.join(__dirname, 'data/organizations.json'),
    demandsPath: path.join(__dirname, 'data/demands.json'),
    citizensPath: path.join(__dirname, 'data/citizens.json'),
    plansPath: path.join(__dirname, 'data/plans.json'),
    sessionsPath: path.join(__dirname, 'data/sessions.json'),
    logsPath: path.join(__dirname, 'data/auth-logs.json'),
    notificationsPath: path.join(__dirname, 'data/notifications.json')
  },

  // Security Configuration
  security: {
    saltRounds: 12,
    maxLoginAttempts: 5,
    lockoutDuration: 15 * 60 * 1000, // 15 minutos
    sessionTimeout: 24 * 60 * 60 * 1000, // 24 horas
    passwordMinLength: 8,
    requireTwoFactor: false
  },

  // Rate Limiting
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutos
    maxRequests: process.env.NODE_ENV === 'production' ? 500 : 100, // mais permissivo em produção
    loginWindowMs: 15 * 60 * 1000, // 15 minutos
    maxLoginAttempts: process.env.NODE_ENV === 'production' ? 10 : 5 // mais tentativas em produção
  },

  // Default Admin User
  defaultAdmin: {
    email: '<EMAIL>',
    password: 'admin123', // Será hasheada na primeira execução
    name: 'Administrador',
    role: 'ADMIN',
    permissions: ['*'] // Todas as permissões
  },

  // Roles and Permissions
  roles: {
    ADMIN: {
      name: 'Administrador',
      permissions: ['*']
    },
    MANAGER: {
      name: 'Gerente',
      permissions: [
        'users.read',
        'users.create',
        'users.update',
        'plans.read',
        'plans.update',
        'settings.read',
        'settings.update',
        'reports.read',
        'notifications.create',
        'demands.read',
        'demands.update'
      ]
    },
    USER: {
      name: 'Usuário',
      permissions: [
        'profile.read',
        'profile.update',
        'plans.read',
        'demands.read'
      ]
    }
  },

  // Email Configuration (para futuras implementações)
  email: {
    from: process.env.EMAIL_FROM || '<EMAIL>',
    smtp: {
      host: process.env.SMTP_HOST,
      port: process.env.SMTP_PORT || 587,
      secure: false,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    }
  }
};